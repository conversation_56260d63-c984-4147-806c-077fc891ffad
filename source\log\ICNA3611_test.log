[22:06:53][INFO ]    │應用程式組件初始化完成
[22:06:53][INFO ]    │專案: ICNA3611, 測試項目: test
[22:06:53][INFO ]    │配置檔案兼容性檢查通過：配置檔案兼容且格式正確
[22:06:53][NOTE ]    ┌ 載入檔案 rgt/ICNA3611_test.rgt
[22:06:53][NOTE ]    │   成功載入檔案 rgt/ICNA3611_test.rgt，共 36 行
[22:06:53][NOTE ]    └ 完成：載入檔案 rgt/ICNA3611_test.rgt (0.22ms)
[22:06:53][NOTE ]    │   supported_commands:['set_pin', 'get_pin', 'set_title', 'set_regif', 'log', 'label', 'include']
[22:06:53][NOTE ]    ┌ 解析檔案格式: rgt/ICNA3611_test.rgt
[22:06:53][NOTE ]    │   找到 FOLDER 標籤: POWER
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 70000=68//RO
[22:06:53][NOTE ]    │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   地址: 70000
[22:06:53][NOTE ]    │   │   資料: 68
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 70000=68//RO (0.60ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 470000=68//RO//
[22:06:53][NOTE ]    │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   地址: 470000
[22:06:53][NOTE ]    │   │   資料: 68
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 470000=68//RO// (0.55ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 76000=12//RO
[22:06:53][NOTE ]    │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   地址: 76000
[22:06:53][NOTE ]    │   │   資料: 12
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 76000=12//RO (0.37ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 76004=1234//RO
[22:06:53][NOTE ]    │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   地址: 76004
[22:06:53][NOTE ]    │   │   資料: 1234
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 76004=1234//RO (0.40ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 76012=123456//RO//ALIGN(L)
[22:06:53][NOTE ]    │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   地址: 76012
[22:06:53][NOTE ]    │   │   資料: 123456
[22:06:53][NOTE ]    │   │   註釋: ALIGN(L)
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 76012=123456//RO//ALIGN(L) (0.35ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 47504=12XXXXXX//RO
[22:06:53][NOTE ]    │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   地址: 47504
[22:06:53][NOTE ]    │   │   資料: 12XXXXXX
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 47504=12XXXXXX//RO (0.30ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 76000=12XX//RO
[22:06:53][NOTE ]    │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   地址: 76000
[22:06:53][NOTE ]    │   │   資料: 12XX
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 76000=12XX//RO (0.38ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 47504=12XX34//RO
[22:06:53][NOTE ]    │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   地址: 47504
[22:06:53][NOTE ]    │   │   資料: 12XX34
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 47504=12XX34//RO (0.36ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 76012=XX34XX56//RO
[22:06:53][NOTE ]    │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   地址: 76012
[22:06:53][NOTE ]    │   │   資料: XX34XX56
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 76012=XX34XX56//RO (0.30ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 70000=68//WO
[22:06:53][NOTE ]    │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   地址: 70000
[22:06:53][NOTE ]    │   │   資料: 68
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 70000=68//WO (0.35ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 470000=68//WO
[22:06:53][NOTE ]    │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   地址: 470000
[22:06:53][NOTE ]    │   │   資料: 68
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 470000=68//WO (0.31ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 76000=12//WO
[22:06:53][NOTE ]    │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   地址: 76000
[22:06:53][NOTE ]    │   │   資料: 12
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 76000=12//WO (0.31ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 76000=13//WO
[22:06:53][NOTE ]    │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   地址: 76000
[22:06:53][NOTE ]    │   │   資料: 13
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 76000=13//WO (0.26ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 76000=14//WO
[22:06:53][NOTE ]    │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   地址: 76000
[22:06:53][NOTE ]    │   │   資料: 14
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 76000=14//WO (0.28ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 76004=1234//WO//ALIGN(L)
[22:06:53][NOTE ]    │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   地址: 76004
[22:06:53][NOTE ]    │   │   資料: 1234
[22:06:53][NOTE ]    │   │   註釋: ALIGN(L)
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 76004=1234//WO//ALIGN(L) (0.29ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 76012=123456//WO
[22:06:53][NOTE ]    │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   地址: 76012
[22:06:53][NOTE ]    │   │   資料: 123456
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 76012=123456//WO (0.34ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 47504=12345678//WO
[22:06:53][NOTE ]    │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   地址: 47504
[22:06:53][NOTE ]    │   │   資料: 12345678
[22:06:53][NOTE ]    │   │   註釋: 
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 47504=12345678//WO (0.27ms)
[22:06:53][NOTE ]    │   ┌ 解析暫存器命令: 76012=12345678//WO//TAG(3:REG_TMP0, 2:REG_A)
[22:06:53][NOTE ]    │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   地址: 76012
[22:06:53][NOTE ]    │   │   資料: 12345678
[22:06:53][NOTE ]    │   │   註釋: TAG(3:REG_TMP0, 2:REG_A)
[22:06:53][NOTE ]    │   └ 完成：解析暫存器命令: 76012=12345678//WO//TAG(3:REG_TMP0, 2:REG_A) (0.30ms)
[22:06:53][NOTE ]    │   解析完成: 總行數 36, 處理行數 26, 有效命令 25
[22:06:53][NOTE ]    └ 完成：解析檔案格式: rgt/ICNA3611_test.rgt (8.41ms)
[22:06:53][NOTE ]    ┌ Start output transferred file
[22:06:53][DEBUG]    │DBI 轉換引擎狀態已重置
[22:06:53][NOTE ]    │   Writing header section
[22:06:53][NOTE ]    │   Writing include section
[22:06:53][NOTE ]    │   ┌ 處理解析後的內容
[22:06:53][NOTE ]    │   │   ┌ 處理命令 1/25: comment
[22:06:53][NOTE ]    │   │   │   命令類型: comment
[22:06:53][NOTE ]    │   │   │   地址: None
[22:06:53][NOTE ]    │   │   │   資料: None
[22:06:53][NOTE ]    │   │   │   註釋: None
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 1/25: comment (0.29ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 2/25: comment
[22:06:53][NOTE ]    │   │   │   命令類型: comment
[22:06:53][NOTE ]    │   │   │   地址: None
[22:06:53][NOTE ]    │   │   │   資料: None
[22:06:53][NOTE ]    │   │   │   註釋: None
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 2/25: comment (0.31ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 3/25: RO
[22:06:53][NOTE ]    │   │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   │   地址: 070000
[22:06:53][NOTE ]    │   │   │   資料: 68
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 3/25: RO (0.31ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 4/25: RO
[22:06:53][NOTE ]    │   │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   │   地址: 470000
[22:06:53][NOTE ]    │   │   │   資料: 68
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 4/25: RO (0.33ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 5/25: RO
[22:06:53][NOTE ]    │   │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   │   地址: 076000
[22:06:53][NOTE ]    │   │   │   資料: 12
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 5/25: RO (0.29ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 6/25: RO
[22:06:53][NOTE ]    │   │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   │   地址: 076004
[22:06:53][NOTE ]    │   │   │   資料: 1234
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 6/25: RO (0.28ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 7/25: RO
[22:06:53][NOTE ]    │   │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   │   地址: 076012
[22:06:53][NOTE ]    │   │   │   資料: 123456
[22:06:53][NOTE ]    │   │   │   註釋: ALIGN(L)
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 7/25: RO (0.46ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 8/25: RO
[22:06:53][NOTE ]    │   │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   │   地址: 047504
[22:06:53][NOTE ]    │   │   │   資料: 12XXXXXX
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 8/25: RO (0.42ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 9/25: RO
[22:06:53][NOTE ]    │   │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   │   地址: 076000
[22:06:53][NOTE ]    │   │   │   資料: 12XX
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 9/25: RO (0.43ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 10/25: RO
[22:06:53][NOTE ]    │   │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   │   地址: 047504
[22:06:53][NOTE ]    │   │   │   資料: 12XX34
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 10/25: RO (0.30ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 11/25: RO
[22:06:53][NOTE ]    │   │   │   命令類型: RO
[22:06:53][NOTE ]    │   │   │   地址: 076012
[22:06:53][NOTE ]    │   │   │   資料: XX34XX56
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 11/25: RO (0.24ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 12/25: comment
[22:06:53][NOTE ]    │   │   │   命令類型: comment
[22:06:53][NOTE ]    │   │   │   地址: None
[22:06:53][NOTE ]    │   │   │   資料: None
[22:06:53][NOTE ]    │   │   │   註釋: None
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 12/25: comment (0.29ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 13/25: WO
[22:06:53][NOTE ]    │   │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   │   地址: 070000
[22:06:53][NOTE ]    │   │   │   資料: 68
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[22:06:53][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG8(0x70000)=0X68;
[22:06:53][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.15ms)
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 13/25: WO (0.55ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 14/25: WO
[22:06:53][NOTE ]    │   │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   │   地址: 470000
[22:06:53][NOTE ]    │   │   │   資料: 68
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[22:06:53][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG8(0x470000)=0X68;
[22:06:53][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.13ms)
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 14/25: WO (0.48ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 15/25: WO
[22:06:53][NOTE ]    │   │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   │   地址: 076000
[22:06:53][NOTE ]    │   │   │   資料: 12
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[22:06:53][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG8(0x76000)=0X12;
[22:06:53][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.11ms)
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 15/25: WO (0.44ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 16/25: WO
[22:06:53][NOTE ]    │   │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   │   地址: 076000
[22:06:53][NOTE ]    │   │   │   資料: 13
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[22:06:53][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG8(0x76000)=0X13;
[22:06:53][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.11ms)
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 16/25: WO (0.45ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 17/25: WO
[22:06:53][NOTE ]    │   │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   │   地址: 076000
[22:06:53][NOTE ]    │   │   │   資料: 14
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[22:06:53][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG8(0x76000)=0X14;
[22:06:53][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.10ms)
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 17/25: WO (0.41ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 18/25: WO
[22:06:53][NOTE ]    │   │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   │   地址: 076004
[22:06:53][NOTE ]    │   │   │   資料: 1234
[22:06:53][NOTE ]    │   │   │   註釋: ALIGN(L)
[22:06:53][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[22:06:53][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG16(0x76004)=0X1234;
[22:06:53][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.11ms)
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 18/25: WO (0.44ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 19/25: WO
[22:06:53][NOTE ]    │   │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   │   地址: 076012
[22:06:53][NOTE ]    │   │   │   資料: 123456
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[22:06:53][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG8(0x76012)=0X56;
[22:06:53][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG16(0x76013)=0X1234;
[22:06:53][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.19ms)
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 19/25: WO (0.52ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 20/25: WO
[22:06:53][NOTE ]    │   │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   │   地址: 047504
[22:06:53][NOTE ]    │   │   │   資料: 12345678
[22:06:53][NOTE ]    │   │   │   註釋: 
[22:06:53][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[22:06:53][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG32(0x47504)=0X12345678;
[22:06:53][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.12ms)
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 20/25: WO (0.53ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 21/25: comment
[22:06:53][NOTE ]    │   │   │   命令類型: comment
[22:06:53][NOTE ]    │   │   │   地址: None
[22:06:53][NOTE ]    │   │   │   資料: None
[22:06:53][NOTE ]    │   │   │   註釋: None
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 21/25: comment (0.25ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 22/25: WO
[22:06:53][NOTE ]    │   │   │   命令類型: WO
[22:06:53][NOTE ]    │   │   │   地址: 076012
[22:06:53][NOTE ]    │   │   │   資料: 12345678
[22:06:53][NOTE ]    │   │   │   註釋: TAG(3:REG_TMP0, 2:REG_A)
[22:06:53][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[22:06:53][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG32(0x76012)=0X12345678;
[22:06:53][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.12ms)
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 22/25: WO (0.48ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 23/25: comment
[22:06:53][NOTE ]    │   │   │   命令類型: comment
[22:06:53][NOTE ]    │   │   │   地址: None
[22:06:53][NOTE ]    │   │   │   資料: None
[22:06:53][NOTE ]    │   │   │   註釋: None
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 23/25: comment (0.25ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 24/25: comment
[22:06:53][NOTE ]    │   │   │   命令類型: comment
[22:06:53][NOTE ]    │   │   │   地址: None
[22:06:53][NOTE ]    │   │   │   資料: None
[22:06:53][NOTE ]    │   │   │   註釋: None
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 24/25: comment (0.27ms)
[22:06:53][NOTE ]    │   │   ┌ 處理命令 25/25: delay
[22:06:53][NOTE ]    │   │   │   命令類型: delay
[22:06:53][NOTE ]    │   │   │   地址: None
[22:06:53][NOTE ]    │   │   │   資料: None
[22:06:53][NOTE ]    │   │   │   註釋: None
[22:06:53][NOTE ]    │   │   └ 完成：處理命令 25/25: delay (0.23ms)
[22:06:53][NOTE ]    │   └ 完成：處理解析後的內容 (11.27ms)
[22:06:53][NOTE ]    └ 完成：Start output transferred file (11.64ms)
[22:06:53][NOTE ]    ┌ Run batch and copy
[22:06:54][NOTE ]    │   Copied and renamed: open_ddi_p_mode.asc -> ICNA3611_test.asc
[22:06:54][NOTE ]    │   Copied and renamed: open_ddi_p_mode.askv.vec -> ICNA3611_test.vec
[22:06:54][NOTE ]    │   Copied and renamed: open_ddi_p_mode.op2 -> ICNA3611_test.op2
[22:06:54][NOTE ]    │   Copied and renamed: open_ddi_p_mode.sv -> ICNA3611_test.sv
[22:06:54][NOTE ]    └ 完成：Run batch and copy (1638.56ms)
[22:06:54][INFO ]    │應用程式執行完成
