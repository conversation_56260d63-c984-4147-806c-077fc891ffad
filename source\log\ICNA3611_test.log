[21:53:03][INFO ]    │應用程式組件初始化完成
[21:53:03][INFO ]    │專案: ICNA3611, 測試項目: test
[21:53:03][INFO ]    │配置檔案兼容性檢查通過：配置檔案兼容且格式正確
[21:53:03][NOTE ]    ┌ 載入檔案 rgt/ICNA3611_test.rgt
[21:53:03][NOTE ]    │   成功載入檔案 rgt/ICNA3611_test.rgt，共 36 行
[21:53:03][NOTE ]    └ 完成：載入檔案 rgt/ICNA3611_test.rgt (0.22ms)
[21:53:03][NOTE ]    │   supported_commands:['set_pin', 'get_pin', 'set_title', 'set_regif', 'log', 'label', 'include']
[21:53:03][NOTE ]    ┌ 解析檔案格式: rgt/ICNA3611_test.rgt
[21:53:03][NOTE ]    │   找到 FOLDER 標籤: POWER
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 70000=68//RO
[21:53:03][NOTE ]    │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   地址: 70000
[21:53:03][NOTE ]    │   │   資料: 68
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 70000=68//RO (0.60ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 470000=68//RO//
[21:53:03][NOTE ]    │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   地址: 470000
[21:53:03][NOTE ]    │   │   資料: 68
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 470000=68//RO// (0.31ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 76000=12//RO
[21:53:03][NOTE ]    │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   地址: 76000
[21:53:03][NOTE ]    │   │   資料: 12
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 76000=12//RO (0.30ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 76004=1234//RO
[21:53:03][NOTE ]    │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   地址: 76004
[21:53:03][NOTE ]    │   │   資料: 1234
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 76004=1234//RO (0.29ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 76012=123456//RO//ALIGN(L)
[21:53:03][NOTE ]    │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   地址: 76012
[21:53:03][NOTE ]    │   │   資料: 123456
[21:53:03][NOTE ]    │   │   註釋: ALIGN(L)
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 76012=123456//RO//ALIGN(L) (0.31ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 47504=12XXXXXX//RO
[21:53:03][NOTE ]    │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   地址: 47504
[21:53:03][NOTE ]    │   │   資料: 12XXXXXX
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 47504=12XXXXXX//RO (0.28ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 76000=12XX//RO
[21:53:03][NOTE ]    │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   地址: 76000
[21:53:03][NOTE ]    │   │   資料: 12XX
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 76000=12XX//RO (0.36ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 47504=12XX34//RO
[21:53:03][NOTE ]    │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   地址: 47504
[21:53:03][NOTE ]    │   │   資料: 12XX34
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 47504=12XX34//RO (0.42ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 76012=XX34XX56//RO
[21:53:03][NOTE ]    │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   地址: 76012
[21:53:03][NOTE ]    │   │   資料: XX34XX56
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 76012=XX34XX56//RO (0.25ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 70000=68//WO
[21:53:03][NOTE ]    │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   地址: 70000
[21:53:03][NOTE ]    │   │   資料: 68
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 70000=68//WO (0.32ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 470000=68//WO
[21:53:03][NOTE ]    │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   地址: 470000
[21:53:03][NOTE ]    │   │   資料: 68
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 470000=68//WO (0.31ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 76000=12//WO
[21:53:03][NOTE ]    │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   地址: 76000
[21:53:03][NOTE ]    │   │   資料: 12
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 76000=12//WO (0.31ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 76000=13//WO
[21:53:03][NOTE ]    │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   地址: 76000
[21:53:03][NOTE ]    │   │   資料: 13
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 76000=13//WO (0.30ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 76000=14//WO
[21:53:03][NOTE ]    │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   地址: 76000
[21:53:03][NOTE ]    │   │   資料: 14
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 76000=14//WO (0.24ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 76004=1234//WO//ALIGN(L)
[21:53:03][NOTE ]    │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   地址: 76004
[21:53:03][NOTE ]    │   │   資料: 1234
[21:53:03][NOTE ]    │   │   註釋: ALIGN(L)
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 76004=1234//WO//ALIGN(L) (0.30ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 76012=123456//WO
[21:53:03][NOTE ]    │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   地址: 76012
[21:53:03][NOTE ]    │   │   資料: 123456
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 76012=123456//WO (0.25ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 47504=12345678//WO
[21:53:03][NOTE ]    │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   地址: 47504
[21:53:03][NOTE ]    │   │   資料: 12345678
[21:53:03][NOTE ]    │   │   註釋: 
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 47504=12345678//WO (0.28ms)
[21:53:03][NOTE ]    │   ┌ 解析暫存器命令: 76012=12345678//WO//TAG(3:REG_TMP0, 2:REG_A)
[21:53:03][NOTE ]    │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   地址: 76012
[21:53:03][NOTE ]    │   │   資料: 12345678
[21:53:03][NOTE ]    │   │   註釋: TAG(3:REG_TMP0, 2:REG_A)
[21:53:03][NOTE ]    │   └ 完成：解析暫存器命令: 76012=12345678//WO//TAG(3:REG_TMP0, 2:REG_A) (0.28ms)
[21:53:03][NOTE ]    │   解析完成: 總行數 36, 處理行數 26, 有效命令 25
[21:53:03][NOTE ]    └ 完成：解析檔案格式: rgt/ICNA3611_test.rgt (7.46ms)
[21:53:03][NOTE ]    ┌ Start output transferred file
[21:53:03][DEBUG]    │DBI 轉換引擎狀態已重置
[21:53:03][NOTE ]    │   Writing header section
[21:53:03][NOTE ]    │   Writing include section
[21:53:03][NOTE ]    │   ┌ 處理解析後的內容
[21:53:03][NOTE ]    │   │   ┌ 處理命令 1/25: comment
[21:53:03][NOTE ]    │   │   │   命令類型: comment
[21:53:03][NOTE ]    │   │   │   地址: None
[21:53:03][NOTE ]    │   │   │   資料: None
[21:53:03][NOTE ]    │   │   │   註釋: None
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 1/25: comment (0.36ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 2/25: comment
[21:53:03][NOTE ]    │   │   │   命令類型: comment
[21:53:03][NOTE ]    │   │   │   地址: None
[21:53:03][NOTE ]    │   │   │   資料: None
[21:53:03][NOTE ]    │   │   │   註釋: None
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 2/25: comment (0.27ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 3/25: RO
[21:53:03][NOTE ]    │   │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   │   地址: 070000
[21:53:03][NOTE ]    │   │   │   資料: 68
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 3/25: RO (0.33ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 4/25: RO
[21:53:03][NOTE ]    │   │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   │   地址: 470000
[21:53:03][NOTE ]    │   │   │   資料: 68
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 4/25: RO (0.29ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 5/25: RO
[21:53:03][NOTE ]    │   │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   │   地址: 076000
[21:53:03][NOTE ]    │   │   │   資料: 12
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 5/25: RO (0.33ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 6/25: RO
[21:53:03][NOTE ]    │   │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   │   地址: 076004
[21:53:03][NOTE ]    │   │   │   資料: 1234
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 6/25: RO (0.28ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 7/25: RO
[21:53:03][NOTE ]    │   │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   │   地址: 076012
[21:53:03][NOTE ]    │   │   │   資料: 123456
[21:53:03][NOTE ]    │   │   │   註釋: ALIGN(L)
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 7/25: RO (0.33ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 8/25: RO
[21:53:03][NOTE ]    │   │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   │   地址: 047504
[21:53:03][NOTE ]    │   │   │   資料: 12XXXXXX
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 8/25: RO (0.27ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 9/25: RO
[21:53:03][NOTE ]    │   │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   │   地址: 076000
[21:53:03][NOTE ]    │   │   │   資料: 12XX
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 9/25: RO (0.25ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 10/25: RO
[21:53:03][NOTE ]    │   │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   │   地址: 047504
[21:53:03][NOTE ]    │   │   │   資料: 12XX34
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 10/25: RO (0.29ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 11/25: RO
[21:53:03][NOTE ]    │   │   │   命令類型: RO
[21:53:03][NOTE ]    │   │   │   地址: 076012
[21:53:03][NOTE ]    │   │   │   資料: XX34XX56
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 11/25: RO (0.27ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 12/25: comment
[21:53:03][NOTE ]    │   │   │   命令類型: comment
[21:53:03][NOTE ]    │   │   │   地址: None
[21:53:03][NOTE ]    │   │   │   資料: None
[21:53:03][NOTE ]    │   │   │   註釋: None
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 12/25: comment (0.36ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 13/25: WO
[21:53:03][NOTE ]    │   │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   │   地址: 070000
[21:53:03][NOTE ]    │   │   │   資料: 68
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[21:53:03][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG8(0x70000)=0X68;
[21:53:03][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.25ms)
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 13/25: WO (0.90ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 14/25: WO
[21:53:03][NOTE ]    │   │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   │   地址: 470000
[21:53:03][NOTE ]    │   │   │   資料: 68
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[21:53:03][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG8(0x470000)=0X68;
[21:53:03][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.22ms)
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 14/25: WO (0.79ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 15/25: WO
[21:53:03][NOTE ]    │   │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   │   地址: 076000
[21:53:03][NOTE ]    │   │   │   資料: 12
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[21:53:03][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG8(0x76000)=0X12;
[21:53:03][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.20ms)
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 15/25: WO (0.73ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 16/25: WO
[21:53:03][NOTE ]    │   │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   │   地址: 076000
[21:53:03][NOTE ]    │   │   │   資料: 13
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[21:53:03][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG8(0x76000)=0X13;
[21:53:03][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.20ms)
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 16/25: WO (0.74ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 17/25: WO
[21:53:03][NOTE ]    │   │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   │   地址: 076000
[21:53:03][NOTE ]    │   │   │   資料: 14
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[21:53:03][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG8(0x76000)=0X14;
[21:53:03][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.21ms)
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 17/25: WO (0.66ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 18/25: WO
[21:53:03][NOTE ]    │   │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   │   地址: 076004
[21:53:03][NOTE ]    │   │   │   資料: 1234
[21:53:03][NOTE ]    │   │   │   註釋: ALIGN(L)
[21:53:03][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[21:53:03][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG16(0x76004)=0X1234;
[21:53:03][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.18ms)
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 18/25: WO (0.68ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 19/25: WO
[21:53:03][NOTE ]    │   │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   │   地址: 076012
[21:53:03][NOTE ]    │   │   │   資料: 123456
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[21:53:03][NOTE ]    │   │   │   │   資料長度: 3
[21:53:03][NOTE ]    │   │   │   │   地址偏移 1: 76012
[21:53:03][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG8(0x76012)=0X56;
[21:53:03][NOTE ]    │   │   │   │   地址偏移 2: 76013
[21:53:03][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG16(0x76013)=0X1234;
[21:53:03][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.55ms)
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 19/25: WO (1.13ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 20/25: WO
[21:53:03][NOTE ]    │   │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   │   地址: 047504
[21:53:03][NOTE ]    │   │   │   資料: 12345678
[21:53:03][NOTE ]    │   │   │   註釋: 
[21:53:03][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[21:53:03][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG32(0x47504)=0X12345678;
[21:53:03][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.23ms)
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 20/25: WO (0.79ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 21/25: comment
[21:53:03][NOTE ]    │   │   │   命令類型: comment
[21:53:03][NOTE ]    │   │   │   地址: None
[21:53:03][NOTE ]    │   │   │   資料: None
[21:53:03][NOTE ]    │   │   │   註釋: None
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 21/25: comment (0.31ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 22/25: WO
[21:53:03][NOTE ]    │   │   │   命令類型: WO
[21:53:03][NOTE ]    │   │   │   地址: 076012
[21:53:03][NOTE ]    │   │   │   資料: 12345678
[21:53:03][NOTE ]    │   │   │   註釋: TAG(3:REG_TMP0, 2:REG_A)
[21:53:03][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[21:53:03][INFO ]    │   │   │   │寫入 C 語言格式的暫存器操作：REG32(0x76012)=0X12345678;
[21:53:03][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.19ms)
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 22/25: WO (0.70ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 23/25: comment
[21:53:03][NOTE ]    │   │   │   命令類型: comment
[21:53:03][NOTE ]    │   │   │   地址: None
[21:53:03][NOTE ]    │   │   │   資料: None
[21:53:03][NOTE ]    │   │   │   註釋: None
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 23/25: comment (0.26ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 24/25: comment
[21:53:03][NOTE ]    │   │   │   命令類型: comment
[21:53:03][NOTE ]    │   │   │   地址: None
[21:53:03][NOTE ]    │   │   │   資料: None
[21:53:03][NOTE ]    │   │   │   註釋: None
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 24/25: comment (0.25ms)
[21:53:03][NOTE ]    │   │   ┌ 處理命令 25/25: delay
[21:53:03][NOTE ]    │   │   │   命令類型: delay
[21:53:03][NOTE ]    │   │   │   地址: None
[21:53:03][NOTE ]    │   │   │   資料: None
[21:53:03][NOTE ]    │   │   │   註釋: None
[21:53:03][NOTE ]    │   │   └ 完成：處理命令 25/25: delay (0.31ms)
[21:53:03][NOTE ]    │   └ 完成：處理解析後的內容 (13.92ms)
[21:53:03][NOTE ]    └ 完成：Start output transferred file (14.22ms)
[21:53:03][NOTE ]    ┌ Run batch and copy
[21:53:04][NOTE ]    │   Copied and renamed: open_ddi_p_mode.asc -> ICNA3611_test.asc
[21:53:04][NOTE ]    │   Copied and renamed: open_ddi_p_mode.askv.vec -> ICNA3611_test.vec
[21:53:04][NOTE ]    │   Copied and renamed: open_ddi_p_mode.op2 -> ICNA3611_test.op2
[21:53:04][NOTE ]    │   Copied and renamed: open_ddi_p_mode.sv -> ICNA3611_test.sv
[21:53:04][NOTE ]    └ 完成：Run batch and copy (1249.01ms)
[21:53:04][INFO ]    │應用程式執行完成
