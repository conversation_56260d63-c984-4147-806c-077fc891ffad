[19:17:08][INFO ]    │應用程式組件初始化完成
[19:17:08][INFO ]    │專案: ICNA3611, 測試項目: test
[19:17:08][INFO ]    │配置檔案兼容性檢查通過：配置檔案兼容且格式正確
[19:17:08][NOTE ]    ┌ 載入檔案 rgt/ICNA3611_test.rgt
[19:17:08][NOTE ]    │   成功載入檔案 rgt/ICNA3611_test.rgt，共 36 行
[19:17:08][NOTE ]    └ 完成：載入檔案 rgt/ICNA3611_test.rgt (0.14ms)
[19:17:08][NOTE ]    │   supported_commands:['set_pin', 'get_pin', 'set_title', 'set_regif', 'log', 'label', 'include']
[19:17:08][NOTE ]    ┌ 解析檔案格式: rgt/ICNA3611_test.rgt
[19:17:08][NOTE ]    │   找到 FOLDER 標籤: POWER
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 70000=68//RO
[19:17:08][NOTE ]    │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   地址: 70000
[19:17:08][NOTE ]    │   │   資料: 68
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 70000=68//RO (0.25ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 470000=68//RO//
[19:17:08][NOTE ]    │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   地址: 470000
[19:17:08][NOTE ]    │   │   資料: 68
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 470000=68//RO// (0.05ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 76000=12//RO
[19:17:08][NOTE ]    │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   地址: 76000
[19:17:08][NOTE ]    │   │   資料: 12
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 76000=12//RO (0.05ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 76004=1234//RO
[19:17:08][NOTE ]    │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   地址: 76004
[19:17:08][NOTE ]    │   │   資料: 1234
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 76004=1234//RO (0.04ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 76012=123456//RO//ALIGN(L)
[19:17:08][NOTE ]    │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   地址: 76012
[19:17:08][NOTE ]    │   │   資料: 123456
[19:17:08][NOTE ]    │   │   註釋: ALIGN(L)
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 76012=123456//RO//ALIGN(L) (0.04ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 47504=12XXXXXX//RO
[19:17:08][NOTE ]    │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   地址: 47504
[19:17:08][NOTE ]    │   │   資料: 12XXXXXX
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 47504=12XXXXXX//RO (0.04ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 76000=12XX//RO
[19:17:08][NOTE ]    │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   地址: 76000
[19:17:08][NOTE ]    │   │   資料: 12XX
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 76000=12XX//RO (0.04ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 47504=12XX34//RO
[19:17:08][NOTE ]    │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   地址: 47504
[19:17:08][NOTE ]    │   │   資料: 12XX34
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 47504=12XX34//RO (0.04ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 76012=XX34XX56//RO
[19:17:08][NOTE ]    │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   地址: 76012
[19:17:08][NOTE ]    │   │   資料: XX34XX56
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 76012=XX34XX56//RO (0.04ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 70000=68//WO
[19:17:08][NOTE ]    │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   地址: 70000
[19:17:08][NOTE ]    │   │   資料: 68
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 70000=68//WO (0.07ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 470000=68//WO
[19:17:08][NOTE ]    │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   地址: 470000
[19:17:08][NOTE ]    │   │   資料: 68
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 470000=68//WO (0.05ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 76000=12//WO
[19:17:08][NOTE ]    │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   地址: 76000
[19:17:08][NOTE ]    │   │   資料: 12
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 76000=12//WO (0.04ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 76000=13//WO
[19:17:08][NOTE ]    │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   地址: 76000
[19:17:08][NOTE ]    │   │   資料: 13
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 76000=13//WO (0.04ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 76000=14//WO
[19:17:08][NOTE ]    │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   地址: 76000
[19:17:08][NOTE ]    │   │   資料: 14
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 76000=14//WO (0.04ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 76004=1234//WO//ALIGN(L)
[19:17:08][NOTE ]    │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   地址: 76004
[19:17:08][NOTE ]    │   │   資料: 1234
[19:17:08][NOTE ]    │   │   註釋: ALIGN(L)
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 76004=1234//WO//ALIGN(L) (0.04ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 76012=123456//WO
[19:17:08][NOTE ]    │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   地址: 76012
[19:17:08][NOTE ]    │   │   資料: 123456
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 76012=123456//WO (0.05ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 47504=12345678//WO
[19:17:08][NOTE ]    │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   地址: 47504
[19:17:08][NOTE ]    │   │   資料: 12345678
[19:17:08][NOTE ]    │   │   註釋: 
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 47504=12345678//WO (0.04ms)
[19:17:08][NOTE ]    │   ┌ 解析暫存器命令: 76012=12345678//WO//TAG(3:REG_TMP0, 2:REG_A)
[19:17:08][NOTE ]    │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   地址: 76012
[19:17:08][NOTE ]    │   │   資料: 12345678
[19:17:08][NOTE ]    │   │   註釋: TAG(3:REG_TMP0, 2:REG_A)
[19:17:08][NOTE ]    │   └ 完成：解析暫存器命令: 76012=12345678//WO//TAG(3:REG_TMP0, 2:REG_A) (0.04ms)
[19:17:08][NOTE ]    │   解析完成: 總行數 36, 處理行數 26, 有效命令 25
[19:17:08][NOTE ]    └ 完成：解析檔案格式: rgt/ICNA3611_test.rgt (1.49ms)
[19:17:08][NOTE ]    ┌ Start output transferred file
[19:17:08][DEBUG]    │DBI 轉換引擎狀態已重置
[19:17:08][NOTE ]    │   Writing header section
[19:17:08][NOTE ]    │   Writing include section
[19:17:08][NOTE ]    │   ┌ 處理解析後的內容
[19:17:08][NOTE ]    │   │   ┌ 處理命令 1/25: comment
[19:17:08][NOTE ]    │   │   │   命令類型: comment
[19:17:08][NOTE ]    │   │   │   地址: None
[19:17:08][NOTE ]    │   │   │   資料: None
[19:17:08][NOTE ]    │   │   │   註釋: None
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 1/25: comment (0.05ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 2/25: comment
[19:17:08][NOTE ]    │   │   │   命令類型: comment
[19:17:08][NOTE ]    │   │   │   地址: None
[19:17:08][NOTE ]    │   │   │   資料: None
[19:17:08][NOTE ]    │   │   │   註釋: None
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 2/25: comment (0.04ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 3/25: RO
[19:17:08][NOTE ]    │   │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   │   地址: 070000
[19:17:08][NOTE ]    │   │   │   資料: 68
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 3/25: RO (0.05ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 4/25: RO
[19:17:08][NOTE ]    │   │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   │   地址: 470000
[19:17:08][NOTE ]    │   │   │   資料: 68
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 4/25: RO (0.05ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 5/25: RO
[19:17:08][NOTE ]    │   │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   │   地址: 076000
[19:17:08][NOTE ]    │   │   │   資料: 12
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 5/25: RO (0.05ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 6/25: RO
[19:17:08][NOTE ]    │   │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   │   地址: 076004
[19:17:08][NOTE ]    │   │   │   資料: 1234
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 6/25: RO (0.04ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 7/25: RO
[19:17:08][NOTE ]    │   │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   │   地址: 076012
[19:17:08][NOTE ]    │   │   │   資料: 123456
[19:17:08][NOTE ]    │   │   │   註釋: ALIGN(L)
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 7/25: RO (0.12ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 8/25: RO
[19:17:08][NOTE ]    │   │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   │   地址: 047504
[19:17:08][NOTE ]    │   │   │   資料: 12XXXXXX
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 8/25: RO (0.05ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 9/25: RO
[19:17:08][NOTE ]    │   │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   │   地址: 076000
[19:17:08][NOTE ]    │   │   │   資料: 12XX
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 9/25: RO (0.04ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 10/25: RO
[19:17:08][NOTE ]    │   │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   │   地址: 047504
[19:17:08][NOTE ]    │   │   │   資料: 12XX34
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 10/25: RO (0.04ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 11/25: RO
[19:17:08][NOTE ]    │   │   │   命令類型: RO
[19:17:08][NOTE ]    │   │   │   地址: 076012
[19:17:08][NOTE ]    │   │   │   資料: XX34XX56
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 11/25: RO (0.04ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 12/25: comment
[19:17:08][NOTE ]    │   │   │   命令類型: comment
[19:17:08][NOTE ]    │   │   │   地址: None
[19:17:08][NOTE ]    │   │   │   資料: None
[19:17:08][NOTE ]    │   │   │   註釋: None
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 12/25: comment (0.04ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 13/25: WO
[19:17:08][NOTE ]    │   │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   │   地址: 070000
[19:17:08][NOTE ]    │   │   │   資料: 68
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[19:17:08][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.01ms)
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 13/25: WO (0.07ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 14/25: WO
[19:17:08][NOTE ]    │   │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   │   地址: 470000
[19:17:08][NOTE ]    │   │   │   資料: 68
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[19:17:08][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.01ms)
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 14/25: WO (0.07ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 15/25: WO
[19:17:08][NOTE ]    │   │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   │   地址: 076000
[19:17:08][NOTE ]    │   │   │   資料: 12
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[19:17:08][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.01ms)
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 15/25: WO (0.07ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 16/25: WO
[19:17:08][NOTE ]    │   │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   │   地址: 076000
[19:17:08][NOTE ]    │   │   │   資料: 13
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[19:17:08][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.01ms)
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 16/25: WO (0.07ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 17/25: WO
[19:17:08][NOTE ]    │   │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   │   地址: 076000
[19:17:08][NOTE ]    │   │   │   資料: 14
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[19:17:08][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.01ms)
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 17/25: WO (0.08ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 18/25: WO
[19:17:08][NOTE ]    │   │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   │   地址: 076004
[19:17:08][NOTE ]    │   │   │   資料: 1234
[19:17:08][NOTE ]    │   │   │   註釋: ALIGN(L)
[19:17:08][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[19:17:08][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.01ms)
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 18/25: WO (0.07ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 19/25: WO
[19:17:08][NOTE ]    │   │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   │   地址: 076012
[19:17:08][NOTE ]    │   │   │   資料: 123456
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[19:17:08][DEBUG]    │   │   │   │資料長度 1: 3
[19:17:08][DEBUG]    │   │   │   │地址偏移 1: 76012
[19:17:08][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.03ms)
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 19/25: WO (0.08ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 20/25: WO
[19:17:08][NOTE ]    │   │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   │   地址: 047504
[19:17:08][NOTE ]    │   │   │   資料: 12345678
[19:17:08][NOTE ]    │   │   │   註釋: 
[19:17:08][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[19:17:08][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.01ms)
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 20/25: WO (0.06ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 21/25: comment
[19:17:08][NOTE ]    │   │   │   命令類型: comment
[19:17:08][NOTE ]    │   │   │   地址: None
[19:17:08][NOTE ]    │   │   │   資料: None
[19:17:08][NOTE ]    │   │   │   註釋: None
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 21/25: comment (0.06ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 22/25: WO
[19:17:08][NOTE ]    │   │   │   命令類型: WO
[19:17:08][NOTE ]    │   │   │   地址: 076012
[19:17:08][NOTE ]    │   │   │   資料: 12345678
[19:17:08][NOTE ]    │   │   │   註釋: TAG(3:REG_TMP0, 2:REG_A)
[19:17:08][NOTE ]    │   │   │   ┌ 生成 C 語言格式的寫入命令
[19:17:08][NOTE ]    │   │   │   └ 完成：生成 C 語言格式的寫入命令 (0.01ms)
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 22/25: WO (0.09ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 23/25: comment
[19:17:08][NOTE ]    │   │   │   命令類型: comment
[19:17:08][NOTE ]    │   │   │   地址: None
[19:17:08][NOTE ]    │   │   │   資料: None
[19:17:08][NOTE ]    │   │   │   註釋: None
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 23/25: comment (0.04ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 24/25: comment
[19:17:08][NOTE ]    │   │   │   命令類型: comment
[19:17:08][NOTE ]    │   │   │   地址: None
[19:17:08][NOTE ]    │   │   │   資料: None
[19:17:08][NOTE ]    │   │   │   註釋: None
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 24/25: comment (0.04ms)
[19:17:08][NOTE ]    │   │   ┌ 處理命令 25/25: delay
[19:17:08][NOTE ]    │   │   │   命令類型: delay
[19:17:08][NOTE ]    │   │   │   地址: None
[19:17:08][NOTE ]    │   │   │   資料: None
[19:17:08][NOTE ]    │   │   │   註釋: None
[19:17:08][NOTE ]    │   │   └ 完成：處理命令 25/25: delay (0.04ms)
[19:17:08][NOTE ]    │   └ 完成：處理解析後的內容 (1.90ms)
[19:17:08][NOTE ]    └ 完成：Start output transferred file (2.01ms)
[19:17:08][NOTE ]    ┌ Run batch and copy
[19:17:09][NOTE ]    │   Copied and renamed: open_ddi_p_mode.asc -> ICNA3611_test.asc
[19:17:09][NOTE ]    │   Copied and renamed: open_ddi_p_mode.askv.vec -> ICNA3611_test.vec
[19:17:09][NOTE ]    │   Copied and renamed: open_ddi_p_mode.op2 -> ICNA3611_test.op2
[19:17:09][NOTE ]    │   Copied and renamed: open_ddi_p_mode.sv -> ICNA3611_test.sv
[19:17:09][NOTE ]    └ 完成：Run batch and copy (1041.94ms)
[19:17:09][INFO ]    │應用程式執行完成
