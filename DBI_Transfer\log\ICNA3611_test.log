[05:17:36][INFO ]    │應用程式組件初始化完成
[05:17:36][INFO ]    │專案: ICNA3611, 測試項目: test
[05:17:36][INFO ]    │配置檔案兼容性檢查通過：配置檔案兼容且格式正確
[05:17:36][NOTE ]    ┌ 載入檔案 rgt/ICNA3611_test.rgt
[05:17:36][NOTE ]    │   成功載入檔案 rgt/ICNA3611_test.rgt，共 36 行
[05:17:36][NOTE ]    └ 完成：載入檔案 rgt/ICNA3611_test.rgt (0.23ms)
[05:17:36][NOTE ]    ┌ 解析檔案格式: rgt/ICNA3611_test.rgt
[05:17:36][NOTE ]    │   找到 FOLDER 標籤: POWER
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 70000=68//RO
[05:17:36][NOTE ]    │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   地址: 70000
[05:17:36][NOTE ]    │   │   資料: 68
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 70000=68//RO (0.50ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 470000=68//RO//
[05:17:36][NOTE ]    │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   地址: 470000
[05:17:36][NOTE ]    │   │   資料: 68
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 470000=68//RO// (0.27ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 76000=12//RO
[05:17:36][NOTE ]    │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   地址: 76000
[05:17:36][NOTE ]    │   │   資料: 12
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 76000=12//RO (0.18ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 76004=1234//RO
[05:17:36][NOTE ]    │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   地址: 76004
[05:17:36][NOTE ]    │   │   資料: 1234
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 76004=1234//RO (0.17ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 76012=123456//RO//ALIGN(L)
[05:17:36][NOTE ]    │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   地址: 76012
[05:17:36][NOTE ]    │   │   資料: 123456
[05:17:36][NOTE ]    │   │   註釋: ALIGN(L)
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 76012=123456//RO//ALIGN(L) (0.16ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 47504=12XXXXXX//RO
[05:17:36][NOTE ]    │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   地址: 47504
[05:17:36][NOTE ]    │   │   資料: 12XXXXXX
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 47504=12XXXXXX//RO (0.15ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 76000=12XX//RO
[05:17:36][NOTE ]    │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   地址: 76000
[05:17:36][NOTE ]    │   │   資料: 12XX
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 76000=12XX//RO (0.14ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 47504=12XX34//RO
[05:17:36][NOTE ]    │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   地址: 47504
[05:17:36][NOTE ]    │   │   資料: 12XX34
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 47504=12XX34//RO (0.14ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 76012=XX34XX56//RO
[05:17:36][NOTE ]    │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   地址: 76012
[05:17:36][NOTE ]    │   │   資料: XX34XX56
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 76012=XX34XX56//RO (0.14ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 70000=68//WO
[05:17:36][NOTE ]    │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   地址: 70000
[05:17:36][NOTE ]    │   │   資料: 68
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 70000=68//WO (0.14ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 470000=68//WO
[05:17:36][NOTE ]    │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   地址: 470000
[05:17:36][NOTE ]    │   │   資料: 68
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 470000=68//WO (0.18ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 76000=12//WO
[05:17:36][NOTE ]    │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   地址: 76000
[05:17:36][NOTE ]    │   │   資料: 12
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 76000=12//WO (0.14ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 76000=13//WO
[05:17:36][NOTE ]    │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   地址: 76000
[05:17:36][NOTE ]    │   │   資料: 13
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 76000=13//WO (0.14ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 76000=14//WO
[05:17:36][NOTE ]    │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   地址: 76000
[05:17:36][NOTE ]    │   │   資料: 14
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 76000=14//WO (0.14ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 76004=1234//WO//ALIGN(L)
[05:17:36][NOTE ]    │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   地址: 76004
[05:17:36][NOTE ]    │   │   資料: 1234
[05:17:36][NOTE ]    │   │   註釋: ALIGN(L)
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 76004=1234//WO//ALIGN(L) (0.14ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 76012=123456//WO
[05:17:36][NOTE ]    │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   地址: 76012
[05:17:36][NOTE ]    │   │   資料: 123456
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 76012=123456//WO (0.14ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 47504=12345678//WO
[05:17:36][NOTE ]    │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   地址: 47504
[05:17:36][NOTE ]    │   │   資料: 12345678
[05:17:36][NOTE ]    │   │   註釋: 
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 47504=12345678//WO (0.14ms)
[05:17:36][NOTE ]    │   ┌ 解析暫存器命令: 76012=12345678//WO//TAG(3:REG_TMP0, 2:REG_A)
[05:17:36][NOTE ]    │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   地址: 76012
[05:17:36][NOTE ]    │   │   資料: 12345678
[05:17:36][NOTE ]    │   │   註釋: TAG(3:REG_TMP0, 2:REG_A)
[05:17:36][NOTE ]    │   └ 完成：解析暫存器命令: 76012=12345678//WO//TAG(3:REG_TMP0, 2:REG_A) (0.15ms)
[05:17:36][NOTE ]    │   解析完成: 總行數 36, 處理行數 26, 有效命令 25
[05:17:36][NOTE ]    └ 完成：解析檔案格式: rgt/ICNA3611_test.rgt (4.18ms)
[05:17:36][NOTE ]    ┌ Start output transferred file
[05:17:36][DEBUG]    │DBI 轉換引擎狀態已重置
[05:17:36][NOTE ]    │   Writing header section
[05:17:36][NOTE ]    │   Writing include section
[05:17:36][NOTE ]    │   ┌ 處理解析後的內容
[05:17:36][NOTE ]    │   │   ┌ 處理命令 1/25: comment
[05:17:36][NOTE ]    │   │   │   命令類型: comment
[05:17:36][NOTE ]    │   │   │   地址: None
[05:17:36][NOTE ]    │   │   │   資料: None
[05:17:36][NOTE ]    │   │   │   註釋: None
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 1/25: comment (0.14ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 2/25: comment
[05:17:36][NOTE ]    │   │   │   命令類型: comment
[05:17:36][NOTE ]    │   │   │   地址: None
[05:17:36][NOTE ]    │   │   │   資料: None
[05:17:36][NOTE ]    │   │   │   註釋: None
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 2/25: comment (0.13ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 3/25: RO
[05:17:36][NOTE ]    │   │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   │   地址: 070000
[05:17:36][NOTE ]    │   │   │   資料: 68
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 3/25: RO (0.15ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 4/25: RO
[05:17:36][NOTE ]    │   │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   │   地址: 470000
[05:17:36][NOTE ]    │   │   │   資料: 68
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 4/25: RO (0.14ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 5/25: RO
[05:17:36][NOTE ]    │   │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   │   地址: 076000
[05:17:36][NOTE ]    │   │   │   資料: 12
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 5/25: RO (0.14ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 6/25: RO
[05:17:36][NOTE ]    │   │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   │   地址: 076004
[05:17:36][NOTE ]    │   │   │   資料: 1234
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 6/25: RO (0.14ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 7/25: RO
[05:17:36][NOTE ]    │   │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   │   地址: 076012
[05:17:36][NOTE ]    │   │   │   資料: 123456
[05:17:36][NOTE ]    │   │   │   註釋: ALIGN(L)
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 7/25: RO (0.21ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 8/25: RO
[05:17:36][NOTE ]    │   │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   │   地址: 047504
[05:17:36][NOTE ]    │   │   │   資料: 12XXXXXX
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 8/25: RO (0.15ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 9/25: RO
[05:17:36][NOTE ]    │   │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   │   地址: 076000
[05:17:36][NOTE ]    │   │   │   資料: 12XX
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 9/25: RO (0.14ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 10/25: RO
[05:17:36][NOTE ]    │   │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   │   地址: 047504
[05:17:36][NOTE ]    │   │   │   資料: 12XX34
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 10/25: RO (0.14ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 11/25: RO
[05:17:36][NOTE ]    │   │   │   命令類型: RO
[05:17:36][NOTE ]    │   │   │   地址: 076012
[05:17:36][NOTE ]    │   │   │   資料: XX34XX56
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 11/25: RO (0.13ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 12/25: comment
[05:17:36][NOTE ]    │   │   │   命令類型: comment
[05:17:36][NOTE ]    │   │   │   地址: None
[05:17:36][NOTE ]    │   │   │   資料: None
[05:17:36][NOTE ]    │   │   │   註釋: None
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 12/25: comment (0.13ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 13/25: WO
[05:17:36][NOTE ]    │   │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   │   地址: 070000
[05:17:36][NOTE ]    │   │   │   資料: 68
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 13/25: WO (0.13ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 14/25: WO
[05:17:36][NOTE ]    │   │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   │   地址: 470000
[05:17:36][NOTE ]    │   │   │   資料: 68
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 14/25: WO (0.13ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 15/25: WO
[05:17:36][NOTE ]    │   │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   │   地址: 076000
[05:17:36][NOTE ]    │   │   │   資料: 12
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 15/25: WO (0.15ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 16/25: WO
[05:17:36][NOTE ]    │   │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   │   地址: 076000
[05:17:36][NOTE ]    │   │   │   資料: 13
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 16/25: WO (0.13ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 17/25: WO
[05:17:36][NOTE ]    │   │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   │   地址: 076000
[05:17:36][NOTE ]    │   │   │   資料: 14
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 17/25: WO (0.13ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 18/25: WO
[05:17:36][NOTE ]    │   │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   │   地址: 076004
[05:17:36][NOTE ]    │   │   │   資料: 1234
[05:17:36][NOTE ]    │   │   │   註釋: ALIGN(L)
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 18/25: WO (0.13ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 19/25: WO
[05:17:36][NOTE ]    │   │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   │   地址: 076012
[05:17:36][NOTE ]    │   │   │   資料: 123456
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 19/25: WO (0.13ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 20/25: WO
[05:17:36][NOTE ]    │   │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   │   地址: 047504
[05:17:36][NOTE ]    │   │   │   資料: 12345678
[05:17:36][NOTE ]    │   │   │   註釋: 
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 20/25: WO (0.14ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 21/25: comment
[05:17:36][NOTE ]    │   │   │   命令類型: comment
[05:17:36][NOTE ]    │   │   │   地址: None
[05:17:36][NOTE ]    │   │   │   資料: None
[05:17:36][NOTE ]    │   │   │   註釋: None
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 21/25: comment (0.19ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 22/25: WO
[05:17:36][NOTE ]    │   │   │   命令類型: WO
[05:17:36][NOTE ]    │   │   │   地址: 076012
[05:17:36][NOTE ]    │   │   │   資料: 12345678
[05:17:36][NOTE ]    │   │   │   註釋: TAG(3:REG_TMP0, 2:REG_A)
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 22/25: WO (0.15ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 23/25: comment
[05:17:36][NOTE ]    │   │   │   命令類型: comment
[05:17:36][NOTE ]    │   │   │   地址: None
[05:17:36][NOTE ]    │   │   │   資料: None
[05:17:36][NOTE ]    │   │   │   註釋: None
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 23/25: comment (0.14ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 24/25: comment
[05:17:36][NOTE ]    │   │   │   命令類型: comment
[05:17:36][NOTE ]    │   │   │   地址: None
[05:17:36][NOTE ]    │   │   │   資料: None
[05:17:36][NOTE ]    │   │   │   註釋: None
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 24/25: comment (0.13ms)
[05:17:36][NOTE ]    │   │   ┌ 處理命令 25/25: delay
[05:17:36][NOTE ]    │   │   │   命令類型: delay
[05:17:36][NOTE ]    │   │   │   地址: None
[05:17:36][NOTE ]    │   │   │   資料: None
[05:17:36][NOTE ]    │   │   │   註釋: None
[05:17:36][NOTE ]    │   │   └ 完成：處理命令 25/25: delay (0.13ms)
[05:17:36][NOTE ]    │   └ 完成：處理解析後的內容 (4.57ms)
[05:17:36][NOTE ]    └ 完成：Start output transferred file (4.80ms)
[05:17:36][NOTE ]    ┌ Run batch and copy
[05:17:37][NOTE ]    │   Copied and renamed: open_ddi_p_mode.asc -> ICNA3611_test.asc
[05:17:37][NOTE ]    │   Copied and renamed: open_ddi_p_mode.askv.vec -> ICNA3611_test.vec
[05:17:37][NOTE ]    │   Copied and renamed: open_ddi_p_mode.op2 -> ICNA3611_test.op2
[05:17:37][NOTE ]    │   Copied and renamed: open_ddi_p_mode.sv -> ICNA3611_test.sv
[05:17:37][NOTE ]    └ 完成：Run batch and copy (1069.35ms)
[05:17:37][INFO ]    │應用程式執行完成
