#!/bin/bash
# DBI Transfer Tool 構建腳本 (Linux/macOS)

set -e  # 遇到錯誤立即退出

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函數
print_header() {
    echo -e "${BLUE}=================================================="
    echo -e "    DBI Transfer Tool 構建腳本 v2.0"
    echo -e "==================================================${NC}"
    echo -e "🖥️  平台: $(uname -s) $(uname -r)"
    echo -e "🐍 Python: $(python3 --version 2>/dev/null || echo 'Not found')"
    echo
}

print_step() {
    echo -e "${BLUE}[$1] $2${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 檢查 Python
check_python() {
    print_step "1/7" "檢查 Python 環境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安裝"
        echo "請安裝 Python 3.8+ 並確保在 PATH 中"
        exit 1
    fi
    
    # 檢查 Python 版本
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    if python3 -c "import sys; sys.exit(0 if sys.version_info >= (3, 8) else 1)"; then
        print_success "Python $python_version 符合要求"
    else
        print_error "需要 Python 3.8 或更高版本，當前版本: $python_version"
        exit 1
    fi
}

# 檢查依賴項
check_dependencies() {
    print_step "2/7" "檢查依賴項..."
    
    # 檢查 PyInstaller
    if python3 -c "import PyInstaller" 2>/dev/null; then
        pyinstaller_version=$(python3 -c "import PyInstaller; print(PyInstaller.__version__)")
        print_success "PyInstaller $pyinstaller_version 已安裝"
    else
        print_error "PyInstaller 未安裝"
        echo "正在安裝 PyInstaller..."
        if python3 -m pip install pyinstaller; then
            print_success "PyInstaller 安裝成功"
        else
            print_error "PyInstaller 安裝失敗"
            exit 1
        fi
    fi
}

# 驗證構建環境
validate_environment() {
    print_step "3/7" "驗證構建環境..."
    
    if python3 build_config.py; then
        print_success "構建環境驗證通過"
    else
        print_error "構建環境驗證失敗"
        exit 1
    fi
}

# 清理構建文件
clean_build_files() {
    print_step "4/7" "清理舊的構建文件..."
    
    # 清理目錄和文件
    rm -rf build/ 2>/dev/null || true
    rm -f DBI_Transfer.spec 2>/dev/null || true
    rm -f DBI_Transfer/DBI_Transfer 2>/dev/null || true
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    
    print_success "清理完成"
}

# 運行 PyInstaller
run_pyinstaller() {
    print_step "5/7" "執行 PyInstaller 構建..."
    echo "這可能需要幾分鐘時間，請耐心等待..."
    echo
    
    if python3 -c "
import subprocess
import sys
from build_config import get_pyinstaller_args

args = ['pyinstaller'] + get_pyinstaller_args()
print('執行命令:', ' '.join(args))
print()

result = subprocess.run(args, capture_output=False)
sys.exit(result.returncode)
"; then
        print_success "PyInstaller 構建成功"
    else
        print_error "PyInstaller 構建失敗"
        exit 1
    fi
}

# 驗證構建結果
verify_build() {
    print_step "6/7" "驗證構建結果..."
    
    exe_path="DBI_Transfer/DBI_Transfer"
    
    if [ ! -f "$exe_path" ]; then
        print_error "可執行檔不存在: $exe_path"
        exit 1
    fi
    
    # 檢查文件大小
    file_size=$(stat -f%z "$exe_path" 2>/dev/null || stat -c%s "$exe_path" 2>/dev/null)
    file_size_mb=$(echo "scale=1; $file_size / 1024 / 1024" | bc -l 2>/dev/null || echo "N/A")
    
    print_success "可執行檔生成成功"
    echo "   📁 位置: $exe_path"
    echo "   📏 大小: $file_size bytes ($file_size_mb MB)"
    
    # 測試可執行檔
    echo "   🧪 測試可執行檔..."
    if timeout 10s "$exe_path" --version >/dev/null 2>&1; then
        echo "   ✅ 可執行檔運行正常"
    else
        echo "   ⚠️  無法測試可執行檔，請手動驗證"
    fi
}

# 顯示構建摘要
show_summary() {
    print_step "7/7" "構建摘要"
    echo
    echo -e "${GREEN}🎉 構建完成！${NC}"
    echo "=================================================="
    echo
    echo "📋 使用說明:"
    echo "   1. 可執行檔位於: DBI_Transfer/DBI_Transfer"
    echo "   2. 測試命令: ./DBI_Transfer/DBI_Transfer --help"
    echo "   3. 運行命令: ./DBI_Transfer/DBI_Transfer [test_item]"
    echo
    echo "📦 發布包內容:"
    if [ -d "DBI_Transfer" ]; then
        ls -la DBI_Transfer/ | while read line; do
            echo "   $line"
        done
    fi
    echo
}

# 主函數
main() {
    print_header
    
    check_python
    echo
    
    check_dependencies
    echo
    
    validate_environment
    echo
    
    clean_build_files
    echo
    
    run_pyinstaller
    echo
    
    verify_build
    echo
    
    show_summary
}

# 錯誤處理
trap 'print_error "構建過程中發生錯誤"; exit 1' ERR

# 執行主函數
main
