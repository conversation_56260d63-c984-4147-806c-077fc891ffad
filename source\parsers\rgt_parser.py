"""
RGT 檔案解析器

這個模組負責解析 RGT (Register Test) 檔案，將文本格式的暫存器操作
轉換為結構化的資料格式，支援：

- 註釋行解析 (// 開頭)
- 命令行解析 (# 開頭)
- 延遲命令解析 (delay 開頭)
- 暫存器操作解析 (包含 = 的行)
- FOLDER 標籤提取
- 資料格式驗證
"""
import re
import sys
import os
from typing import Optional, List, Tuple

# 添加父目錄到路徑以支援直接執行
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from constant import FieldIndex, DataLength, RegexPatterns, CommandTypes
from validation.error_handler import create_error_handler, ErrorHandler
from exceptions import DataFormatError
from type_definitions import Loggable


class RgtParser:
    """
    RGT 檔案解析器

    負責解析 RGT (Register Test) 檔案，將文本格式的暫存器操作
    轉換為結構化的資料格式，便於後續處理
    """

    def __init__(self, logger: Loggable) -> None:
        """
        初始化 RGT 解析器

        Args:
            logger: 日誌記錄器，用於記錄解析過程和錯誤
        """
        self.logger: Loggable = logger
        self.error_handler: ErrorHandler = create_error_handler(logger)

    def is_valid_data_pattern(self, data: str) -> bool:
        """
        驗證十六進制資料格式是否正確

        檢查資料是否符合以下規範：
        - 長度必須是有效長度（2, 4, 6, 8 字符）
        - 內容必須是十六進制字符或 XX 模式

        Args:
            data: 要檢查的資料字串

        Returns:
            True 如果格式有效，否則 False

        Example:
            >>> parser.is_valid_data_pattern("ABCD")
            True
            >>> parser.is_valid_data_pattern("XXXX")
            True
            >>> parser.is_valid_data_pattern("ABC")
            False
        """
        # 檢查長度是否有效
        if len(data) not in DataLength.VALID_LENGTHS:
            return False

        # 檢查內容是否符合十六進制或 XX 模式
        return bool(re.fullmatch(RegexPatterns.HEX_OR_XX, data))

    def get_label_value(self, text: Optional[str], label: str) -> Optional[str]:
        """
        從字串中提取標籤值

        從形如 "LABEL(value)" 的字串中提取 value 部分

        Args:
            text: 要搜尋的字串，可能為 None
            label: 標籤名稱，如 "FOLDER", "TAG", "ALIGN" 等

        Returns:
            標籤值，如果未找到則返回 None

        Example:
            >>> parser._extract_label_value("FOLDER(test_folder)", "FOLDER")
            'test_folder'
            >>> parser._extract_label_value("no folder", "FOLDER")
            None
        """
        if not text:
            return None

        pattern = rf"{label}\(([^)]+)\)"
        match = re.search(pattern, text)
        return match.group(1) if match else None
    
    def load_file_content(self, filename: str, project_name: str) -> List[str]:
        """
        載入 RGT 檔案內容

        Args:
            filename: 檔案路徑
            project_name: 專案名稱（用於錯誤報告）

        Returns:
            檔案內容行列表

        Raises:
            FileNotFoundError: 當檔案不存在時
        """
        with self.logger.section(f"載入檔案 {filename}"):
            try:
                with open(filename, "r", encoding="utf-8") as file:
                    content = file.readlines()

                self.logger.step(f"成功載入檔案 {filename}，共 {len(content)} 行")
                return content

            except FileNotFoundError:
                error_msg = f"找不到檔案: {filename}"
                self.logger.log(error_msg, level="ERROR")
                self.error_handler.handle_file_not_found(filename, project_name)
                raise
            except UnicodeDecodeError as e:
                error_msg = f"檔案編碼錯誤: {filename} - {e}"
                self.logger.log(error_msg, level="ERROR")
                raise
            except Exception as e:
                error_msg = f"載入檔案時發生未知錯誤: {filename} - {e}"
                self.logger.log(error_msg, level="ERROR")
                raise
    
    def parse_comment_line(self, line_content: str) -> List[Optional[str]]:
        """
        解析註釋行（以 // 開頭的行）

        Args:
            line_content: 註釋行內容

        Returns:
            解析結果：[命令類型, None, None, None, 完整內容]
        """
        parsed_data = [None] * 5
        parsed_data[FieldIndex.COMMAND_IDX] = CommandTypes.COMMENT
        parsed_data[FieldIndex.FULL_DAT] = line_content
        return parsed_data

    def parse_command_line(
        self,
        line_content: str,
        supported_commands: List[str],
        line_number: int
    ) -> List[Optional[str]]:
        """
        解析命令行（以 # 開頭的行）

        Args:
            line_content: 命令行內容
            supported_commands: 支援的命令列表
            line_number: 行號（用於錯誤報告）

        Returns:
            解析結果：[命令類型, None, None, None, 完整內容]
        """
        parsed_data = [None] * 5

        # 檢查命令格式和支援性
        self.logger.step(f"解析命令行: {line_content}")
        self.logger.step(f"RegexPattern:{RegexPatterns.COMMAND_PATTERN}")
        match = re.match(RegexPatterns.COMMAND_PATTERN, line_content)
        if not match or match.group(1) not in supported_commands:
            self.error_handler.handle_command_not_supported(line_number, line_content)

        parsed_data[FieldIndex.COMMAND_IDX] = CommandTypes.CMD
        parsed_data[FieldIndex.FULL_DAT] = line_content
        return parsed_data

    def parse_delay_line(self, line_content: str, line_number: int) -> List[Optional[str]]:
        """
        解析延遲行（delay 命令）

        Args:
            line_content: 延遲命令行
            line_number: 行號（用於錯誤報告）

        Returns:
            解析結果：[命令類型, None, None, None, 完整內容]
        """
        parsed_data = [None] * 5

        # 驗證延遲命令格式
        if not re.fullmatch(RegexPatterns.DELAY_PATTERN, line_content):
            self.error_handler.handle_format_error(
                line_number, line_content, "Invalid delay format"
            )

        parsed_data[FieldIndex.COMMAND_IDX] = CommandTypes.DELAY
        parsed_data[FieldIndex.FULL_DAT] = line_content
        return parsed_data
    
    def parse_register_command(self, line_content: str, line_number: int) -> List[Optional[str]]:
        """
        解析暫存器命令（包含 = 的行）

        解析形如 "12345=ABCD//WO//Write operation" 的暫存器操作命令

        Args:
            line_content: 暫存器命令行內容
            line_number: 行號（用於錯誤報告）

        Returns:
            解析結果：[命令類型, 地址, 資料, 註釋, 完整內容]
        """
        with self.logger.section(f"解析暫存器命令: {line_content}"):
            # 使用正則表達式解析命令格式
            pattern = re.compile(RegexPatterns.REGISTER_PATTERN, re.VERBOSE)
            match = pattern.match(line_content)

            if not match:
                self.error_handler.handle_format_error(
                    line_number, line_content, "Invalid register command format"
                )
                return [None] * 5

            # 提取各個組件
            command_type = match.group("cmd") or ""
            address = match.group("addr")
            data = match.group("data")
            comment = match.group("comment") or ""

            # 記錄解析結果
            self.logger.step(f"命令類型: {command_type}")
            self.logger.step(f"地址: {address}")
            self.logger.step(f"資料: {data}")
            self.logger.step(f"註釋: {comment}")

            # 驗證資料格式
            if not self.is_valid_data_pattern(data):
                error = DataFormatError(
                    f"Invalid data format: {data}",
                    data=data,
                    expected_format='Hex pairs (AB, ABCD, ABCDEF, ABCDEF12) or XX patterns',
                    context={
                        'line_number': line_number,
                        'line_content': line_content
                    }
                )
                self.error_handler.handle_error_with_context(error, fatal=False)

            # 標準化地址格式（確保6位數）
            normalized_address = address if len(address) == 6 else address.zfill(6)

            # 構建解析結果
            parsed_data = [None] * 5
            parsed_data[FieldIndex.COMMAND_IDX] = command_type
            parsed_data[FieldIndex.ADDR_IDX] = normalized_address
            parsed_data[FieldIndex.DATA_IDX] = data
            parsed_data[FieldIndex.COMMENT_IDX] = comment
            parsed_data[FieldIndex.FULL_DAT] = line_content

            return parsed_data
    
    def parse_file(
        self,
        filename: str,
        supported_commands: List[str],
        project_name: str
    ) -> Tuple[List[str], List[List[Optional[str]]], str]:
        """
        解析 RGT 檔案並返回結構化資料

        Args:
            filename: RGT 檔案路徑
            supported_commands: 支援的命令列表
            project_name: 專案名稱

        Returns:
            (原始內容, 解析後內容, FOLDER名稱) 的元組
        """
        # 載入檔案內容
        raw_content = self.load_file_content(filename, project_name)
        parsed_content: List[List[Optional[str]]] = []
        folder_name = ""
        self.logger.step(f"supported_commands:{supported_commands}")
        with self.logger.section(f"解析檔案格式: {filename}"):
            total_lines = len(raw_content)
            processed_lines = 0

            for line_index, line in enumerate(raw_content):
                line_content = line.strip()

                # 跳過空行
                if not line_content:
                    continue

                line_number = line_index + 1
                processed_lines += 1

                # 根據行的開頭字符分類處理
                try:
                    if line_content.startswith("//"):
                        # 註釋行
                        parsed_content.append(self.parse_comment_line(line_content))

                    elif line_content.startswith("FOLDER"):
                        # FOLDER 標籤行
                        folder_name = self.get_label_value(line_content, "FOLDER") or ""
                        self.logger.step(f"找到 FOLDER 標籤: {folder_name}")

                    elif line_content.startswith("#"):
                        # 命令行
                        parsed_content.append(
                            self.parse_command_line(line_content, supported_commands, line_number)
                        )

                    elif line_content.startswith("delay"):
                        # 延遲命令行
                        parsed_content.append(self.parse_delay_line(line_content, line_number))

                    elif "=" in line_content:
                        # 暫存器操作行
                        parsed_content.append(self.parse_register_command(line_content, line_number))

                    else:
                        # 未知格式
                        self.error_handler.handle_format_error(
                            line_number, line_content, "Unknown line format"
                        )

                except Exception as e:
                    # 處理解析過程中的異常
                    self.logger.log(
                        f"解析第 {line_number} 行時發生錯誤: {e}",
                        level="ERROR"
                    )
                    continue

            # 記錄解析統計
            self.logger.step(f"解析完成: 總行數 {total_lines}, 處理行數 {processed_lines}, 有效命令 {len(parsed_content)}")

        return raw_content, parsed_content, folder_name
