#!/usr/bin/env python3
"""
DBI Transfer Tool 構建腳本 (Python 版本)
支持 Windows, Linux, macOS
"""
import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path
from build_config import BUILD_CONFIG, get_pyinstaller_args, validate_build_environment

def print_header():
    """打印標題"""
    print("=" * 50)
    print("    DBI Transfer Tool 構建腳本 v2.0")
    print("=" * 50)
    print(f"🖥️  平台: {platform.system()} {platform.release()}")
    print(f"🐍 Python: {sys.version.split()[0]}")
    print()

def check_python_version():
    """檢查 Python 版本"""
    print("[1/7] 檢查 Python 版本...")
    if sys.version_info < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        print(f"   當前版本: {sys.version}")
        return False
    print(f"✅ Python {sys.version.split()[0]} 符合要求")
    return True

def check_dependencies():
    """檢查依賴項"""
    print("[2/7] 檢查依賴項...")
    
    # 檢查 PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller {PyInstaller.__version__} 已安裝")
    except ImportError:
        print("❌ PyInstaller 未安裝")
        print("正在安裝 PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                         check=True, capture_output=True)
            print("✅ PyInstaller 安裝成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ PyInstaller 安裝失敗: {e}")
            return False
    
    return True

def validate_environment():
    """驗證構建環境"""
    print("[3/7] 驗證構建環境...")
    
    errors = validate_build_environment()
    if errors:
        print("❌ 構建環境驗證失敗:")
        for error in errors:
            print(f"   - {error}")
        return False
    
    print("✅ 構建環境驗證通過")
    return True

def clean_build_files():
    """清理構建文件"""
    print("[4/7] 清理舊的構建文件...")
    
    # 要清理的目錄和文件
    clean_targets = [
        BUILD_CONFIG["build_folder"],
        f"{BUILD_CONFIG['dist_name']}.spec",
        "__pycache__",
    ]
    
    # 清理可執行檔
    exe_name = BUILD_CONFIG["dist_name"]
    if platform.system() == "Windows":
        exe_name += ".exe"
    
    exe_path = Path(BUILD_CONFIG["dist_folder"]) / exe_name
    if exe_path.exists():
        clean_targets.append(str(exe_path))
    
    for target in clean_targets:
        target_path = Path(target)
        if target_path.exists():
            if target_path.is_dir():
                shutil.rmtree(target_path, ignore_errors=True)
                print(f"   🗑️  已刪除目錄: {target}")
            else:
                target_path.unlink()
                print(f"   🗑️  已刪除文件: {target}")
    
    print("✅ 清理完成")

def run_pyinstaller():
    """運行 PyInstaller"""
    print("[5/7] 執行 PyInstaller 構建...")
    print("這可能需要幾分鐘時間，請耐心等待...")
    print()
    
    # 獲取 PyInstaller 參數
    args = ["pyinstaller"] + get_pyinstaller_args()
    
    print("執行命令:")
    print(" ".join(args))
    print()
    
    try:
        # 運行 PyInstaller
        result = subprocess.run(args, check=True, capture_output=False)
        print("✅ PyInstaller 構建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller 構建失敗 (退出碼: {e.returncode})")
        return False
    except FileNotFoundError:
        print("❌ PyInstaller 未找到，請確保已正確安裝")
        return False

def verify_build():
    """驗證構建結果"""
    print("[6/7] 驗證構建結果...")
    
    # 確定可執行檔名稱
    exe_name = BUILD_CONFIG["dist_name"]
    if platform.system() == "Windows":
        exe_name += ".exe"
    
    exe_path = Path(BUILD_CONFIG["dist_folder"]) / exe_name
    
    if not exe_path.exists():
        print(f"❌ 可執行檔不存在: {exe_path}")
        return False
    
    # 檢查文件大小
    file_size = exe_path.stat().st_size
    print(f"✅ 可執行檔生成成功")
    print(f"   📁 位置: {exe_path}")
    print(f"   📏 大小: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
    
    # 測試可執行檔
    print("   🧪 測試可執行檔...")
    try:
        result = subprocess.run([str(exe_path), "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("   ✅ 可執行檔運行正常")
        else:
            print("   ⚠️  可執行檔可能有問題，請手動測試")
    except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
        print("   ⚠️  無法測試可執行檔，請手動驗證")
    
    return True

def show_summary():
    """顯示構建摘要"""
    print("[7/7] 構建摘要")
    print()
    print("🎉 構建完成！")
    print("=" * 50)
    print()
    
    exe_name = BUILD_CONFIG["dist_name"]
    if platform.system() == "Windows":
        exe_name += ".exe"
    
    exe_path = Path(BUILD_CONFIG["dist_folder"]) / exe_name
    
    print("📋 使用說明:")
    print(f"   1. 可執行檔位於: {exe_path}")
    print(f"   2. 測試命令: {exe_path} --help")
    print(f"   3. 運行命令: {exe_path} [test_item]")
    print()
    
    print("📦 發布包內容:")
    dist_folder = Path(BUILD_CONFIG["dist_folder"])
    if dist_folder.exists():
        for item in sorted(dist_folder.iterdir()):
            if item.is_file():
                size = item.stat().st_size
                print(f"   📄 {item.name} ({size:,} bytes)")
            elif item.is_dir():
                print(f"   📁 {item.name}/")
    print()

def main():
    """主函數"""
    print_header()
    
    # 執行構建步驟
    steps = [
        check_python_version,
        check_dependencies,
        validate_environment,
        clean_build_files,
        run_pyinstaller,
        verify_build,
    ]
    
    for step in steps:
        if not step():
            print()
            print("❌ 構建失敗！")
            return 1
        print()
    
    show_summary()
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  構建被用戶中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 構建過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
