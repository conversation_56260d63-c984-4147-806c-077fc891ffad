"""
DBI Transfer Tool 應用程式主入口

這是 DBI Transfer Tool 的主要應用程式入口點，負責：
- 應用程式初始化和配置
- 命令行參數處理
- 工作流程協調
- 錯誤處理和日誌記錄
- 使用者介面交互

使用方式：
    python application.py [test_item]
    python application.py --help
    python application.py --version
"""
import sys
import os
from typing import Optional

# 確保可以導入同目錄的模組
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 導入編碼修復模組（必須在其他導入之前）
from utils.encoding_fix import setup_encoding, safe_print, safe_input

# 初始化編碼設置
setup_encoding()

# 確保可以導入同目錄的模組
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.workflow import WorkflowManager
from utils.logger import LoggerFactory
from utils.config import ConfigManager
from utils.messages import get_message, format_startup_message, format_config_file_message, format_project_message
from parameterManger import ParameterManager
from validation.error_handler import create_error_handler
from type_definitions import Loggable, FilePath


class Application:
    """
    DBI Transfer Tool 主應用程式類別

    負責協調整個應用程式的執行流程，包括：
    - 配置載入和驗證
    - 日誌系統初始化
    - 工作流程管理
    - 錯誤處理和恢復
    """

    def __init__(self, config_file: FilePath = "para.txt", testing_mode: Optional[bool] = None) -> None:
        """
        初始化應用程式實例

        Args:
            config_file: 配置文件路徑，預設為 "para.txt"
            testing_mode: 測試模式，如果為 True 則跳過用戶輸入等待。如果為 None 則自動檢測
        """
        self.config_file: FilePath = config_file
        self.logger: Optional[Loggable] = None
        self.workflow_manager: Optional[WorkflowManager] = None
        self._is_initialized: bool = False
        # 如果未指定測試模式，則自動檢測
        self.testing_mode: bool = testing_mode if testing_mode is not None else _is_testing_environment()

    def setup(self, params: ParameterManager, test_item: str) -> None:
        """
        設置應用程式組件和服務

        初始化日誌系統和工作流管理器，準備執行環境

        Args:
            params: 參數管理器實例，包含所有配置信息
            test_item: 測試項目名稱，用於生成日誌文件名
        """
        # 創建專案特定的日誌器
        log_filename = f'{params.proj}_{test_item}.log'
        self.logger = LoggerFactory.create_logger(params, log_filename)

        # 初始化工作流管理器
        self.workflow_manager = WorkflowManager(self.logger)

        # 標記為已初始化
        self._is_initialized = True

        self.logger.log("應用程式組件初始化完成", level="INFO")
        self.logger.log(f"專案: {params.proj}, 測試項目: {test_item}", level="INFO")
    
    def run(self, test_item: Optional[str] = None) -> None:
        """
        運行應用程式主流程

        這是應用程式的主要入口點，負責完整的執行流程：
        1. 載入和驗證配置
        2. 獲取測試項目（從參數或使用者輸入）
        3. 初始化應用程式組件
        4. 執行工作流程
        5. 處理錯誤和清理

        Args:
            test_item: 可選的測試項目名稱，如果未提供則提示使用者輸入
        """
        error_handler = None

        try:
            # 載入配置文件
            with ParameterManager(self.config_file) as params:
                self._log_startup_info(params)

                # 確定測試項目
                final_test_item = test_item or self._get_test_item_from_user()

                # 初始化應用程式組件
                self.setup(params, final_test_item)

                # 創建錯誤處理器
                error_handler = create_error_handler(self.logger, self.testing_mode)

                # 執行主要工作流程
                self._execute_main_workflow(params, final_test_item)

                # 顯示執行摘要
                self._display_execution_summary(error_handler)

                self.logger.log("應用程式執行完成", level="INFO")

                # 顯示完成信息並等待用戶
                self._wait_for_user_exit()

        except KeyboardInterrupt:
            self._handle_user_interruption()
        except Exception as e:
            self._handle_application_error(e, error_handler)
            raise

    def _log_startup_info(self, params: ParameterManager) -> None:
        """記錄啟動信息"""
        print(format_startup_message(self.get_version()))
        print(format_config_file_message(str(self.config_file)))
        print(format_project_message(params.proj))

    def _get_test_item_from_user(self) -> str:
        """從使用者獲取測試項目名稱"""
        return ConfigManager.parse_user_input()

    def _execute_main_workflow(self, params: ParameterManager, test_item: str) -> None:
        """執行主要工作流程"""
        if not self._is_initialized:
            raise RuntimeError("應用程式未正確初始化")

        if self.workflow_manager is None:
            raise RuntimeError("工作流管理器未初始化")

        self.workflow_manager.execute_workflow(params, test_item)

    def _display_execution_summary(self, error_handler) -> None:
        """顯示執行摘要"""
        if error_handler and self.logger:
            summary = error_handler.get_error_summary()
            if summary['total_errors'] > 0 or summary['total_warnings'] > 0:
                self.logger.log(
                    f"執行摘要: {summary['total_errors']} 錯誤, {summary['total_warnings']} 警告",
                    level="INFO"
                )

    def _wait_for_user_exit(self) -> None:
        """等待用戶按鍵退出"""
        print("\n" + "="*50)
        print(get_message('execution_complete'))
        print("="*50)
        print(get_message('results_saved'))
        print(get_message('check_files'))
        print(f"\n{get_message('press_any_key')}")

        # 在測試模式下跳過等待用戶輸入
        if self.testing_mode:
            return

        try:
            input()  # 等待用戶按鍵
        except (KeyboardInterrupt, EOFError):
            pass  # 用戶按 Ctrl+C 或關閉窗口時也正常退出

    def _handle_user_interruption(self) -> None:
        """處理使用者中斷"""
        if self.logger:
            self.logger.log("使用者中斷執行", level="WARN")
        print(f"\n{get_message('user_interrupted')}")

        # 在測試模式下跳過等待用戶輸入
        if self.testing_mode:
            return

        # 即使被中斷也等待用戶確認
        try:
            print(get_message('press_any_key_short'))
            input()
        except (KeyboardInterrupt, EOFError):
            pass

    def _handle_application_error(self, error: Exception, error_handler) -> None:
        """處理應用程式錯誤"""
        error_message = f"應用程式執行錯誤：{error}"

        if error_handler:
            error_handler.handle_error_with_context(
                error,
                context={'stage': 'application_run'},
                fatal=True
            )
        elif self.logger:
            self.logger.log(error_message, level="ERROR")
        else:
            print(f"❌ {error_message}")

        # 錯誤情況下也等待用戶確認
        print("\n" + "="*50)
        print(get_message('execution_error'))
        print("="*50)
        print(get_message('check_error_info'))
        print(get_message('need_help'))
        print(f"\n{get_message('press_any_key')}")

        # 在測試模式下跳過等待用戶輸入
        if self.testing_mode:
            return

        try:
            input()
        except (KeyboardInterrupt, EOFError):
            pass
    
    def run_with_config(self, config_file: FilePath, test_item: Optional[str] = None) -> None:
        """
        使用指定配置檔案運行應用程式

        Args:
            config_file: 配置文件路徑
            test_item: 可選的測試項目名稱
        """
        self.config_file = config_file
        self.run(test_item)

    @staticmethod
    def get_version() -> str:
        """
        獲取應用程式版本號

        Returns:
            版本號字串
        """
        return "0.01"

    @staticmethod
    def get_info() -> dict:
        """
        獲取應用程式詳細資訊

        Returns:
            包含應用程式資訊的字典
        """
        return {
            "name": "DBI Transfer Tool",
            "version": Application.get_version(),
            "description": "將 RGT 格式檔案轉換為 DBI 中間格式的專業工具",
            "author": "Sychang",
            "license": "MIT",
            "repository": "https://github.com/example/dbi-transfer-tool"
        }


def main() -> None:
    """
    主函數 - 命令行入口點

    處理命令行參數並啟動應用程式：
    - 無參數：互動模式，提示輸入測試項目
    - 一個參數：可能是測試項目名稱或選項
    - 多個參數：顯示錯誤
    """
    try:
        # 檢測是否在測試環境中，如果是則啟用測試模式
        testing_mode = _is_testing_environment()
        app = Application(testing_mode=testing_mode)

        # 處理命令行參數
        if len(sys.argv) > 1:
            arg = sys.argv[1]

            if arg in ['-h', '--help']:
                _display_help()
                _wait_for_exit()
            elif arg in ['-v', '--version']:
                _display_version()
                _wait_for_exit()
            else:
                # 假設是測試項目名稱
                print(get_message('startup_test_item').format(arg))
                app.run(arg)
        else:
            # 互動模式
            print(get_message('startup_interactive'))
            app.run()

    except KeyboardInterrupt:
        print(f"\n{get_message('user_interrupted')}")
        _wait_for_exit()
        sys.exit(1)
    except Exception as e:
        print(get_message('startup_failed').format(e))
        _wait_for_exit()
        sys.exit(1)


def _display_help() -> None:
    """顯示幫助信息"""
    info = Application.get_info()
    print(f"📋 {info['name']} v{info['version']}")
    print(f"📝 {info['description']}")
    print(f"👤 作者: {info['author']}")
    print()
    print(get_message('usage_title'))
    print(get_message('usage_interactive'))
    print(get_message('usage_test_item'))
    print(get_message('usage_help'))
    print(get_message('usage_version'))
    print()
    print(get_message('examples_title'))
    print(get_message('example_test'))


def _display_version() -> None:
    """顯示版本信息"""
    info = Application.get_info()
    print(f"📋 {info['name']} v{info['version']}")
    print(get_message('author').format(info['author']))
    if 'license' in info:
        print(get_message('license').format(info['license']))


def _is_testing_environment() -> bool:
    """檢測是否在測試環境中運行"""
    return (
        'pytest' in sys.modules or
        'unittest' in sys.modules or
        os.environ.get('PYTEST_CURRENT_TEST') is not None or
        os.environ.get('TESTING') == 'true'
    )

def _wait_for_exit() -> None:
    """等待用戶按鍵退出（用於幫助和版本顯示）"""
    print(f"\n{get_message('press_any_key_short')}")

    # 在測試環境中跳過等待用戶輸入
    if _is_testing_environment():
        return

    try:
        input()
    except (KeyboardInterrupt, EOFError):
        pass


if __name__ == "__main__":
    main()
