{"data_mtime": 1750671873, "dep_lines": [4, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30], "dependencies": ["output.output_manager", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "5b69b31d17884d45c6c5dcf4ad7ed5c31176f7c2", "id": "output", "ignore_all": false, "interface_hash": "f9f162bed70adff181f8a8b1976939dc28328f21", "mtime": 1750524064, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\output\\__init__.py", "plugin_data": null, "size": 98, "suppressed": [], "version_id": "1.16.1"}