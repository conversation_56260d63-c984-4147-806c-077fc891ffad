#!/usr/bin/env python3
"""
測試命令解析修復

驗證修復後的命令分割是否正確處理帶空格的命令列表
"""
import sys
import os

# 添加源碼路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'source'))

def test_command_splitting():
    """測試命令分割修復"""
    print("🧪 測試命令分割修復")
    print("=" * 40)
    
    # 模擬配置文件中的命令字符串（帶空格）
    test_cases = [
        "set_pin, get_pin, include",
        "set_pin,get_pin,include",  # 無空格
        "set_pin, get_pin, set_title, set_regif, log, label, include",  # 完整列表
        "set_pin,  get_pin,   include",  # 多個空格
    ]
    
    for i, commands_str in enumerate(test_cases, 1):
        print(f"\n測試案例 {i}: '{commands_str}'")
        
        # 舊方法（有問題）
        old_result = commands_str.split(",")
        print(f"  舊方法結果: {old_result}")
        
        # 新方法（修復後）
        new_result = [cmd.strip() for cmd in commands_str.split(",")]
        print(f"  新方法結果: {new_result}")
        
        # 檢查 include 命令是否能正確匹配
        include_in_old = "include" in old_result
        include_in_new = "include" in new_result
        
        print(f"  'include' 在舊結果中: {include_in_old}")
        print(f"  'include' 在新結果中: {include_in_new}")
        
        if include_in_new and not include_in_old:
            print(f"  ✅ 修復成功！")
        elif include_in_new and include_in_old:
            print(f"  ✅ 正常工作")
        else:
            print(f"  ❌ 仍有問題")

def test_workflow_integration():
    """測試工作流集成"""
    print("\n🧪 測試工作流集成")
    print("=" * 40)
    
    try:
        from core.workflow import WorkflowManager
        from parameterManger import ParameterManager
        from unittest.mock import Mock
        
        # 創建模擬的參數管理器
        mock_params = Mock()
        mock_params.commands = "set_pin, get_pin, include"  # 帶空格的命令
        
        # 創建工作流管理器
        mock_parser = Mock()
        mock_output_manager = Mock()
        mock_logger = Mock()
        
        workflow = WorkflowManager(mock_parser, mock_output_manager, mock_logger)
        
        # 模擬 process_rgt_file 方法中的命令處理
        commands = [cmd.strip() for cmd in mock_params.commands.split(",")]
        
        print(f"原始命令字符串: '{mock_params.commands}'")
        print(f"處理後的命令列表: {commands}")
        print(f"'include' 在命令列表中: {'include' in commands}")
        
        if "include" in commands:
            print("✅ 工作流集成測試通過")
            return True
        else:
            print("❌ 工作流集成測試失敗")
            return False
            
    except Exception as e:
        print(f"❌ 工作流集成測試異常: {e}")
        return False

def test_real_config_file():
    """測試真實配置文件"""
    print("\n🧪 測試真實配置文件")
    print("=" * 40)
    
    try:
        # 讀取真實的配置文件
        config_path = os.path.join("source", "para.txt")
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找 commands 行
            for line in content.split('\n'):
                if line.startswith('commands='):
                    commands_str = line.split('=', 1)[1]
                    print(f"配置文件中的命令: '{commands_str}'")
                    
                    # 測試分割
                    old_result = commands_str.split(",")
                    new_result = [cmd.strip() for cmd in commands_str.split(",")]
                    
                    print(f"舊方法結果: {old_result}")
                    print(f"新方法結果: {new_result}")
                    
                    # 檢查是否有帶空格的命令
                    has_spaces = any(cmd.startswith(' ') or cmd.endswith(' ') for cmd in old_result)
                    
                    if has_spaces:
                        print("✅ 發現帶空格的命令，修復有效")
                    else:
                        print("ℹ️  配置文件中沒有帶空格的命令")
                    
                    return True
            
            print("⚠️  配置文件中未找到 commands 行")
            return False
        else:
            print(f"⚠️  配置文件不存在: {config_path}")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件測試異常: {e}")
        return False

def main():
    """主函數"""
    print("🔧 命令解析修復驗證測試")
    print("=" * 50)
    
    # 運行測試
    tests = [
        ("命令分割", test_command_splitting),
        ("工作流集成", test_workflow_integration),
        ("真實配置文件", test_real_config_file),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result is True:
                passed += 1
                print(f"\n✅ {test_name} 測試通過")
            elif result is False:
                failed += 1
                print(f"\n❌ {test_name} 測試失敗")
            else:
                # 對於沒有返回值的測試，假設通過
                passed += 1
                print(f"\n✅ {test_name} 測試完成")
        except Exception as e:
            failed += 1
            print(f"\n❌ {test_name} 測試異常: {e}")
    
    # 顯示結果
    print("\n" + "=" * 50)
    print("📊 測試結果:")
    print(f"✅ 通過: {passed}")
    print(f"❌ 失敗: {failed}")
    print(f"📋 總計: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 所有測試通過！命令解析修復成功。")
        print("✅ 現在 #include 命令應該能正確識別")
    else:
        print(f"\n⚠️  {failed} 個測試失敗，請檢查修復。")
    
    return 0 if failed == 0 else 1

if __name__ == "__main__":
    sys.exit(main())
