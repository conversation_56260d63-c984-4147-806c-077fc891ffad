#!/usr/bin/env python3
"""
測試語言環境修復

驗證修復後的語言檢測是否正常工作，不再產生棄用警告
"""
import sys
import os
import warnings

# 添加源碼路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'source'))

def test_locale_detection():
    """測試語言環境檢測"""
    print("🧪 測試語言環境檢測（無棄用警告）")
    print("=" * 50)
    
    # 捕獲警告
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        try:
            # 測試消息系統
            from utils.messages import MessageManager, get_language, get_message
            
            # 創建消息管理器（這會觸發語言檢測）
            manager = MessageManager()
            
            print(f"✅ 檢測到的語言: {manager.language}")
            print(f"✅ 語言檢測函數: {get_language()}")
            
            # 測試一些消息
            print(f"✅ 應用名稱: {get_message('app_name')}")
            print(f"✅ 完成消息: {get_message('execution_complete')}")
            
            # 檢查是否有棄用警告
            deprecation_warnings = [warning for warning in w if issubclass(warning.category, DeprecationWarning)]
            
            if deprecation_warnings:
                print(f"\n❌ 發現 {len(deprecation_warnings)} 個棄用警告:")
                for warning in deprecation_warnings:
                    print(f"   - {warning.message}")
                    print(f"     文件: {warning.filename}:{warning.lineno}")
                return False
            else:
                print("\n✅ 沒有發現棄用警告")
                return True
                
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            return False

def test_encoding_fix():
    """測試編碼修復"""
    print("\n🧪 測試編碼修復（無棄用警告）")
    print("=" * 50)
    
    # 捕獲警告
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        try:
            # 測試編碼修復模組
            from utils.encoding_fix import (
                is_chinese_system, 
                is_simplified_chinese_system,
                detect_system_encoding,
                test_encoding
            )
            
            print(f"✅ 是否中文系統: {is_chinese_system()}")
            print(f"✅ 是否簡體中文: {is_simplified_chinese_system()}")
            print(f"✅ 編碼測試: {'通過' if test_encoding() else '失敗'}")
            
            # 獲取系統編碼信息
            encoding_info = detect_system_encoding()
            print(f"✅ 系統編碼信息: {len(encoding_info)} 項")
            
            # 檢查是否有棄用警告
            deprecation_warnings = [warning for warning in w if issubclass(warning.category, DeprecationWarning)]
            
            if deprecation_warnings:
                print(f"\n❌ 發現 {len(deprecation_warnings)} 個棄用警告:")
                for warning in deprecation_warnings:
                    print(f"   - {warning.message}")
                    print(f"     文件: {warning.filename}:{warning.lineno}")
                return False
            else:
                print("\n✅ 沒有發現棄用警告")
                return True
                
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            return False

def test_application_import():
    """測試應用程序導入"""
    print("\n🧪 測試應用程序導入（無棄用警告）")
    print("=" * 50)
    
    # 捕獲警告
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        try:
            # 測試應用程序導入
            from application import Application
            
            # 獲取應用信息
            info = Application.get_info()
            print(f"✅ 應用名稱: {info.get('name', 'N/A')}")
            print(f"✅ 版本: {info.get('version', 'N/A')}")
            
            # 檢查是否有棄用警告
            deprecation_warnings = [warning for warning in w if issubclass(warning.category, DeprecationWarning)]
            
            if deprecation_warnings:
                print(f"\n❌ 發現 {len(deprecation_warnings)} 個棄用警告:")
                for warning in deprecation_warnings:
                    print(f"   - {warning.message}")
                    print(f"     文件: {warning.filename}:{warning.lineno}")
                return False
            else:
                print("\n✅ 沒有發現棄用警告")
                return True
                
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            return False

def main():
    """主函數"""
    print("🔧 語言環境修復驗證測試")
    print("=" * 60)
    print()
    
    # 運行測試
    tests = [
        ("語言環境檢測", test_locale_detection),
        ("編碼修復", test_encoding_fix),
        ("應用程序導入", test_application_import),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 測試通過")
            else:
                failed += 1
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} 測試異常: {e}")
        print()
    
    # 顯示結果
    print("=" * 60)
    print("📊 測試結果:")
    print(f"✅ 通過: {passed}")
    print(f"❌ 失敗: {failed}")
    print(f"📋 總計: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 所有測試通過！語言環境修復成功。")
        print("✅ 不再有 locale.getdefaultlocale() 棄用警告")
    else:
        print(f"\n⚠️  {failed} 個測試失敗，請檢查修復。")
    
    print("\n按任意鍵退出...")
    try:
        input()
    except (KeyboardInterrupt, EOFError):
        pass

if __name__ == "__main__":
    main()
