"""
DBI Transfer Tool 自定義異常類
"""
from typing import Optional, Dict, Any


class DbiTransferError(Exception):
    """DBI Transfer Tool 基礎異常類"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 context: Optional[Dict[str, Any]] = None):
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.context = context or {}
        super().__init__(self.message)
    
    def __str__(self) -> str:
        if self.context:
            context_str = ", ".join(f"{k}={v}" for k, v in self.context.items())
            return f"{self.message} (Context: {context_str})"
        return self.message
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式，便於日誌記錄"""
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "context": self.context
        }


class ConfigurationError(DbiTransferError):
    """配置相關錯誤"""
    pass


class FileOperationError(DbiTransferError):
    """文件操作錯誤"""
    
    def __init__(self, message: str, file_path: Optional[str] = None,
                 operation: Optional[str] = None, **kwargs: Any) -> None:
        context = kwargs.pop('context', {})  # 使用 pop 避免重複傳遞
        if file_path:
            context['file_path'] = file_path
        if operation:
            context['operation'] = operation
        super().__init__(message, context=context, **kwargs)


class ParseError(DbiTransferError):
    """解析錯誤"""
    
    def __init__(self, message: str, line_number: Optional[int] = None,
                 line_content: Optional[str] = None, file_path: Optional[str] = None, **kwargs: Any) -> None:
        context = kwargs.pop('context', {})  # 使用 pop 避免重複傳遞
        if line_number is not None:
            context['line_number'] = line_number
        if line_content:
            context['line_content'] = line_content[:100]  # 限制長度
        if file_path:
            context['file_path'] = file_path

        # 構建詳細的錯誤訊息
        detailed_message = message
        if line_number is not None:
            detailed_message = f"Line {line_number}: {message}"
        if file_path:
            detailed_message = f"{file_path} - {detailed_message}"

        super().__init__(detailed_message, context=context, **kwargs)


class ValidationError(DbiTransferError):
    """驗證錯誤"""
    
    def __init__(self, message: str, field_name: Optional[str] = None,
                 field_value: Optional[str] = None, **kwargs: Any) -> None:
        context = kwargs.pop('context', {})  # 使用 pop 避免重複傳遞
        if field_name:
            context['field_name'] = field_name
        if field_value:
            context['field_value'] = str(field_value)[:50]  # 限制長度
        super().__init__(message, context=context, **kwargs)


class VersionCompatibilityError(DbiTransferError):
    """版本兼容性錯誤"""
    
    def __init__(self, message: str, exe_version: Optional[str] = None,
                 para_version: Optional[str] = None, **kwargs: Any) -> None:
        context = kwargs.pop('context', {})  # 使用 pop 避免重複傳遞
        if exe_version:
            context['exe_version'] = exe_version
        if para_version:
            context['para_version'] = para_version
        super().__init__(message, context=context, **kwargs)


class ProcessingError(DbiTransferError):
    """處理過程錯誤"""
    
    def __init__(self, message: str, stage: Optional[str] = None,
                 processed_count: Optional[int] = None, **kwargs: Any) -> None:
        context = kwargs.pop('context', {})  # 使用 pop 避免重複傳遞
        if stage:
            context['stage'] = stage
        if processed_count is not None:
            context['processed_count'] = processed_count
        super().__init__(message, context=context, **kwargs)


class OutputError(DbiTransferError):
    """輸出錯誤"""
    
    def __init__(self, message: str, output_path: Optional[str] = None,
                 output_format: Optional[str] = None, **kwargs: Any) -> None:
        context = kwargs.pop('context', {})  # 使用 pop 避免重複傳遞
        if output_path:
            context['output_path'] = output_path
        if output_format:
            context['output_format'] = output_format
        super().__init__(message, context=context, **kwargs)


class CommandNotSupportedError(DbiTransferError):
    """不支援的命令錯誤"""
    
    def __init__(self, command: str, supported_commands: Optional[list] = None, **kwargs: Any) -> None:
        context = kwargs.pop('context', {})  # 使用 pop 避免重複傳遞
        context['command'] = command
        if supported_commands:
            context['supported_commands'] = supported_commands

        message = f"Command '{command}' is not supported"
        if supported_commands:
            message += f". Supported commands: {', '.join(supported_commands)}"

        super().__init__(message, context=context, **kwargs)


class DataFormatError(DbiTransferError):
    """資料格式錯誤"""
    
    def __init__(self, message: str, data: Optional[str] = None,
                 expected_format: Optional[str] = None, **kwargs: Any) -> None:
        context = kwargs.pop('context', {})  # 使用 pop 避免重複傳遞
        if data:
            context['data'] = str(data)[:50]  # 限制長度
        if expected_format:
            context['expected_format'] = expected_format
        super().__init__(message, context=context, **kwargs)


# 錯誤代碼常數
class ErrorCodes:
    """錯誤代碼常數"""
    
    # 配置錯誤
    CONFIG_FILE_NOT_FOUND = "CFG001"
    CONFIG_INVALID_FORMAT = "CFG002"
    CONFIG_MISSING_FIELD = "CFG003"
    
    # 文件錯誤
    FILE_NOT_FOUND = "FILE001"
    FILE_PERMISSION_DENIED = "FILE002"
    FILE_CORRUPTED = "FILE003"
    
    # 解析錯誤
    PARSE_INVALID_SYNTAX = "PARSE001"
    PARSE_INVALID_COMMAND = "PARSE002"
    PARSE_INVALID_DATA = "PARSE003"
    
    # 驗證錯誤
    VALIDATION_FAILED = "VAL001"
    VERSION_INCOMPATIBLE = "VAL002"
    
    # 處理錯誤
    PROCESSING_FAILED = "PROC001"
    OUTPUT_FAILED = "PROC002"


def create_error_from_exception(exc: Exception, context: Optional[Dict[str, Any]] = None) -> DbiTransferError:
    """從標準異常創建自定義異常"""
    if isinstance(exc, DbiTransferError):
        return exc
    
    error_context = context or {}
    error_context['original_exception'] = str(exc)
    error_context['exception_type'] = type(exc).__name__
    
    if isinstance(exc, FileNotFoundError):
        return FileOperationError(
            f"File not found: {exc}",
            error_code=ErrorCodes.FILE_NOT_FOUND,
            context=error_context
        )
    elif isinstance(exc, PermissionError):
        return FileOperationError(
            f"Permission denied: {exc}",
            error_code=ErrorCodes.FILE_PERMISSION_DENIED,
            context=error_context
        )
    elif isinstance(exc, ValueError):
        return ValidationError(
            f"Invalid value: {exc}",
            error_code=ErrorCodes.VALIDATION_FAILED,
            context=error_context
        )
    else:
        return DbiTransferError(
            f"Unexpected error: {exc}",
            context=error_context
        )
