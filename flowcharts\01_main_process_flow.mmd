flowchart TD
    A[程序啟動] --> B[初始化編碼設置]
    B --> C[檢測測試環境]
    C --> D[創建 Application 實例]
    D --> E{解析命令行參數}
    
    E -->|--help| F[顯示幫助信息]
    E -->|--version| G[顯示版本信息]
    E -->|test_item| H[指定測試項目模式]
    E -->|無參數| I[互動模式]
    
    F --> F1[等待用戶按鍵]
    G --> G1[等待用戶按鍵]
    F1 --> END[程序結束]
    G1 --> END
    
    H --> J[載入配置文件 para.txt]
    I --> K[提示用戶輸入測試項目]
    K --> J
    
    J --> L{配置文件是否存在?}
    L -->|否| M[顯示配置文件錯誤]
    L -->|是| N[解析配置參數]
    
    M --> M1[錯誤處理]
    M1 --> M2[等待用戶按鍵]
    M2 --> END
    
    N --> O[初始化應用程序組件]
    O --> P[創建日誌系統]
    P --> Q[創建工作流管理器]
    Q --> R[創建錯誤處理器]
    
    R --> S[執行主要工作流程]
    S --> T[RGT 文件解析]
    T --> U{RGT 文件是否存在?}
    
    U -->|否| V[文件不存在錯誤]
    U -->|是| W[解析 RGT 內容]
    
    V --> V1[錯誤處理]
    V1 --> V2[等待用戶按鍵]
    V2 --> END
    
    W --> X{解析是否成功?}
    X -->|否| Y[格式錯誤處理]
    X -->|是| Z[生成 DBI 中間格式]
    
    Y --> Y1[記錄錯誤]
    Y1 --> Y2[等待用戶按鍵]
    Y2 --> END
    
    Z --> AA[調用 OP2 工具]
    AA --> BB{OP2 執行是否成功?}
    
    BB -->|否| CC[OP2 執行錯誤]
    BB -->|是| DD[生成輸出文件]
    
    CC --> CC1[錯誤處理]
    CC1 --> CC2[等待用戶按鍵]
    CC2 --> END
    
    DD --> EE[保存結果到 results 目錄]
    EE --> FF[顯示執行摘要]
    FF --> GG[記錄完成日誌]
    GG --> HH[顯示完成信息]
    HH --> II{是否為測試模式?}
    
    II -->|是| END
    II -->|否| JJ[等待用戶按鍵退出]
    JJ --> END
    
    %% 異常處理流程
    S -.->|KeyboardInterrupt| KK[用戶中斷處理]
    S -.->|Exception| LL[應用程序錯誤處理]
    
    KK --> MM[顯示中斷信息]
    MM --> NN{是否為測試模式?}
    NN -->|是| END
    NN -->|否| OO[等待用戶按鍵]
    OO --> END
    
    LL --> PP[顯示錯誤信息]
    PP --> QQ[記錄錯誤日誌]
    QQ --> RR{是否為測試模式?}
    RR -->|是| END
    RR -->|否| SS[等待用戶按鍵]
    SS --> END
    
    %% 樣式定義
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    
    class A,END startEnd
    class B,C,D,J,N,O,P,Q,R,S,T,W,Z,AA,DD,EE,FF,GG,HH process
    class E,L,U,X,BB,II,NN,RR decision
    class M,V,Y,CC,KK,LL,MM,PP error
    class F,G,H,I success
