# DBI Transfer Tool

一個專業的 DBI (Display Bus Interface) 數據傳輸工具，用於 ChipOne 技術的顯示控制器測試和驗證。

## 🚀 功能特點

- **多格式支持**: 支持 RGT、TXT、CSV 等多種輸入格式
- **智能解析**: 自動解析和驗證 DBI 命令序列
- **靈活配置**: 支持多種配置方式（文件、環境變數、命令行）
- **版本兼容**: 自動檢查配置文件與可執行文件的兼容性
- **錯誤處理**: 完善的錯誤處理和日誌記錄機制
- **跨平台**: 支持 Windows、Linux、macOS

## 📋 目錄

- [快速開始](#快速開始)
- [安裝說明](#安裝說明)
- [使用方法](#使用方法)
- [配置說明](#配置說明)
- [構建說明](#構建說明)
- [開發指南](#開發指南)
- [測試](#測試)
- [貢獻](#貢獻)

## 🏃 快速開始

### 使用預構建的可執行文件

1. **下載發布版本**：
   ```bash
   # 下載最新版本的可執行文件
   # 解壓到目標目錄
   ```

2. **運行工具**：
   ```bash
   # Windows
   DBI_Transfer.exe --help
   DBI_Transfer.exe test_item_name
   
   # Linux/macOS
   ./DBI_Transfer --help
   ./DBI_Transfer test_item_name
   ```

### 從源碼運行

1. **克隆倉庫**：
   ```bash
   git clone <repository-url>
   cd dbi_transfer_tool
   ```

2. **安裝依賴**：
   ```bash
   pip install -r requirements.txt
   ```

3. **運行程序**：
   ```bash
   python source/application.py --help
   python source/application.py test_item_name
   ```

## 📦 安裝說明

### 系統要求

- **Python**: 3.8 或更高版本
- **操作系統**: Windows 10+, Linux (Ubuntu 18.04+), macOS 10.14+
- **內存**: 至少 512MB RAM
- **磁盤空間**: 至少 100MB 可用空間

### 依賴項

主要依賴項會自動安裝：

```bash
pip install -r requirements.txt
```

## 🔧 使用方法

### 基本用法

```bash
# 顯示幫助信息
DBI_Transfer --help

# 運行特定測試項目
DBI_Transfer test_item_name

# 使用自定義配置文件
DBI_Transfer --config custom_config.txt test_item_name

# 顯示版本信息
DBI_Transfer --version
```

### 高級用法

```bash
# 啟用調試模式
DBI_Transfer --debug test_item_name

# 指定輸出目錄
DBI_Transfer --output-dir /path/to/output test_item_name

# 使用環境變數配置
export DBI_DEBUG=true
export DBI_OUTPUT_WIDTH=120
DBI_Transfer test_item_name
```

## ⚙️ 配置說明

### 配置文件格式

支持多種配置文件格式：

#### 1. 簡單格式 (para.txt)
```ini
version=1.0
proj=ICNA3611
commands=set_pin,get_pin,reset
output_width=120
debug=false
```

#### 2. JSON 格式
```json
{
  "version": "1.0",
  "proj": "ICNA3611",
  "commands": ["set_pin", "get_pin", "reset"],
  "output_width": 120,
  "debug": false
}
```

#### 3. INI 格式
```ini
[general]
version = 1.0
proj = ICNA3611

[commands]
list = set_pin,get_pin,reset

[output]
width = 120
debug = false
```

### 配置優先級

配置載入優先級（高到低）：
1. 命令行參數
2. 環境變數
3. 配置文件
4. 預設值

### 環境變數

使用 `DBI_` 前綴的環境變數：

```bash
export DBI_DEBUG=true
export DBI_OUTPUT_WIDTH=120
export DBI_LOG_LEVEL=DEBUG
```

## 🔨 構建說明

詳細的構建說明請參考 [BUILD.md](BUILD.md)。

### 快速構建

```bash
# Windows
build_exe.bat

# Linux/macOS
./build.sh

# 跨平台 (Python)
python build.py
```

構建完成後，可執行文件將位於 `DBI_Transfer/` 目錄中。

## 👨‍💻 開發指南

### 項目結構

```
dbi_transfer_tool/
├── source/                 # 源代碼
│   ├── application.py      # 主應用程序
│   ├── config/            # 配置管理
│   ├── core/              # 核心引擎
│   ├── parsers/           # 解析器
│   ├── output/            # 輸出管理
│   ├── utils/             # 工具函數
│   └── validation/        # 驗證模組
├── test/                  # 測試文件
├── scripts/               # 腳本工具
├── build_exe.bat         # Windows 構建腳本
├── build.sh              # Linux/macOS 構建腳本
├── build.py              # 跨平台構建腳本
├── build_config.py       # 構建配置
├── BUILD.md              # 構建說明
└── README.md             # 本文件
```

### 代碼風格

- 使用 Python 3.8+ 類型提示
- 遵循 PEP 8 代碼風格
- 使用 Black 進行代碼格式化
- 使用 MyPy 進行類型檢查

### 開發環境設置

```bash
# 安裝開發依賴
pip install -r test/requirements.txt

# 運行類型檢查
python scripts/type_check.py

# 運行代碼格式化
black source/ test/
```

## 🧪 測試

### 運行測試

```bash
# 運行所有測試
python -m pytest test/

# 運行特定測試文件
python -m pytest test/test_application.py

# 運行測試並生成覆蓋率報告
python -m pytest test/ --cov=source --cov-report=html
```

### 測試覆蓋率

當前測試覆蓋率：**84%**

- 配置系統：94%
- 核心引擎：47%
- 解析器：80%
- 工作流：100%
- 日誌系統：95%

## 📚 文檔

- [BUILD.md](BUILD.md) - 構建說明
- [source/docs/](source/docs/) - 詳細文檔
  - [configuration_guide.md](source/docs/configuration_guide.md) - 配置指南
  - [error_handling_guide.md](source/docs/error_handling_guide.md) - 錯誤處理指南
  - [type_annotations_guide.md](source/docs/type_annotations_guide.md) - 類型註解指南

## 🤝 貢獻

歡迎貢獻代碼！請遵循以下步驟：

1. Fork 本倉庫
2. 創建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 創建 Pull Request

### 貢獻指南

- 確保所有測試通過
- 添加適當的測試用例
- 更新相關文檔
- 遵循現有的代碼風格

## 📄 許可證

本項目使用 MIT 許可證 - 詳見 [LICENSE](LICENSE) 文件。

## 📞 支持

如果您遇到問題或有疑問：

1. 查看 [文檔](source/docs/)
2. 搜索現有的 [Issues](../../issues)
3. 創建新的 [Issue](../../issues/new)

## 🏷️ 版本歷史

### v1.0.0 (當前版本)
- ✅ 完整的 DBI 命令解析
- ✅ 多格式配置支持
- ✅ 版本兼容性檢查
- ✅ 跨平台構建支持
- ✅ 完善的測試覆蓋

---

**ChipOne Technology** - DBI Transfer Tool Team
