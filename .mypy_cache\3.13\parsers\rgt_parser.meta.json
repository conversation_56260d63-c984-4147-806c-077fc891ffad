{"data_mtime": 1750671873, "dep_lines": [23, 14, 15, 16, 17, 22, 24, 25, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["validation.error_handler", "re", "sys", "os", "typing", "constant", "exceptions", "type_definitions", "builtins", "_collections_abc", "_frozen_importlib", "_io", "abc", "enum", "io", "validation"], "hash": "06bd443ca6f405f91a64dc75c4045219feea351f", "id": "parsers.rgt_parser", "ignore_all": false, "interface_hash": "129a2d07a431e313153c3e65f6eace3e28eceb3b", "mtime": 1750671392, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\parsers\\rgt_parser.py", "plugin_data": null, "size": 12663, "suppressed": [], "version_id": "1.16.1"}