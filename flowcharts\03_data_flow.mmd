flowchart LR
    subgraph "輸入階段"
        A[命令行參數] --> B[Application.main]
        C[para.txt<br/>配置文件] --> D[ParameterManager]
        E[RGT文件<br/>測試數據] --> F[RgtParser]
    end
    
    subgraph "處理階段"
        B --> G[Application.run]
        D --> H[配置驗證]
        H --> I[WorkflowManager]
        F --> J[數據解析]
        J --> K[格式轉換]
        K --> L[CoreEngine]
        L --> M[OP2處理]
    end
    
    subgraph "輸出階段"
        M --> N[DBI中間文件]
        N --> O[OutputManager]
        O --> P[results目錄]
        G --> Q[日誌文件]
        Q --> R[LogPrinter]
    end
    
    subgraph "錯誤處理"
        S[異常捕獲] --> T[ErrorHandler]
        T --> U[錯誤日誌]
        T --> V[用戶提示]
    end
    
    subgraph "多語言支持"
        W[系統語言檢測] --> X[MessageManager]
        X --> Y[本地化消息]
        Y --> Z[用戶界面]
    end
    
    %% 數據流連接
    G -.-> I
    I -.-> L
    H -.-> S
    J -.-> S
    L -.-> S
    M -.-> S
    
    %% 多語言流連接
    V -.-> X
    Z -.-> V
    
    %% 樣式定義
    classDef input fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    classDef output fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef error fill:#ffebee,stroke:#f44336,stroke-width:2px
    classDef i18n fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    
    class A,C,E input
    class B,D,F,G,H,I,J,K,L,M process
    class N,O,P,Q,R output
    class S,T,U,V error
    class W,X,Y,Z i18n
