"""
測試工具模組
"""
import pytest
from unittest.mock import Mock, patch, mock_open
import tempfile
import os
from pathlib import Path

from utils.file_utils import FileUtils
from utils.logger import LoggerFactory
from utils.config import ConfigManager


class TestFileUtils:
    """測試檔案工具"""
    
    @patch('os.listdir')
    @patch('os.path.isfile')
    @patch('shutil.copy2')
    @patch('os.makedirs')
    def test_copy_and_rename_files_basic(self, mock_makedirs, mock_copy, mock_isfile, mock_listdir, mock_logger):
        """測試基本檔案複製重命名"""
        mock_listdir.return_value = ["file1.txt", "file2.log"]
        mock_isfile.return_value = True
        
        FileUtils.copy_and_rename_files(
            "/src", "/dst", "new_base", add_index=False, logger=mock_logger
        )
        
        mock_makedirs.assert_called_once_with("/dst", exist_ok=True)
        assert mock_copy.call_count == 2
        mock_logger.step.assert_called()
    
    @patch('os.listdir')
    @patch('os.path.isfile')
    @patch('shutil.copy2')
    @patch('os.makedirs')
    def test_copy_and_rename_files_with_index(self, mock_makedirs, mock_copy, mock_isfile, mock_listdir, mock_logger):
        """測試帶索引的檔案複製重命名"""
        mock_listdir.return_value = ["file1.txt", "file2.txt"]
        mock_isfile.return_value = True
        
        FileUtils.copy_and_rename_files(
            "/src", "/dst", "new_base", add_index=True, logger=mock_logger
        )
        
        assert mock_copy.call_count == 2
        # 驗證檔案名稱包含索引
        call_args = mock_copy.call_args_list
        assert "new_base_1.txt" in call_args[0][0][1]
        assert "new_base_2.txt" in call_args[1][0][1]
    
    @patch('os.listdir')
    @patch('os.path.isfile')
    @patch('shutil.copy2')
    @patch('os.makedirs')
    def test_copy_and_rename_files_skip_directories(self, mock_makedirs, mock_copy, mock_isfile, mock_listdir):
        """測試跳過目錄"""
        mock_listdir.return_value = ["file1.txt", "subdir"]
        mock_isfile.side_effect = lambda path: "file1.txt" in path
        
        FileUtils.copy_and_rename_files("/src", "/dst", "new_base")
        
        # 只應該複製檔案，不複製目錄
        assert mock_copy.call_count == 1
    
    @patch('builtins.open', new_callable=mock_open, read_data="line1\noriginal_line2\nline3\n")
    def test_update_batch_file(self, mock_file):
        """測試更新批次檔案"""
        FileUtils.update_batch_file("TEST_PROJ", "test_folder", "output.txt")

        # 驗證檔案被開啟兩次（讀取和寫入）
        assert mock_file.call_count == 2

        # 獲取寫入的內容
        # mock_file().writelines 被調用來寫入修改後的內容
        mock_file().writelines.assert_called_once()

        # 檢查 writelines 的參數
        written_lines = mock_file().writelines.call_args[0][0]
        written_content = ''.join(written_lines)
        assert "cases/TEST_PROJ/test_folder/output.txt" in written_content
    
    @patch('subprocess.run')
    def test_execute_batch_file(self, mock_run):
        """測試執行批次檔案"""
        FileUtils.execute_batch_file()
        
        mock_run.assert_called_once_with("run_in_win", shell=True, check=True)


class TestLoggerFactory:
    """測試日誌工廠"""
    
    @patch('utils.logger.LogPrinter')
    def test_create_logger(self, mock_logger_class, sample_params):
        """測試創建日誌器"""
        mock_logger_instance = Mock()
        mock_logger_class.return_value = mock_logger_instance

        result = LoggerFactory.create_logger(sample_params, "test.log")

        assert result == mock_logger_instance

        # 驗證日誌級別設定
        mock_logger_instance.enable.assert_called()
        mock_logger_instance.disable.assert_called()
        mock_logger_instance.enable_file_log.assert_called_with("log/test.log")
    
    @patch('utils.logger.LogPrinter')
    def test_create_logger_all_disabled(self, mock_logger_class):
        """測試創建所有級別都關閉的日誌器"""
        mock_logger_instance = Mock()
        mock_logger_class.return_value = mock_logger_instance

        # 創建所有日誌級別都關閉的參數
        class DisabledParams:
            def __init__(self):
                self.debug = "0"
                self.info = "0"
                self.warn = "0"
                self.error = "0"
                self.note = "0"

        params = DisabledParams()
        result = LoggerFactory.create_logger(params, "test.log")

        # 驗證所有級別都被禁用
        disable_calls = mock_logger_instance.disable.call_args_list
        assert len(disable_calls) == 5  # 5個日誌級別


class TestConfigManager:
    """測試配置管理器"""
    
    @patch('builtins.input', return_value='test_input')
    def test_parse_user_input(self, mock_input):
        """測試解析使用者輸入"""
        result = ConfigManager.parse_user_input()
        
        assert result == 'test_input'
        mock_input.assert_called_once_with("Enter test item: ")
    
    @patch('utils.file_utils.FileUtils.copy_and_rename_files')
    def test_copy_result_files(self, mock_copy, mock_logger):
        """測試複製結果檔案"""
        ConfigManager.copy_result_files(
            "src_folder", "dest_folder", "base_name", mock_logger
        )
        
        mock_copy.assert_called_once_with(
            "src_folder", "dest_folder", "base_name", logger=mock_logger
        )


class TestUtilsIntegration:
    """測試工具模組整合"""
    
    def test_file_operations_workflow(self, tmp_path, mock_logger):
        """測試檔案操作工作流程"""
        # 創建測試檔案
        src_dir = tmp_path / "src"
        dst_dir = tmp_path / "dst"
        src_dir.mkdir()
        
        test_file1 = src_dir / "test1.txt"
        test_file2 = src_dir / "test2.log"
        test_file1.write_text("content1")
        test_file2.write_text("content2")
        
        # 執行複製操作
        FileUtils.copy_and_rename_files(
            str(src_dir), str(dst_dir), "renamed", logger=mock_logger
        )
        
        # 驗證結果
        assert dst_dir.exists()
        renamed_files = list(dst_dir.glob("renamed*"))
        assert len(renamed_files) == 2
        
        # 驗證檔案內容
        for file in renamed_files:
            assert file.read_text() in ["content1", "content2"]
