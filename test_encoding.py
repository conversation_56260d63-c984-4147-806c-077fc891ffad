#!/usr/bin/env python3
"""
測試編碼和多語言支持

這個腳本用於測試 DBI Transfer Tool 在不同語言環境下的顯示效果
"""
import sys
import os
import locale

# 添加源碼路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'source'))

def test_console_encoding():
    """測試控制台編碼"""
    print("=" * 60)
    print("🧪 控制台編碼測試")
    print("=" * 60)
    
    # 顯示系統信息
    print(f"🖥️  操作系統: {sys.platform}")
    print(f"🐍 Python 版本: {sys.version.split()[0]}")
    
    # 顯示編碼信息
    try:
        print(f"📝 默認編碼: {sys.getdefaultencoding()}")
        print(f"📤 標準輸出編碼: {sys.stdout.encoding}")
        print(f"📥 標準錯誤編碼: {sys.stderr.encoding}")
        print(f"🌍 文件系統編碼: {sys.getfilesystemencoding()}")
        
        # 顯示語言環境
        default_locale = locale.getdefaultlocale()
        print(f"🗣️  默認語言環境: {default_locale}")
        
        current_locale = locale.getlocale()
        print(f"🌐 當前語言環境: {current_locale}")
        
    except Exception as e:
        print(f"❌ 獲取編碼信息失敗: {e}")
    
    print()

def test_chinese_characters():
    """測試中文字符顯示"""
    print("=" * 60)
    print("🈳 中文字符顯示測試")
    print("=" * 60)
    
    # 測試各種中文字符
    test_strings = [
        "🚀 DBI Transfer Tool",
        "📋 繁體中文測試：應用程式執行完成",
        "📋 简体中文测试：应用程序执行完成",
        "🎯 特殊符號：①②③④⑤⑥⑦⑧⑨⑩",
        "📁 文件路徑：C:\\測試\\文件夾\\檔案.txt",
        "📁 文件路径：C:\\测试\\文件夹\\文件.txt",
        "⚠️  警告信息：程式被使用者中斷",
        "⚠️  警告信息：程序被用户中断",
        "❌ 錯誤信息：配置文件不存在",
        "❌ 错误信息：配置文件不存在",
        "✅ 成功信息：構建完成",
        "✅ 成功信息：构建完成",
    ]
    
    for i, test_str in enumerate(test_strings, 1):
        try:
            print(f"{i:2d}. {test_str}")
        except UnicodeEncodeError as e:
            print(f"{i:2d}. ❌ 編碼錯誤: {e}")
        except Exception as e:
            print(f"{i:2d}. ❌ 其他錯誤: {e}")
    
    print()

def test_message_system():
    """測試消息系統"""
    print("=" * 60)
    print("💬 多語言消息系統測試")
    print("=" * 60)
    
    try:
        from utils.messages import (
            get_message, get_language, 
            is_simplified_chinese, is_traditional_chinese,
            format_startup_message, format_test_item_message
        )
        
        print(f"🌍 檢測到的語言: {get_language()}")
        print(f"🇨🇳 是否簡體中文: {is_simplified_chinese()}")
        print(f"🇹🇼 是否繁體中文: {is_traditional_chinese()}")
        print()
        
        # 測試各種消息
        test_messages = [
            'app_name',
            'app_description',
            'execution_complete',
            'results_saved',
            'check_files',
            'press_any_key',
            'user_interrupted',
            'startup_failed',
            'execution_error',
            'usage_title',
            'examples_title',
        ]
        
        print("📋 消息測試:")
        for msg_key in test_messages:
            try:
                message = get_message(msg_key)
                print(f"  {msg_key}: {message}")
            except Exception as e:
                print(f"  {msg_key}: ❌ 錯誤 - {e}")
        
        print()
        print("🎯 格式化消息測試:")
        print(f"  啟動消息: {format_startup_message('1.0.0')}")
        print(f"  測試項目: {format_test_item_message('test_item')}")
        
    except ImportError as e:
        print(f"❌ 無法導入消息系統: {e}")
    except Exception as e:
        print(f"❌ 消息系統測試失敗: {e}")
    
    print()

def test_application_import():
    """測試應用程序導入"""
    print("=" * 60)
    print("📦 應用程序導入測試")
    print("=" * 60)
    
    try:
        # 測試導入應用程序
        from application import Application
        
        print("✅ 應用程序導入成功")
        
        # 測試獲取應用信息
        info = Application.get_info()
        print(f"📋 應用名稱: {info.get('name', 'N/A')}")
        print(f"📝 應用描述: {info.get('description', 'N/A')}")
        print(f"🔢 版本號: {info.get('version', 'N/A')}")
        print(f"👤 作者: {info.get('author', 'N/A')}")
        
    except ImportError as e:
        print(f"❌ 應用程序導入失敗: {e}")
    except Exception as e:
        print(f"❌ 應用程序測試失敗: {e}")
    
    print()

def test_encoding_scenarios():
    """測試各種編碼場景"""
    print("=" * 60)
    print("🔤 編碼場景測試")
    print("=" * 60)
    
    # 測試文件名編碼
    test_filenames = [
        "測試文件.txt",
        "测试文件.txt", 
        "test_file.txt",
        "檔案名稱.rgt",
        "文件名称.rgt",
    ]
    
    print("📁 文件名編碼測試:")
    for filename in test_filenames:
        try:
            encoded = filename.encode('utf-8')
            decoded = encoded.decode('utf-8')
            print(f"  ✅ {filename} -> UTF-8 -> {decoded}")
        except Exception as e:
            print(f"  ❌ {filename} -> 編碼失敗: {e}")
    
    print()
    
    # 測試路徑編碼
    test_paths = [
        "C:\\程式\\DBI_Transfer\\測試.exe",
        "C:\\程序\\DBI_Transfer\\测试.exe",
        "/usr/local/bin/測試程式",
        "/usr/local/bin/测试程序",
    ]
    
    print("🛤️  路徑編碼測試:")
    for path in test_paths:
        try:
            from pathlib import Path
            path_obj = Path(path)
            print(f"  ✅ {path} -> Path 對象創建成功")
        except Exception as e:
            print(f"  ❌ {path} -> Path 創建失敗: {e}")
    
    print()

def main():
    """主函數"""
    print("🧪 DBI Transfer Tool 編碼和多語言支持測試")
    print("=" * 60)
    print()
    
    # 運行各項測試
    test_console_encoding()
    test_chinese_characters()
    test_message_system()
    test_application_import()
    test_encoding_scenarios()
    
    print("=" * 60)
    print("🎉 測試完成！")
    print("=" * 60)
    print()
    print("💡 如果看到亂碼，請檢查:")
    print("  1. 控制台是否支持 UTF-8 編碼")
    print("  2. 字體是否支持中文字符")
    print("  3. 系統語言設置是否正確")
    print()
    print("按任意鍵退出...")
    
    try:
        input()
    except (KeyboardInterrupt, EOFError):
        pass

if __name__ == "__main__":
    main()
