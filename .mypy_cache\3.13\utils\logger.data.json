{".class": "MypyFile", "_fullname": "utils.logger", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FilePaths": {".class": "SymbolTableNode", "cross_ref": "constant.FilePaths", "kind": "Gdef"}, "LogLevel": {".class": "SymbolTableNode", "cross_ref": "type_definitions.LogLevel", "kind": "Gdef"}, "LogLevels": {".class": "SymbolTableNode", "cross_ref": "constant.LogLevels", "kind": "Gdef"}, "LogPrinter": {".class": "SymbolTableNode", "cross_ref": "logPrinter.LogPrinter", "kind": "Gdef"}, "Loggable": {".class": "SymbolTableNode", "cross_ref": "type_definitions.Loggable", "kind": "Gdef"}, "LoggerFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "utils.logger.LoggerFactory", "name": "LoggerFactory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "utils.logger.LoggerFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "utils.logger", "mro": ["utils.logger.LoggerFactory", "builtins.object"], "names": {".class": "SymbolTable", "create_logger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["params", "log_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "utils.logger.LoggerFactory.create_logger", "name": "create_logger", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["params", "log_name"], "arg_types": ["parameterManger.ParameterManager", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_logger of LoggerFactory", "ret_type": "type_definitions.Loggable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "utils.logger.LoggerFactory.create_logger", "name": "create_logger", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["params", "log_name"], "arg_types": ["parameterManger.ParameterManager", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_logger of LoggerFactory", "ret_type": "type_definitions.Loggable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "utils.logger.LoggerFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "utils.logger.LoggerFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParameterManager": {".class": "SymbolTableNode", "cross_ref": "parameterManger.ParameterManager", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.logger.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.logger.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.logger.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.logger.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.logger.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.logger.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\utils\\logger.py"}