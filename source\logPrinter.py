from datetime import datetime
from typing import Optional, <PERSON>, Tuple, Any, TextIO, Literal
from time import time
import re

class LogPrinter:
    def __init__(self) -> None:
        self.level_stack: List[Tuple[int, float]] = []
        self.level_enabled = {
            "DEBUG": False,
            "INFO": True,
            "NOTE": True,
            "WARN": True,
            "ERROR": True,
        }
        self.color = True
        self.file_log_enabled = False
        self.file_log_path: Optional[str] = None
        self.file_log_handle: Optional[Any] = None
    
    def enable_file_log(self, path: str) -> None:
        self.file_log_enabled = True
        self.file_log_path = path
        self.file_log_handle = open(path, "w", encoding="utf-8")

    def disable_file_log(self) -> None:
        if self.file_log_handle:
            self.file_log_handle.close()
        self.file_log_enabled = False
        self.file_log_path = None
        self.file_log_handle = None

    def enable(self, level: str) -> None:
        self.level_enabled[level.upper()] = True

    def disable(self, level: str) -> None:
        self.level_enabled[level.upper()] = False

    def _now(self) -> str:
        return datetime.now().strftime("%H:%M:%S")

    def _get_prefix(self, indent: int, state: str, offset: int = 0) -> str:
        symbols = []
        for i in range(indent - offset):
            symbols.append("│   " if i < len(self.level_stack) else "    ")

        if state == 'start':
            symbols.append("┌")
        elif state == 'end':
            symbols.append("└")
        else:
            symbols.append("│")

        return "".join(symbols)
    


    def _strip_ansi(self, text: str) -> str:
        ansi_escape = re.compile(r"\x1B\[[0-?]*[ -/]*[@-~]")
        return ansi_escape.sub("", text)
    
    def _colorize(self, level: str, text: str) -> str:
        # ANSI 顏色碼，依照層級回傳彩色文字
        colors = {
            "ERROR": "\033[31m",   # 紅色
            "NOTE": "\033[32m",    # 綠色
            "INFO": "\033[34m",    # 藍色
            "WARN": "\033[33m",    # 黃色
            "DEBUG": "\033[38;5;136m",  # 暗金色
        }
        reset = "\033[0m"
        color_code = colors.get(level.upper(), "")
        return f"{color_code}{text}{reset}" if color_code else text

    def _print(self, level: str, message: str) -> None:
        # 加上時間戳
        timestamp = datetime.now().strftime("[%H:%M:%S]")
        formatted = f"{timestamp}[{level:<5}]    {message}"

        # 終端彩色印出
        if self.color:
            formatted_colored = self._colorize(level, formatted)
            print(formatted_colored)
        else:
            print(formatted)

        # 同步寫入檔案（去除顏色碼）
        if self.file_log_enabled and self.file_log_handle:
            clean_text = self._strip_ansi(formatted)
            self.file_log_handle.write(clean_text + "\n")
            self.file_log_handle.flush()


    def enter(self, message: str, level: str = "NOTE") -> None:
        indent = len(self.level_stack)
        self.level_stack.append((indent, time()))
        prefix = self._get_prefix(indent, state="start")
        self._print(level, prefix + " " + message)

    def leave(self, message: Optional[str] = None, level: str = "NOTE") -> None:
        if not self.level_stack:
            indent = 0
            start_time = None
        else:
            indent, start_time = self.level_stack.pop()

        duration_str = ""
        if start_time is not None:
            elapsed = time() - start_time
            duration_str = f" ({elapsed * 1000:.02f}ms)"

        if message is None:
            message = "Done"

        prefix = self._get_prefix(indent, state="end")
        self._print(level, prefix + " " + message + duration_str)

    def step(self, message: str, level: str = "NOTE") -> None:
        indent = len(self.level_stack)
        prefix = self._get_prefix(indent, state="mid", offset=1)
        self._print(level, prefix + "   " + message)

    def log(self, message: str, level: str = "INFO") -> None:
        if not self.level_enabled.get(level, True):
            return
        indent = len(self.level_stack)  # 取得目前縮排層級
        prefix = self._get_prefix(indent, state="mid", offset=1)
        self._print(level, prefix + message)

    def section(self, message: str, level: str = "NOTE") -> Any:
        class LogContext:
            def __init__(self, logger: 'LogPrinter', msg: str, lvl: str) -> None:
                self.logger = logger
                self.msg = msg
                self.level = lvl

            def __enter__(self) -> None:
                self.logger.enter(self.msg, self.level)

            def __exit__(self, _exc_type: Any, _exc_val: Any, _exc_tb: Any) -> Literal[False]:
                self.logger.leave(f"完成：{self.msg}", self.level)
                return False

        return LogContext(self, message, level)
