// Author: zhili
// -----------------------------------------------------------------------------
// 配置这个pattern的名称为OTP_PGM1，在生成vector的时候会以这个名称为标准，默认为idle
#set_title OPEN_DDI_P_MODE

// -----------------------------------------------------------------------------
// 配置寄存器接口为DBI，目前只支持这一种配置
#set_regif dbi

// -----------------------------------------------------------------------------
// 添加一行log，log会在生成的vector中以注释的形式存在
#log This configue to change TP_boot_state to DDI_PRGM
RFD 5A 5A//MCS_LOCK
R9F 02
R9B 11
RB8 5A
R9B 00//PMODE LOCK ON
R17 01

#log read back
R17 #1 01 Read 1 byte from GR00.17 and chek
delay 2

#log finish