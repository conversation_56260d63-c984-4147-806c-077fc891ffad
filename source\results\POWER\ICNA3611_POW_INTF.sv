task open_ddi_p_mode;
begin


    $display("This configue to change TP_boot_state to DDI_PRGM");
    ext_dbi_write(8'd03, 8'hFD, {8'h5A, 8'h5A});
    ext_dbi_write(8'd02, 8'h9F, {8'h02});
    ext_dbi_write(8'd02, 8'h9B, {8'h11});
    ext_dbi_write(8'd02, 8'hB8, {8'h5A});
    ext_dbi_write(8'd02, 8'h9B, {8'h00});
    ext_dbi_write(8'd02, 8'h17, {8'h01});
    $display("read back");
    ext_dbi_read_and_compare(8'h17, 8'd02, "Read 1 byte from GR00.17 and chek", 
                                           '{8'h01});
    #2_000_000ns;
    $display("finish");
    $display("This configue to change TP_boot_state to DDI_PRGM");
    ext_dbi_write(8'd03, 8'hFD, {8'h5A, 8'h5A});
    ext_dbi_write(8'd02, 8'h9F, {8'h02});
    ext_dbi_write(8'd02, 8'h9B, {8'h11});
    ext_dbi_write(8'd02, 8'hB8, {8'h5A});
    ext_dbi_write(8'd02, 8'h9B, {8'h00});
    ext_dbi_write(8'd02, 8'h17, {8'h01});
    $display("read back");
    ext_dbi_read_and_compare(8'h17, 8'd02, "Read 1 byte from GR00.17 and chek", 
                                           '{8'h01});
    #2_000_000ns;
    $display("finish");
    ext_dbi_write(8'd02, 8'h1A, {8'h01});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h30, 8'h05});
    ext_dbi_write(8'd03, 8'h1D, {8'h06, 8'h03});
    ext_dbi_write(8'd02, 8'h1C, {8'h00});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h30, 8'h07});
    ext_dbi_write(8'd03, 8'h1D, {8'h67, 8'hC0});
    ext_dbi_write(8'd02, 8'h1C, {8'h00});
    #20_000ns;
    pin_read_and_compare(GPO0, 1, "");
    pin_read_and_compare(GPO1, 1, "");
    ext_dbi_write(8'd03, 8'h1D, {8'h67, 8'hC1});
    ext_dbi_write(8'd02, 8'h1C, {8'h00});
    #20_000ns;
    pin_read_and_compare(GPO0, 1, "");
    pin_read_and_compare(GPO1, 1, "");
    #10ns; // Finish
end
endtask