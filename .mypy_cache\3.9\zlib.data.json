{".class": "MypyFile", "_fullname": "zlib", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DEFLATED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 8, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.DEFLATED", "name": "DEFLATED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "DEF_BUF_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 16384, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.DEF_BUF_SIZE", "name": "DEF_BUF_SIZE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16384}, "type_ref": "builtins.int"}}}, "DEF_MEM_LEVEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zlib.DEF_MEM_LEVEL", "name": "DEF_MEM_LEVEL", "setter_type": null, "type": "builtins.int"}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MAX_WBITS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zlib.MAX_WBITS", "name": "MAX_WBITS", "setter_type": null, "type": "builtins.int"}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ZLIB_RUNTIME_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zlib.ZLIB_RUNTIME_VERSION", "name": "ZLIB_RUNTIME_VERSION", "setter_type": null, "type": "builtins.str"}}, "ZLIB_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zlib.ZLIB_VERSION", "name": "ZLIB_VERSION", "setter_type": null, "type": "builtins.str"}}, "Z_BEST_COMPRESSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 9, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_BEST_COMPRESSION", "name": "Z_BEST_COMPRESSION", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, "type_ref": "builtins.int"}}}, "Z_BEST_SPEED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_BEST_SPEED", "name": "Z_BEST_SPEED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "Z_BLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 5, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_BLOCK", "name": "Z_BLOCK", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "Z_DEFAULT_COMPRESSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": -1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_DEFAULT_COMPRESSION", "name": "Z_DEFAULT_COMPRESSION", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": -1}, "type_ref": "builtins.int"}}}, "Z_DEFAULT_STRATEGY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_DEFAULT_STRATEGY", "name": "Z_DEFAULT_STRATEGY", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "Z_FILTERED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_FILTERED", "name": "Z_FILTERED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "Z_FINISH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_FINISH", "name": "Z_FINISH", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "Z_FIXED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_FIXED", "name": "Z_FIXED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "Z_FULL_FLUSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 3, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_FULL_FLUSH", "name": "Z_FULL_FLUSH", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "Z_HUFFMAN_ONLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_HUFFMAN_ONLY", "name": "Z_HUFFMAN_ONLY", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "Z_NO_COMPRESSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_NO_COMPRESSION", "name": "Z_NO_COMPRESSION", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "Z_NO_FLUSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_NO_FLUSH", "name": "Z_NO_FLUSH", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "Z_PARTIAL_FLUSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_PARTIAL_FLUSH", "name": "Z_PARTIAL_FLUSH", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "Z_RLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 3, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_RLE", "name": "Z_RLE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "Z_SYNC_FLUSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_SYNC_FLUSH", "name": "Z_SYNC_FLUSH", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "Z_TREES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 6, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "zlib.Z_TREES", "name": "Z_TREES", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_ref": "builtins.int"}}}, "_Compress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "zlib._Compress", "name": "_Compress", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "zlib._Compress", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "zlib", "mro": ["zlib._Compress", "builtins.object"], "names": {".class": "SymbolTable", "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zlib._Compress.__copy__", "name": "__copy__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zlib._Compress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zlib._Compress", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__copy__ of _Compress", "ret_type": "zlib._Compress", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zlib._Compress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zlib._Compress", "values": [], "variance": 0}]}}}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zlib._Compress.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zlib._Compress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zlib._Compress", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__deepcopy__ of _Compress", "ret_type": "zlib._Compress", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zlib._Compress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zlib._Compress", "values": [], "variance": 0}]}}}, "compress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "zlib._Compress.compress", "name": "compress", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["zlib._Compress", "typing_extensions.Buffer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compress of _Compress", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "zlib._Compress.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["zlib._Compress"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "copy of _Compress", "ret_type": "zlib._Compress", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "zlib._Compress.flush", "name": "flush", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["zlib._Compress", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "flush of _Compress", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zlib._Compress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zlib._Compress", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Decompress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "zlib._Decompress", "name": "_Decompress", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "zlib._Decompress", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "zlib", "mro": ["zlib._Decompress", "builtins.object"], "names": {".class": "SymbolTable", "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zlib._Decompress.__copy__", "name": "__copy__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zlib._Decompress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zlib._Decompress", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__copy__ of _Decompress", "ret_type": "zlib._Decompress", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zlib._Decompress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zlib._Decompress", "values": [], "variance": 0}]}}}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zlib._Decompress.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zlib._Decompress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zlib._Decompress", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__deepcopy__ of _Decompress", "ret_type": "zlib._Decompress", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zlib._Decompress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zlib._Decompress", "values": [], "variance": 0}]}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "zlib._Decompress.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["zlib._Decompress"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "copy of _Decompress", "ret_type": "zlib._Decompress", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decompress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, "max_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "zlib._Decompress.decompress", "name": "decompress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, "max_length"], "arg_types": ["zlib._Decompress", "typing_extensions.Buffer", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "decompress of _Decompress", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "zlib._Decompress.eof", "name": "eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["zlib._Decompress"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "eof of _Decompress", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "zlib._Decompress.eof", "name": "eof", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["zlib._Decompress"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "eof of _Decompress", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "zlib._Decompress.flush", "name": "flush", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["zlib._Decompress", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "flush of _Decompress", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unconsumed_tail": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "zlib._Decompress.unconsumed_tail", "name": "unconsumed_tail", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["zlib._Decompress"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "unconsumed_tail of _Decompress", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "zlib._Decompress.unconsumed_tail", "name": "unconsumed_tail", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["zlib._Decompress"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "unconsumed_tail of _Decompress", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "unused_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "zlib._Decompress.unused_data", "name": "unused_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["zlib._Decompress"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "unused_data of _Decompress", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "zlib._Decompress.unused_data", "name": "unused_data", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["zlib._Decompress"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "unused_data of _Decompress", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zlib._Decompress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zlib._Decompress", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zlib.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zlib.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zlib.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zlib.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zlib.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zlib.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "adler32": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zlib.adler32", "name": "adler32", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["typing_extensions.Buffer", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adler32", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zlib.compress", "name": "compress", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, "level"], "arg_types": ["typing_extensions.Buffer", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compress", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compressobj": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1], "arg_names": ["level", "method", "wbits", "memLevel", "strategy", "zdict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zlib.compressobj", "name": "compressobj", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1], "arg_names": ["level", "method", "wbits", "memLevel", "strategy", "zdict"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["typing_extensions.Buffer", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compressobj", "ret_type": "zlib._Compress", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "crc32": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zlib.crc32", "name": "crc32", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["typing_extensions.Buffer", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "crc32", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decompress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": [null, "wbits", "bufsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zlib.decompress", "name": "decompress", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": [null, "wbits", "bufsize"], "arg_types": ["typing_extensions.Buffer", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "decompress", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decompressobj": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["wbits", "zdict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zlib.decompressobj", "name": "decompressobj", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["wbits", "zdict"], "arg_types": ["builtins.int", "typing_extensions.Buffer"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "decompressobj", "ret_type": "zlib._Decompress", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "zlib.error", "name": "error", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "zlib.error", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "zlib", "mro": ["zlib.error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zlib.error.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zlib.error", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\zlib.pyi"}