{"data_mtime": 1750671873, "dep_lines": [4, 5, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30], "dependencies": ["validation.version_validator", "validation.error_handler", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "c3102f39295f28bbff3f19f5be5d8fba8edd5d9d", "id": "validation", "ignore_all": false, "interface_hash": "0c2242b6db37044965ff53507d998aef66eef15f", "mtime": 1750524075, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\validation\\__init__.py", "plugin_data": null, "size": 257, "suppressed": [], "version_id": "1.16.1"}