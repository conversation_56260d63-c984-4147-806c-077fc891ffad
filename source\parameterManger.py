from typing import Any

class ParameterManager:
    """
    從參數檔讀取 key=value，並可透過屬性訪問
    """

    def __init__(self, params_file: str) -> None:
        self.params: dict[str, str] = {}
        self.load_params(params_file)

    def load_params(self, params_file: str) -> None:
        with open(params_file, "r", encoding="utf-8") as file:
            for line in file:
                if "=" in line:
                    key, value = line.strip().split("=", maxsplit=1)
                    self.params[key] = value
                    setattr(self, key, value)

    def __enter__(self) -> "ParameterManager":
        return self

    def __exit__(self, exc_type: Any, exc_value: Any, traceback: Any) -> None:
        pass

    def __getattr__(self, name: str) -> str:
        if name in self.params:
            return self.params[name]
        raise AttributeError(f"'ParameterManager' object has no attribute '{name}'")