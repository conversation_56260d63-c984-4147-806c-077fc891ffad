"""
測試日誌打印器
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch, mock_open
from pathlib import Path
from io import StringIO

# 添加源碼路徑
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'source'))

from logPrinter import LogPrinter


class TestLogPrinter:
    """測試日誌打印器"""
    
    def test_init_default(self):
        """測試默認初始化"""
        logger = LogPrinter()

        assert logger.level_stack == []
        assert logger.level_enabled["INFO"] is True
        assert logger.level_enabled["DEBUG"] is False
        assert logger.color is True
        assert logger.file_log_enabled is False
        assert logger.file_log_path is None

    def test_enable_file_log(self):
        """測試啟用文件日誌"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
            log_file = f.name

        try:
            logger = LogPrinter()
            logger.enable_file_log(log_file)

            assert logger.file_log_enabled is True
            assert logger.file_log_path == log_file
            assert logger.file_log_handle is not None

            # 清理
            logger.disable_file_log()
        finally:
            if os.path.exists(log_file):
                os.unlink(log_file)
    
    @patch('sys.stdout', new_callable=StringIO)
    def test_log_to_console(self, mock_stdout):
        """測試控制台日誌輸出"""
        logger = LogPrinter()

        logger.log("Test message", level="INFO")

        output = mock_stdout.getvalue()
        assert "Test message" in output
        assert "INFO" in output

    def test_log_to_file(self):
        """測試文件日誌輸出"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
            log_file = f.name

        try:
            logger = LogPrinter()
            logger.enable_file_log(log_file)

            # 啟用 DEBUG 級別
            logger.enable("DEBUG")
            logger.log("Test file message", level="DEBUG")

            # 關閉文件日誌以確保內容被寫入
            logger.disable_file_log()

            # 檢查文件內容
            with open(log_file, 'r', encoding='utf-8') as rf:
                content = rf.read()
                assert "Test file message" in content
                assert "DEBUG" in content
        finally:
            if os.path.exists(log_file):
                os.unlink(log_file)
    
    @patch('sys.stdout', new_callable=StringIO)
    def test_log_different_levels(self, mock_stdout):
        """測試不同日誌級別"""
        logger = LogPrinter()

        # 啟用所有級別
        logger.enable("DEBUG")

        levels = ["DEBUG", "INFO", "WARN", "ERROR"]

        for level in levels:
            logger.log(f"Test {level} message", level=level)

        output = mock_stdout.getvalue()
        for level in levels:
            assert f"Test {level} message" in output
            assert level in output

    @patch('sys.stdout', new_callable=StringIO)
    def test_log_with_section(self, mock_stdout):
        """測試帶區段的日誌"""
        logger = LogPrinter()

        with logger.section("Test Section"):
            logger.log("Inside section", level="INFO")

        output = mock_stdout.getvalue()
        assert "Test Section" in output
        assert "Inside section" in output
    
    def test_enable_disable_levels(self):
        """測試啟用/禁用日誌級別"""
        logger = LogPrinter()

        # 測試禁用級別
        logger.disable("INFO")
        assert logger.level_enabled["INFO"] is False

        # 測試啟用級別
        logger.enable("INFO")
        assert logger.level_enabled["INFO"] is True

    def test_section_stack(self):
        """測試區段堆疊"""
        logger = LogPrinter()

        initial_stack_size = len(logger.level_stack)

        with logger.section("Test Section"):
            # 進入區段後堆疊應該增加
            assert len(logger.level_stack) == initial_stack_size + 1

        # 離開區段後堆疊應該恢復
        assert len(logger.level_stack) == initial_stack_size
    
    @patch('sys.stdout', new_callable=StringIO)
    def test_nested_sections(self, mock_stdout):
        """測試嵌套區段"""
        logger = LogPrinter()

        with logger.section("Outer Section"):
            logger.log("Outer message", level="INFO")
            with logger.section("Inner Section"):
                logger.log("Inner message", level="INFO")

        output = mock_stdout.getvalue()
        assert "Outer Section" in output
        assert "Inner Section" in output
        assert "Outer message" in output
        assert "Inner message" in output

    def test_disable_file_log(self):
        """測試禁用文件日誌"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
            log_file = f.name

        try:
            logger = LogPrinter()
            logger.enable_file_log(log_file)

            # 禁用文件日誌
            logger.disable_file_log()

            assert logger.file_log_enabled is False
            assert logger.file_log_path is None
            assert logger.file_log_handle is None
        finally:
            if os.path.exists(log_file):
                os.unlink(log_file)

    @patch('sys.stdout', new_callable=StringIO)
    def test_log_level_filtering(self, mock_stdout):
        """測試日誌級別過濾"""
        logger = LogPrinter()

        # DEBUG 默認是禁用的
        logger.log("Debug message", level="DEBUG")
        logger.log("Info message", level="INFO")

        output = mock_stdout.getvalue()
        # DEBUG 消息不應該出現
        assert "Debug message" not in output
        # INFO 消息應該出現
        assert "Info message" in output

    @patch('sys.stdout', new_callable=StringIO)
    def test_colorize_functionality(self, mock_stdout):
        """測試顏色功能"""
        logger = LogPrinter()

        # 測試不同級別的顏色
        logger.log("Error message", level="ERROR")
        logger.log("Info message", level="INFO")

        output = mock_stdout.getvalue()
        assert "Error message" in output
        assert "Info message" in output

    def test_step_method(self):
        """測試 step 方法"""
        logger = LogPrinter()

        # step 方法應該不拋出異常
        logger.step("Test step message")

    def test_enter_leave_methods(self):
        """測試 enter 和 leave 方法"""
        logger = LogPrinter()

        # 測試 enter/leave 方法
        logger.enter("Starting process")
        logger.leave("Process completed")

        # 檢查堆疊是否正確管理
        assert len(logger.level_stack) == 0


class TestLogPrinterIntegration:
    """測試日誌打印器集成"""

    def test_real_world_usage(self):
        """測試真實世界使用場景"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
            log_file = f.name

        try:
            logger = LogPrinter()
            logger.enable_file_log(log_file)

            # 模擬真實使用場景
            with logger.section("Processing File"):
                logger.log("Starting file processing", level="INFO")

                with logger.section("Validation"):
                    logger.log("Validation passed", level="INFO")

                with logger.section("Transformation"):
                    logger.log("Transforming data", level="INFO")
                    logger.log("Transformation complete", level="INFO")

                logger.log("File processing complete", level="INFO")

            # 關閉文件日誌
            logger.disable_file_log()

            # 檢查日誌文件
            with open(log_file, 'r', encoding='utf-8') as rf:
                content = rf.read()
                assert "Processing File" in content
                assert "Validation" in content
                assert "Transformation" in content
                assert "Starting file processing" in content
                assert "File processing complete" in content
        finally:
            if os.path.exists(log_file):
                os.unlink(log_file)
