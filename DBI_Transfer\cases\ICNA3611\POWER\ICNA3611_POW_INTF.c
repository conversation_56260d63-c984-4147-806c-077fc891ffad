//Test the connection of power between DDI and TP.
//power pin : tp2ddi_pow_ctrl_release, ddi2tp_pow_ctrl_request, dd12tp_pow_ctl_ready, ddi_avdd_ready
//#include open_ddi_p_mode.op2.txt
//73005=03//WO
REG8(0x073005)=0X03;
//73006=06//WO
REG8(0x073006)=0X06;
//73008=67//WO
REG8(0x073008)=0X67;
//設置 IO0=tp2ddi_pow_ctrl_release(1), IO1=ddi2tp_pow_ctrl_request(1)
//IO0: 7th bit, IO1: 6th bit
//73007=C0//WO
REG8(0x073007)=0XC0;
//delay 0.02
//量測 IO0
//#get_pin GPO0 1
//量測 IO1
//#get_pin GPO1 1
//設置 IO0=ddi2tp_pow_ctrl_ready(1), IO1=ddi_avdd_ready(1)
//IO0: 7th bit, IO1: 6th bit
//73007=C1//WO
REG8(0x073007)=0XC1;
//delay 0.02
//量測 IO0
//#get_pin GPO0 1
//量測 IO1
//#get_pin GPO1 1
//發送 11
//測試結束
