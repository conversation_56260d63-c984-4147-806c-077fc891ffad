"""
工作流管理器 - 管理整個處理流程
"""
import sys
import os
from typing import Tuple

# 添加父目錄到路徑以支援直接執行
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from logPrinter import LogPrinter
from parameterManger import ParameterManager
from parsers.rgt_parser import RgtParser
from output.output_manager import OutputManager
from core.engine import DbiTransferEngine
from utils.file_utils import FileUtils
from utils.config import ConfigManager
from validation.version_validator import create_version_validator
from constant import FilePaths


class WorkflowManager:
    """工作流管理器 - 協調各個組件完成整個處理流程"""
    
    def __init__(self, logger: LogPrinter):
        self.logger = logger
        self.parser = RgtParser(logger)
        self.output_manager = OutputManager(logger)
        self.engine = DbiTransferEngine(logger)
    
    def validate_configuration(self, params: ParameterManager) -> bool:
        """驗證配置檔案"""
        validator = create_version_validator()
        is_compatible, message = validator.validate_compatibility(params)
        
        if not is_compatible:
            self.logger.log(f"配置檔案兼容性檢查失敗：{message}", level="ERROR")
            
            # 顯示詳細的兼容性資訊
            compatibility_info = validator.get_compatibility_info(params)
            self.logger.log(f"EXE版本：{compatibility_info['exe_version']}", level="INFO")
            self.logger.log(f"Para.txt版本：{compatibility_info['para_version']}", level="INFO")
            self.logger.log(f"支援的版本：{', '.join(compatibility_info['supported_versions'])}", level="INFO")
            
            # 顯示具體的檢查結果
            for check_name, check_result in compatibility_info['details'].items():
                status = "✓" if check_result['passed'] else "✗"
                self.logger.log(f"{status} {check_name}: {check_result.get('message', '檢查完成')}", level="INFO")
            
            input("按 Enter 鍵退出...")
            return False
        
        self.logger.log(f"配置檔案兼容性檢查通過：{message}", level="INFO")
        return True
    
    def process_rgt_file(self, params: ParameterManager, in_file: str) -> tuple:
        """處理 RGT 檔案"""
        rgt_path = f"{FilePaths.RGT_DIR}/{in_file}"
        commands = [cmd.strip() for cmd in params.commands.split(",")]
        return self.parser.parse_file(rgt_path, commands, params.proj)
    
    def generate_output(self, params: ParameterManager, test_item: str,
                       rgt_lines: list, decoded_lines: list, cases_folder: str) -> Tuple[str, str]:
        """生成輸出檔案"""
        in_file, out_file, c_file = self.output_manager.prepare_filenames(params.proj, test_item)
        output_folder = self.output_manager.get_output_folder(params.proj, cases_folder)
        title = self.output_manager.build_title(params, test_item)
        
        self.output_manager.write_output_files(
            output_folder, out_file, c_file, title, decoded_lines,
            int(params.output_width), rgt_lines, in_file, self.engine
        )
        
        return output_folder, out_file
    
    def run_batch_and_copy(self, params: ParameterManager, cases_folder: str, 
                          out_file: str, test_item: str) -> None:
        """執行批次處理並複製檔案"""
        with self.logger.section('Run batch and copy'):
            FileUtils.update_batch_file(params.proj, cases_folder, out_file)
            FileUtils.execute_batch_file()
            ConfigManager.copy_result_files(
                FilePaths.RESULTS_DIR, 
                f"{FilePaths.RESULTS_DIR}/{cases_folder}", 
                f"{params.proj}_{test_item}", 
                self.logger
            )
    
    def execute_workflow(self, params: ParameterManager, test_item: str) -> None:
        """執行完整的工作流程"""
        # 1. 驗證配置
        if not self.validate_configuration(params):
            return
        
        # 2. 準備檔案名稱
        in_file, out_file, c_file = self.output_manager.prepare_filenames(params.proj, test_item)
        
        # 3. 解析 RGT 檔案
        rgt_lines, decoded_lines, cases_folder = self.process_rgt_file(params, in_file)
        
        # 4. 生成輸出檔案
        output_folder, out_file = self.generate_output(
            params, test_item, rgt_lines, decoded_lines, cases_folder
        )
        
        # 5. 執行批次處理
        self.run_batch_and_copy(params, cases_folder, out_file, test_item)
