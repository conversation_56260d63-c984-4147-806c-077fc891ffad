#!/usr/bin/env python3
"""
測試版本驗證機制
"""
import sys
import os

# 添加 source 目錄到路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'source'))

from validation.version_validator import create_version_validator, SemanticVersion
from parameterManger import ParameterManager


class MockParameterManager:
    """模擬的參數管理器，用於測試"""
    
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)


def test_semantic_version():
    """測試語義版本號解析"""
    print("=== 測試語義版本號解析 ===")
    
    # 測試正常版本號
    v1 = SemanticVersion("1.2.3")
    print(f"版本 '1.2.3' 解析為: {v1} (major={v1.major}, minor={v1.minor}, patch={v1.patch})")
    
    # 測試簡短版本號
    v2 = SemanticVersion("0.01")
    print(f"版本 '0.01' 解析為: {v2} (major={v2.major}, minor={v2.minor}, patch={v2.patch})")
    
    # 測試兼容性
    exe_version = SemanticVersion("1.2.0")
    para_version = SemanticVersion("1.1.5")
    compatible = exe_version.is_compatible_with(para_version)
    print(f"EXE {exe_version} 與 Para {para_version} 兼容: {compatible}")
    
    print()


def test_valid_config():
    """測試有效的配置"""
    print("=== 測試有效配置 ===")
    
    # 創建有效的參數
    params = MockParameterManager(
        version="0.01",
        proj="ICNA3611",
        commands="set_pin, get_pin, set_title",
        output_width="100",
        author="Sychang",
        debug="1",
        info="1",
        warn="1",
        error="1",
        note="0"
    )
    
    validator = create_version_validator()
    is_compatible, message = validator.validate_compatibility(params)
    
    print(f"兼容性檢查結果: {is_compatible}")
    print(f"訊息: {message}")
    
    # 顯示詳細資訊
    info = validator.get_compatibility_info(params)
    print(f"詳細資訊: {info}")
    print()


def test_invalid_version():
    """測試無效版本"""
    print("=== 測試無效版本 ===")
    
    params = MockParameterManager(
        version="2.0.0",  # 不支援的版本
        proj="ICNA3611",
        commands="set_pin, get_pin",
        output_width="100",
        author="Sychang",
        debug="1",
        info="1",
        warn="1",
        error="1",
        note="0"
    )
    
    validator = create_version_validator()
    is_compatible, message = validator.validate_compatibility(params)
    
    print(f"兼容性檢查結果: {is_compatible}")
    print(f"訊息: {message}")
    print()


def test_missing_fields():
    """測試缺少欄位"""
    print("=== 測試缺少欄位 ===")
    
    params = MockParameterManager(
        version="0.01",
        proj="ICNA3611",
        # 缺少 commands, output_width, author
        debug="1",
        info="1"
        # 缺少其他日誌級別
    )
    
    validator = create_version_validator()
    is_compatible, message = validator.validate_compatibility(params)
    
    print(f"兼容性檢查結果: {is_compatible}")
    print(f"訊息: {message}")
    print()


def test_invalid_format():
    """測試無效格式"""
    print("=== 測試無效格式 ===")
    
    params = MockParameterManager(
        version="0.01",
        proj="ICNA3611",
        commands="set_pin, get_pin",
        output_width="abc",  # 無效的數字
        author="Sychang",
        debug="2",  # 無效的日誌級別
        info="1",
        warn="1",
        error="1",
        note="0"
    )
    
    validator = create_version_validator()
    is_compatible, message = validator.validate_compatibility(params)
    
    print(f"兼容性檢查結果: {is_compatible}")
    print(f"訊息: {message}")
    print()


def test_real_para_file():
    """測試真實的 para.txt 檔案"""
    print("=== 測試真實 para.txt 檔案 ===")
    
    try:
        # 嘗試載入真實的 para.txt
        with ParameterManager("source/para.txt") as params:
            validator = create_version_validator()
            is_compatible, message = validator.validate_compatibility(params)
            
            print(f"真實檔案兼容性檢查結果: {is_compatible}")
            print(f"訊息: {message}")
            
            if not is_compatible:
                info = validator.get_compatibility_info(params)
                print("詳細檢查結果:")
                for check_name, result in info['details'].items():
                    status = "✓" if result['passed'] else "✗"
                    print(f"  {status} {check_name}")
                    if not result['passed']:
                        if 'message' in result:
                            print(f"    錯誤: {result['message']}")
                        if 'missing_fields' in result:
                            print(f"    缺少欄位: {result['missing_fields']}")
                        if 'errors' in result:
                            print(f"    格式錯誤: {result['errors']}")
    
    except Exception as e:
        print(f"無法載入真實檔案: {e}")
    
    print()


if __name__ == "__main__":
    print("開始測試版本驗證機制...\n")
    
    test_semantic_version()
    test_valid_config()
    test_invalid_version()
    test_missing_fields()
    test_invalid_format()
    test_real_para_file()
    
    print("測試完成！")
