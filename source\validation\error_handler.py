"""
統一的錯誤處理機制
"""
import sys
import os
import traceback
from typing import Optional, Dict, Any, Callable, List
import datetime

# 添加父目錄到路徑以支援直接執行
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from logPrinter import LogPrinter
from type_definitions import Loggable, LogLevel


class DbiTransferError(Exception):
    """DBI Transfer 工具的基礎異常類"""
    pass


class FileNotFoundError(DbiTransferError):
    """檔案未找到錯誤"""
    pass


class FormatError(DbiTransferError):
    """格式錯誤"""
    pass


class DataValidationError(DbiTransferError):
    """資料驗證錯誤"""
    pass


class VersionMismatchError(DbiTransferError):
    """版本不匹配錯誤"""
    pass


class ErrorHandler:
    """增強的統一錯誤處理器"""

    def __init__(self, logger: Optional[Loggable] = None, testing_mode: bool = False):
        self.logger = logger
        self.error_count = 0
        self.warning_count = 0
        self.error_history: List[Dict[str, Any]] = []
        self.continue_on_error = False  # 是否在錯誤時繼續執行
        self.testing_mode = testing_mode  # 測試模式，跳過用戶輸入等待
    
    def handle_file_not_found(self, filename: str, proj: str) -> None:
        """處理檔案未找到錯誤"""
        error_msg = f"File {filename} not found. 應該以 {proj}_ 為前綴，例如 {proj}_xxx.rgt"
        if self.logger:
            self.logger.log(error_msg, level="ERROR")
        self._exit_with_input()
    
    def handle_format_error(self, line_num: int, line_content: str, reason: str = "") -> None:
        """處理格式錯誤"""
        error_msg = f"Line {line_num}, {line_content} format not supported."
        if reason:
            error_msg += f" Reason: {reason}"
        
        if self.logger:
            self.logger.log(error_msg, level="ERROR")
        self._exit_with_input()
    
    def handle_data_validation_error(self, line_num: int, data: str) -> None:
        """處理資料驗證錯誤"""
        error_msg = f"Line {line_num}, data is illegal: {data}"
        if self.logger:
            self.logger.log(error_msg, level="ERROR")
        self._exit_with_input()
    
    def handle_version_mismatch(self, expected: str, actual: str) -> None:
        """處理版本不匹配錯誤"""
        error_msg = f"Version mismatch. Expected: {expected}, Actual: {actual}"
        if self.logger:
            self.logger.log(error_msg, level="ERROR")
        self._exit_with_input()
    
    def handle_command_not_supported(self, line_num: int, line_content: str) -> None:
        """處理不支援的命令錯誤"""
        error_msg = f"Line {line_num}, command not supported: {line_content}"
        self._record_error("CommandNotSupported", error_msg, {
            'line_number': line_num,
            'line_content': line_content
        }, fatal=False)
        # 對於不支援的命令，我們只記錄但不退出

    def handle_error_with_context(self, error: Exception, context: Optional[Dict[str, Any]] = None,
                                 fatal: bool = True) -> bool:
        """
        通用錯誤處理方法

        Args:
            error: 異常對象
            context: 錯誤上下文信息
            fatal: 是否為致命錯誤

        Returns:
            bool: True 表示可以繼續執行，False 表示應該停止
        """
        error_type = type(error).__name__
        error_msg = str(error)

        # 記錄錯誤
        self._record_error(error_type, error_msg, context or {}, fatal)

        # 根據錯誤類型決定是否繼續
        if fatal and not self.continue_on_error:
            self._exit_with_input()
            return False

        return True

    def set_continue_on_error(self, continue_flag: bool) -> None:
        """設置是否在錯誤時繼續執行"""
        self.continue_on_error = continue_flag

    def _record_error(self, error_type: str, message: str, context: Dict[str, Any], fatal: bool) -> None:
        """記錄錯誤到歷史和日誌"""
        # 更新計數
        if fatal:
            self.error_count += 1
        else:
            self.warning_count += 1

        # 添加到歷史
        error_record = {
            'timestamp': datetime.datetime.now().isoformat(),
            'type': error_type,
            'message': message,
            'context': context,
            'fatal': fatal
        }
        self.error_history.append(error_record)

        # 記錄到日誌
        level: LogLevel = "ERROR" if fatal else "WARN"
        if self.logger:
            self.logger.log(message, level=level)

            # 記錄上下文信息
            if context:
                context_str = ", ".join(f"{k}={v}" for k, v in context.items())
                self.logger.log(f"Context: {context_str}", level="DEBUG")
        else:
            print(f"{level}: {message}")

    def get_error_summary(self) -> Dict[str, Any]:
        """獲取錯誤摘要"""
        error_types: Dict[str, int] = {}
        for record in self.error_history:
            error_type = record['type']
            error_types[error_type] = error_types.get(error_type, 0) + 1

        return {
            'total_errors': self.error_count,
            'total_warnings': self.warning_count,
            'error_types': error_types,
            'recent_errors': self.error_history[-5:] if self.error_history else [],
            'has_fatal_errors': any(record['fatal'] for record in self.error_history)
        }

    def clear_history(self) -> None:
        """清除錯誤歷史"""
        self.error_history.clear()
        self.error_count = 0
        self.warning_count = 0
    
    def _exit_with_input(self) -> None:
        """等待使用者輸入後退出"""
        # 在測試模式下跳過等待用戶輸入
        if not self.testing_mode:
            try:
                input("Press Enter to exit...")
            except (KeyboardInterrupt, EOFError):
                pass
        sys.exit(1)


def create_error_handler(logger: Optional[Loggable] = None, testing_mode: bool = False) -> ErrorHandler:
    """創建錯誤處理器的工廠函數"""
    return ErrorHandler(logger, testing_mode)
