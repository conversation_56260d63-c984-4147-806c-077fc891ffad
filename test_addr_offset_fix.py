#!/usr/bin/env python3
"""
測試地址偏移計算修復

驗證 engine.py 中地址偏移計算的修改是否正確
"""
import sys
import os
from io import StringIO
from unittest.mock import Mock

# 添加源碼路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'source'))

def test_addr_offset_calculation():
    """測試地址偏移計算修復"""
    print("🧪 測試地址偏移計算修復")
    print("=" * 50)
    
    try:
        from core.engine import DbiTransferEngine
        from constant import FieldIndex
        
        # 創建模擬的 logger
        mock_logger = Mock()
        engine = DbiTransferEngine(mock_logger)
        
        # 測試案例：不同資料長度的地址偏移計算
        test_cases = [
            {
                "name": "1字節資料",
                "address": "001234",
                "data": "AB",
                "data_length_bytes": 1,
                "expected_old": f"{0x1234 + 4 - 1:x}",  # 1237
                "expected_new": f"{0x1234:x}",          # 1234
            },
            {
                "name": "2字節資料", 
                "address": "001234",
                "data": "ABCD",
                "data_length_bytes": 2,
                "expected_old": f"{0x1234 + 4 - 2:x}",  # 1236
                "expected_new": f"{0x1234:x}",          # 1234
            },
            {
                "name": "4字節資料",
                "address": "001234", 
                "data": "ABCDEF12",
                "data_length_bytes": 4,
                "expected_old": f"{0x1234 + 4 - 4:x}",  # 1234
                "expected_new": f"{0x1234:x}",          # 1234
            }
        ]
        
        print("📋 測試案例:")
        for i, case in enumerate(test_cases, 1):
            print(f"\n{i}. {case['name']}")
            print(f"   地址: {case['address']}")
            print(f"   資料: {case['data']}")
            print(f"   資料長度: {case['data_length_bytes']} 字節")
            print(f"   舊邏輯結果: 0x{case['expected_old']}")
            print(f"   新邏輯結果: 0x{case['expected_new']}")
            
            # 檢查差異
            if case['expected_old'] != case['expected_new']:
                print(f"   ⚠️  修改有影響：{case['expected_old']} → {case['expected_new']}")
            else:
                print(f"   ✅ 修改無影響：結果相同")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_c_file_generation():
    """測試 C 文件生成"""
    print("\n🧪 測試 C 文件生成")
    print("=" * 50)
    
    try:
        from core.engine import DbiTransferEngine
        from constant import FieldIndex
        
        mock_logger = Mock()
        engine = DbiTransferEngine(mock_logger)
        
        # 測試案例
        test_cases = [
            {
                "name": "1字節寫入",
                "parsed_line": [None, "001234", "AB", "WO", "//Test 1 byte write"],
                "expected_pattern": "REG8(0x1234)=0XAB;"
            },
            {
                "name": "2字節寫入", 
                "parsed_line": [None, "001234", "ABCD", "WO", "//Test 2 byte write"],
                "expected_pattern": "REG16(0x1234)=0XABCD;"
            },
            {
                "name": "4字節寫入",
                "parsed_line": [None, "001234", "ABCDEF12", "WO", "//Test 4 byte write"], 
                "expected_pattern": "REG32(0x1234)=0XABCDEF12;"
            }
        ]
        
        print("📋 C 文件生成測試:")
        for i, case in enumerate(test_cases, 1):
            print(f"\n{i}. {case['name']}")
            
            # 創建模擬的 C 文件輸出
            c_file = StringIO()
            
            # 準備測試資料
            parsed_line = case["parsed_line"]
            address = parsed_line[FieldIndex.ADDR_IDX]
            data = parsed_line[FieldIndex.DATA_IDX]
            data_length_bytes = len(data) // 2
            
            # 調用 _generate_c_write_commands 方法
            engine._generate_c_write_commands(c_file, parsed_line, address, data, data_length_bytes)
            
            # 檢查輸出
            output = c_file.getvalue()
            print(f"   輸出: {output.strip()}")
            
            if case["expected_pattern"] in output:
                print(f"   ✅ 包含預期模式: {case['expected_pattern']}")
            else:
                print(f"   ❌ 缺少預期模式: {case['expected_pattern']}")
                print(f"   實際輸出: {output}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_3_byte_special_case():
    """測試 3 字節特殊情況"""
    print("\n🧪 測試 3 字節特殊情況")
    print("=" * 50)
    
    try:
        from core.engine import DbiTransferEngine
        from constant import FieldIndex
        
        mock_logger = Mock()
        engine = DbiTransferEngine(mock_logger)
        
        # 3字節資料測試
        parsed_line = [None, "001234", "ABCDEF", "WO", "//Test 3 byte write"]
        address = "001234"
        data = "ABCDEF"
        data_length_bytes = 3
        
        c_file = StringIO()
        
        print("📋 3字節資料測試:")
        print(f"   地址: {address}")
        print(f"   資料: {data}")
        print(f"   資料長度: {data_length_bytes} 字節")
        
        # 調用方法
        engine._generate_c_write_commands(c_file, parsed_line, address, data, data_length_bytes)
        
        output = c_file.getvalue()
        print(f"   輸出:")
        for line in output.strip().split('\n'):
            print(f"     {line}")
        
        # 檢查是否包含預期的分割操作
        expected_patterns = [
            "REG16(0x1235)",  # address + 1
            "REG8(0x1236)",   # address + 2
        ]
        
        for pattern in expected_patterns:
            if pattern in output:
                print(f"   ✅ 包含預期模式: {pattern}")
            else:
                print(f"   ❌ 缺少預期模式: {pattern}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_address_offset_impact():
    """測試地址偏移修改的實際影響"""
    print("\n🧪 測試地址偏移修改的實際影響")
    print("=" * 50)
    
    # 模擬舊邏輯和新邏輯的比較
    test_addresses = ["001234", "00ABCD", "001000", "00FFFF"]
    data_lengths = [1, 2, 4]
    
    print("📋 地址偏移比較:")
    print("地址     | 長度 | 舊邏輯(+4-len) | 新邏輯(+0) | 差異")
    print("-" * 55)
    
    for addr in test_addresses:
        addr_int = int(addr, 16)
        for length in data_lengths:
            old_offset = addr_int + 4 - length
            new_offset = addr_int
            diff = new_offset - old_offset
            
            print(f"{addr} | {length}字節 | 0x{old_offset:06X}     | 0x{new_offset:06X}   | {diff:+d}")
    
    print("\n💡 分析:")
    print("   - 新邏輯總是使用原始地址")
    print("   - 舊邏輯會根據資料長度調整地址")
    print("   - 1字節資料：差異 -3")
    print("   - 2字節資料：差異 -2") 
    print("   - 4字節資料：差異 0（無變化）")
    
    return True

def main():
    """主函數"""
    print("🔧 地址偏移計算修復驗證測試")
    print("=" * 60)
    print()
    
    # 運行測試
    tests = [
        ("地址偏移計算", test_addr_offset_calculation),
        ("C 文件生成", test_c_file_generation),
        ("3字節特殊情況", test_3_byte_special_case),
        ("地址偏移影響分析", test_address_offset_impact),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"\n✅ {test_name} 測試通過")
            else:
                failed += 1
                print(f"\n❌ {test_name} 測試失敗")
        except Exception as e:
            failed += 1
            print(f"\n❌ {test_name} 測試異常: {e}")
    
    # 顯示結果
    print("\n" + "=" * 60)
    print("📊 測試結果:")
    print(f"✅ 通過: {passed}")
    print(f"❌ 失敗: {failed}")
    print(f"📋 總計: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 所有測試通過！")
        print("💡 建議：將這些測試加入正式的測試套件中")
    else:
        print(f"\n⚠️  {failed} 個測試失敗，請檢查修改的影響。")
    
    return 0 if failed == 0 else 1

if __name__ == "__main__":
    sys.exit(main())
