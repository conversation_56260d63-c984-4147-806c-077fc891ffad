{".class": "MypyFile", "_fullname": "email._policybase", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Compat32": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Compat32", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email._policybase.Compat32", "name": "Compat32", "type_vars": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Compat32", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email._policybase.Compat32", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "email._policybase", "mro": ["email._policybase.Compat32", "email._policybase.Policy", "email._policybase._PolicyBase", "builtins.object"], "names": {".class": "SymbolTable", "fold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "email._policybase.Compat32.fold", "name": "fold", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Compat32", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Compat32"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fold of Compat32", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fold_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "email._policybase.Compat32.fold_binary", "name": "fold_binary", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Compat32", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Compat32"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fold_binary of Compat32", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "header_fetch_parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "email._policybase.Compat32.header_fetch_parse", "name": "header_fetch_parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Compat32", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Compat32"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "header_fetch_parse of Compat32", "ret_type": {".class": "UnionType", "items": ["builtins.str", "email.header.Header"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "header_source_parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sourcelines"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "email._policybase.Compat32.header_source_parse", "name": "header_source_parse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sourcelines"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Compat32", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Compat32"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "header_source_parse of Compat32", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "header_store_parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "email._policybase.Compat32.header_store_parse", "name": "header_store_parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Compat32", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Compat32"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "header_store_parse of Compat32", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["_MessageT"], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Header": {".class": "SymbolTableNode", "cross_ref": "email.header.Header", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Message": {".class": "SymbolTableNode", "cross_ref": "email.message.Message", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MessageDefect": {".class": "SymbolTableNode", "cross_ref": "email.errors.MessageDefect", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Policy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["fold", 1], ["fold_binary", 1], ["header_fetch_parse", 1], ["header_source_parse", 1], ["header_store_parse", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._PolicyBase"}], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "email._policybase.Policy", "name": "Policy", "type_vars": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "email._policybase.Policy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "email._policybase", "mro": ["email._policybase.Policy", "email._policybase._PolicyBase", "builtins.object"], "names": {".class": "SymbolTable", "fold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "email._policybase.Policy.fold", "name": "fold", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fold of Policy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "email._policybase.Policy.fold", "name": "fold", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fold of Policy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fold_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "email._policybase.Policy.fold_binary", "name": "fold_binary", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fold_binary of Policy", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "email._policybase.Policy.fold_binary", "name": "fold_binary", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fold_binary of Policy", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "handle_defect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "defect"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "email._policybase.Policy.handle_defect", "name": "handle_defect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "defect"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}, "email.errors.MessageDefect"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_defect of Policy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "header_fetch_parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "email._policybase.Policy.header_fetch_parse", "name": "header_fetch_parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "header_fetch_parse of Policy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "email._policybase.Policy.header_fetch_parse", "name": "header_fetch_parse", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "header_fetch_parse of Policy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "header_max_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "email._policybase.Policy.header_max_count", "name": "header_max_count", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "header_max_count of Policy", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "header_source_parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "sourcelines"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "email._policybase.Policy.header_source_parse", "name": "header_source_parse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sourcelines"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "header_source_parse of Policy", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "email._policybase.Policy.header_source_parse", "name": "header_source_parse", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sourcelines"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "header_source_parse of Policy", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "header_store_parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "email._policybase.Policy.header_store_parse", "name": "header_store_parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "header_store_parse of Policy", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "email._policybase.Policy.header_store_parse", "name": "header_store_parse", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "header_store_parse of Policy", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "register_defect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "defect"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "email._policybase.Policy.register_defect", "name": "register_defect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "defect"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}, {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase.Policy", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}, "email.errors.MessageDefect"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_defect of Policy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["_MessageT"], "typeddict_type": null}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_MessageFactory": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email._policybase._MessageFactory", "name": "_MessageFactory", "type_vars": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._MessageFactory", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "email._policybase._MessageFactory", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "email._policybase", "mro": ["email._policybase._MessageFactory", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "email._policybase._MessageFactory.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "policy"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._MessageFactory", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._MessageFactory"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._MessageFactory", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _MessageFactory", "ret_type": {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._MessageFactory", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["_MessageT"], "typeddict_type": null}}, "_MessageT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "name": "_MessageT", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}}, "_PolicyBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email._policybase._PolicyBase", "name": "_PolicyBase", "type_vars": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._PolicyBase", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email._policybase._PolicyBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email._policybase", "mro": ["email._policybase._PolicyBase", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email._policybase._PolicyBase.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email._policybase._PolicyBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._PolicyBase", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._PolicyBase"}, "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}], "extra_attrs": null, "type_ref": "email._policybase.Policy"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__add__ of _PolicyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email._policybase._PolicyBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._PolicyBase", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._PolicyBase"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email._policybase._PolicyBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._PolicyBase", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._PolicyBase"}, "values": [], "variance": 0}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "max_line_length", "linesep", "cte_type", "raise_on_defect", "mangle_from_", "message_factory", "verify_generated_headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "email._policybase._PolicyBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "max_line_length", "linesep", "cte_type", "raise_on_defect", "mangle_from_", "message_factory", "verify_generated_headers"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._PolicyBase", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._PolicyBase"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._PolicyBase", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._MessageFactory"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _PolicyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "max_line_length", "linesep", "cte_type", "raise_on_defect", "mangle_from_", "message_factory", "verify_generated_headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email._policybase._PolicyBase.clone", "name": "clone", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "max_line_length", "linesep", "cte_type", "raise_on_defect", "mangle_from_", "message_factory", "verify_generated_headers"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email._policybase._PolicyBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._PolicyBase", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._PolicyBase"}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._PolicyBase", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._MessageFactory"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clone of _PolicyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email._policybase._PolicyBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._PolicyBase", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._PolicyBase"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email._policybase._PolicyBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._PolicyBase", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._PolicyBase"}, "values": [], "variance": 0}]}}}, "cte_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "email._policybase._PolicyBase.cte_type", "name": "cte_type", "setter_type": null, "type": "builtins.str"}}, "linesep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "email._policybase._PolicyBase.linesep", "name": "linesep", "setter_type": null, "type": "builtins.str"}}, "mangle_from_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "email._policybase._PolicyBase.mangle_from_", "name": "mangle_from_", "setter_type": null, "type": "builtins.bool"}}, "max_line_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "email._policybase._PolicyBase.max_line_length", "name": "max_line_length", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "message_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "email._policybase._PolicyBase.message_factory", "name": "message_factory", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._PolicyBase", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._MessageFactory"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "raise_on_defect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "email._policybase._PolicyBase.raise_on_defect", "name": "raise_on_defect", "setter_type": null, "type": "builtins.bool"}}, "verify_generated_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "email._policybase._PolicyBase.verify_generated_headers", "name": "verify_generated_headers", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "email._policybase._PolicyBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": 1, "name": "_MessageT", "namespace": "email._policybase._PolicyBase", "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "email._policybase._PolicyBase"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_MessageT"], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email._policybase.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email._policybase.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email._policybase.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email._policybase.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email._policybase.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email._policybase.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email._policybase.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_hidden": true, "module_public": false}, "compat32": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email._policybase.compat32", "name": "compat32", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}], "extra_attrs": null, "type_ref": "email._policybase.Compat32"}}}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\email\\_policybase.pyi"}