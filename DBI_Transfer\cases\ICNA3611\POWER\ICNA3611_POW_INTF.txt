////////////////////////////////////////////////////////////////////////////////////////////////////
//Tool version : 0.01                                                                             //
//Proj         : ICNA3611                                                                         //
//Test Item    : POW_INTF                                                                         //
//Author       : Sychang                                                                          //
//Date         : 2025/06/23 13:29:12                                                              //
////////////////////////////////////////////////////////////////////////////////////////////////////
//;-------------------------------------------------------------------------------------------------
//;ICNA3611_POW_INTF.rgt start
//;//Test the connection of power between DDI and TP.                                             //
//;//power pin : tp2ddi_pow_ctrl_release, ddi2tp_pow_ctrl_request, dd12tp_pow_ctl_ready, ddi_avdd_ready//
//;FOLDER(POWER)                                                                                  //
//;#include open_ddi_p_mode.op2.txt                                                               //
//;                                                                                               //
//;//設置 IO0, IO1 來自 modectl/u01_power_seq_ctl                                                     //
//;73005=0603//WO                                                                                 //
//;                                                                                               //
//;//設置 IO0=tp2ddi_pow_ctrl_release(1), IO1=ddi2tp_pow_ctrl_request(1)                            //
//;//IO1: 7th bit, IO2: 6th bit                                                                   //
//;73007=67C0//WO                                                                                 //
//;delay 0.02                                                                                     //
//;//量測 IO0                                                                                       //
//;#get_pin GPO0 1                                                                                //
//;//量測 IO1                                                                                       //
//;#get_pin GPO1 1                                                                                //
//;                                                                                               //
//;//設置 IO0=ddi2tp_pow_ctrl_ready, IO1=ddi_avdd_ready                                             //
//;//設置 IO0=ddi2tp_pow_ctrl_ready(1), IO1=ddi_avdd_ready(1)                                       //
//;//IO1: 7th bit, IO2: 6th bit                                                                   //
//;73007=67C1//WO                                                                                 //
//;delay 0.02                                                                                     //
//;//量測 IO0                                                                                       //
//;#get_pin GPO0 1                                                                                //
//;//量測 IO1                                                                                       //
//;#get_pin GPO1 1                                                                                //
//;                                                                                               //
//;                                                                                               //
//;//發送 11                                                                                        //
//;                                                                                               //
//;//設置 IO1=tp2ddi_pow_ctrl_release, IO2=ddi2tp_pow_ctrl_request                                  //
//;                                                                                               //
//;//量測 IO1                                                                                       //
//;                                                                                               //
//;//量測 IO2                                                                                       //
//;                                                                                               //
//;//設置 IO1=ddi2tp_pow_ctrl_ready, IO2=ddi_avdd_ready                                             //
//;                                                                                               //
//;//量測 IO1                                                                                       //
//;                                                                                               //
//;//量測 IO2                                                                                       //
//;                                                                                               //
//;//測試結束                                                                                         //
//;ICNA3611_POW_INTF.rgt end
//;-------------------------------------------------------------------------------------------------
#include open_ddi_p_mode.op2.txt

//Test the connection of power between DDI and TP.
//power pin : tp2ddi_pow_ctrl_release, ddi2tp_pow_ctrl_request, dd12tp_pow_ctl_ready, ddi_avdd_ready
#include open_ddi_p_mode.op2.txt
//設置 IO0, IO1 來自 modectl/u01_power_seq_ctl
//73005=0603//WO
R1A 01
R1B 00 07 30 05
R1D 06 03
R1C 00

//設置 IO0=tp2ddi_pow_ctrl_release(1), IO1=ddi2tp_pow_ctrl_request(1)
//IO1: 7th bit, IO2: 6th bit
//73007=67C0//WO
//R1A 01
R1B 00 07 30 07
R1D 67 C0
R1C 00

delay 0.02
//量測 IO0
#get_pin GPO0 1
//量測 IO1
#get_pin GPO1 1
//設置 IO0=ddi2tp_pow_ctrl_ready, IO1=ddi_avdd_ready
//設置 IO0=ddi2tp_pow_ctrl_ready(1), IO1=ddi_avdd_ready(1)
//IO1: 7th bit, IO2: 6th bit
//73007=67C1//WO
//R1A 01
//R1B 00 07 30 07
R1D 67 C1
R1C 00

delay 0.02
//量測 IO0
#get_pin GPO0 1
//量測 IO1
#get_pin GPO1 1
//發送 11
//設置 IO1=tp2ddi_pow_ctrl_release, IO2=ddi2tp_pow_ctrl_request
//量測 IO1
//量測 IO2
//設置 IO1=ddi2tp_pow_ctrl_ready, IO2=ddi_avdd_ready
//量測 IO1
//量測 IO2
//測試結束
