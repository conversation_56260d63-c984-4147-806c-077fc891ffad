////////////////////////////////////////////////////////////////////////////////////////////////////
//Tool version : 0.01                                                                             //
//Proj         : ICNA3611                                                                         //
//Test Item    : POW_INTF                                                                         //
//Author       : Sychang                                                                          //
//Date         : 2025/06/23 16:17:25                                                              //
////////////////////////////////////////////////////////////////////////////////////////////////////
//;-------------------------------------------------------------------------------------------------
//;ICNA3611_POW_INTF.rgt start
//;//Test the connection of power between DDI and TP.                                             //
//;//power pin : tp2ddi_pow_ctrl_release, ddi2tp_pow_ctrl_request, dd12tp_pow_ctl_ready, ddi_avdd_ready//
//;FOLDER(POWER)                                                                                  //
//;#include open_ddi_p_mode.op2.txt                                                               //
//;73005=03//WO                                                                                   //
//;73006=06//WO                                                                                   //
//;73008=67//WO                                                                                   //
//;//設置 IO0=tp2ddi_pow_ctrl_release(1), IO1=ddi2tp_pow_ctrl_request(1)                            //
//;//IO0: 7th bit, IO1: 6th bit                                                                   //
//;73007=C0//WO                                                                                   //
//;delay 0.02                                                                                     //
//;//量測 IO0                                                                                       //
//;#get_pin GPO0 1                                                                                //
//;//量測 IO1                                                                                       //
//;#get_pin GPO1 1                                                                                //
//;                                                                                               //
//;//設置 IO0=ddi2tp_pow_ctrl_ready(1), IO1=ddi_avdd_ready(1)                                       //
//;//IO0: 7th bit, IO1: 6th bit                                                                   //
//;73007=C1//WO                                                                                   //
//;delay 0.02                                                                                     //
//;//量測 IO0                                                                                       //
//;#get_pin GPO0 1                                                                                //
//;//量測 IO1                                                                                       //
//;#get_pin GPO1 1                                                                                //
//;                                                                                               //
//;                                                                                               //
//;//發送 11                                                                                        //
//;                                                                                               //
//;                                                                                               //
//;                                                                                               //
//;//測試結束                                                                                         //
//;ICNA3611_POW_INTF.rgt end
//;-------------------------------------------------------------------------------------------------
#include open_ddi_p_mode.op2.txt

//Test the connection of power between DDI and TP.
//power pin : tp2ddi_pow_ctrl_release, ddi2tp_pow_ctrl_request, dd12tp_pow_ctl_ready, ddi_avdd_ready
#include open_ddi_p_mode.op2.txt
//73005=03//WO
R1A 00
R1B 00 07 30 05
R1D 03
R1C 00

//73006=06//WO
//R1A 00
R1B 00 07 30 06
R1D 06
R1C 00

//73008=67//WO
//R1A 00
R1B 00 07 30 08
R1D 67
R1C 00

//設置 IO0=tp2ddi_pow_ctrl_release(1), IO1=ddi2tp_pow_ctrl_request(1)
//IO0: 7th bit, IO1: 6th bit
//73007=C0//WO
//R1A 00
R1B 00 07 30 07
R1D C0
R1C 00

delay 0.02
//量測 IO0
#get_pin GPO0 1
//量測 IO1
#get_pin GPO1 1
//設置 IO0=ddi2tp_pow_ctrl_ready(1), IO1=ddi_avdd_ready(1)
//IO0: 7th bit, IO1: 6th bit
//73007=C1//WO
//R1A 00
//R1B 00 07 30 07
R1D C1
R1C 00

delay 0.02
//量測 IO0
#get_pin GPO0 1
//量測 IO1
#get_pin GPO1 1
//發送 11
//測試結束
