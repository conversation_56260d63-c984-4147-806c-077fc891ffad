1 This configue to change TP_boot_state to DDI_PRGM
4 3 0
4 4 1
4 1 0
4 2 0
4 6 1
4 7 1
4 8 1
4 9 1
4 10 1
4 11 1
4 12 0
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 1
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 0
0 100.000
4 6 0
4 7 1
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 1
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 1
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 1
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 0
4 12 0
4 13 1
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 1
4 7 0
4 8 1
4 9 1
4 10 1
4 11 0
4 12 0
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 1
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 1
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 1
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
1 read back
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 1
4 4 0
4 6 2
4 7 2
4 8 2
4 9 2
4 10 2
4 11 2
4 12 2
4 13 2
0 100.000
0 70.000
5 6 0
5 7 0
5 8 0
5 9 0
5 10 0
5 11 0
5 12 0
5 13 1
6 [r:??1700][g:01] Read 1 byte from GR00.17 and chek
0 30.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
0 2000000.000
1 finish
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 1
4 13 1
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 1
4 4 0
4 6 2
4 7 2
4 8 2
4 9 2
4 10 2
4 11 2
4 12 2
4 13 2
0 100.000
0 70.000
0 30.000
0 70.000
0 30.000
0 70.000
0 30.000
0 70.000
5 6 0
5 7 1
5 8 1
5 9 0
5 10 1
5 11 0
5 12 0
5 13 0
6 [r:??1F03][g:68] Read 4 bytes from UCS.1F and check the byte [4]
0 30.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 1
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 1
4 4 0
4 6 2
4 7 2
4 8 2
4 9 2
4 10 2
4 11 2
4 12 2
4 13 2
0 100.000
0 70.000
0 30.000
0 70.000
0 30.000
0 70.000
0 30.000
0 70.000
5 6 0
5 7 1
5 8 1
5 9 0
5 10 1
5 11 0
5 12 0
5 13 0
6 [r:??1F03][g:68] Read 4 bytes from UCS.1F and check the byte [4]
0 30.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 1
4 8 1
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 1
4 4 0
4 6 2
4 7 2
4 8 2
4 9 2
4 10 2
4 11 2
4 12 2
4 13 2
0 100.000
0 70.000
0 30.000
0 70.000
0 30.000
0 70.000
0 30.000
0 70.000
5 6 0
5 7 0
5 8 0
5 9 1
5 10 0
5 11 0
5 12 1
5 13 0
6 [r:??1F03][g:12] Read 4 bytes from UCS.1F and check the byte [4]
0 30.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 1
4 8 1
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 1
4 4 0
4 6 2
4 7 2
4 8 2
4 9 2
4 10 2
4 11 2
4 12 2
4 13 2
0 100.000
0 70.000
0 30.000
0 70.000
0 30.000
0 70.000
5 6 0
5 7 0
5 8 0
5 9 1
5 10 0
5 11 0
5 12 1
5 13 0
6 [r:??1F02][g:12] Read 4 bytes from UCS.1F and check the byte [3, 4]
0 30.000
0 70.000
5 6 0
5 7 0
5 8 1
5 9 1
5 10 0
5 11 1
5 12 0
5 13 0
6 [r:??1F03][g:34] Read 4 bytes from UCS.1F and check the byte [3, 4]
0 30.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 1
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 1
4 8 1
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 0
4 12 1
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 1
4 4 0
4 6 2
4 7 2
4 8 2
4 9 2
4 10 2
4 11 2
4 12 2
4 13 2
0 100.000
0 70.000
5 6 0
5 7 0
5 8 0
5 9 1
5 10 0
5 11 0
5 12 1
5 13 0
6 [r:??1F00][g:12] Read 3 bytes from UCS.1F and check the byte [1, 2, 3]
0 30.000
0 70.000
5 6 0
5 7 0
5 8 1
5 9 1
5 10 0
5 11 1
5 12 0
5 13 0
6 [r:??1F01][g:34] Read 3 bytes from UCS.1F and check the byte [1, 2, 3]
0 30.000
0 70.000
5 6 0
5 7 1
5 8 0
5 9 1
5 10 0
5 11 1
5 12 1
5 13 0
6 [r:??1F02][g:56] Read 3 bytes from UCS.1F and check the byte [1, 2, 3]
0 30.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 1
4 13 1
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
4 6 0
4 7 1
4 8 1
4 9 1
4 10 0
4 11 1
4 12 0
4 13 1
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 1
4 4 0
4 6 2
4 7 2
4 8 2
4 9 2
4 10 2
4 11 2
4 12 2
4 13 2
0 100.000
0 70.000
5 6 0
5 7 0
5 8 0
5 9 1
5 10 0
5 11 0
5 12 1
5 13 0
6 [r:??1F00][g:12] Read 4 bytes from UCS.1F and check the byte [1]
0 30.000
0 70.000
0 30.000
0 70.000
0 30.000
0 70.000
0 30.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 1
4 8 1
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 1
4 4 0
4 6 2
4 7 2
4 8 2
4 9 2
4 10 2
4 11 2
4 12 2
4 13 2
0 100.000
0 70.000
0 30.000
0 70.000
0 30.000
0 70.000
5 6 0
5 7 0
5 8 0
5 9 1
5 10 0
5 11 0
5 12 1
5 13 0
6 [r:??1F02][g:12] Read 4 bytes from UCS.1F and check the byte [3]
0 30.000
0 70.000
0 30.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
4 6 0
4 7 1
4 8 1
4 9 1
4 10 0
4 11 1
4 12 0
4 13 1
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 1
4 4 0
4 6 2
4 7 2
4 8 2
4 9 2
4 10 2
4 11 2
4 12 2
4 13 2
0 100.000
0 70.000
0 30.000
0 70.000
5 6 0
5 7 0
5 8 0
5 9 1
5 10 0
5 11 0
5 12 1
5 13 0
6 [r:??1F01][g:12] Read 4 bytes from UCS.1F and check the byte [2, 4]
0 30.000
0 70.000
0 30.000
0 70.000
5 6 0
5 7 0
5 8 1
5 9 1
5 10 0
5 11 1
5 12 0
5 13 0
6 [r:??1F03][g:34] Read 4 bytes from UCS.1F and check the byte [2, 4]
0 30.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 1
4 8 1
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 0
4 12 1
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 1
4 4 0
4 6 2
4 7 2
4 8 2
4 9 2
4 10 2
4 11 2
4 12 2
4 13 2
0 100.000
0 70.000
0 30.000
0 70.000
5 6 0
5 7 0
5 8 1
5 9 1
5 10 0
5 11 1
5 12 0
5 13 0
6 [r:??1F01][g:34] Read 4 bytes from UCS.1F and check the byte [2, 4]
0 30.000
0 70.000
0 30.000
0 70.000
5 6 0
5 7 1
5 8 0
5 9 1
5 10 0
5 11 1
5 12 1
5 13 0
6 [r:??1F03][g:56] Read 4 bytes from UCS.1F and check the byte [2, 4]
0 30.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 1
4 8 1
4 9 0
4 10 1
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 1
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 1
4 8 1
4 9 0
4 10 1
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 1
4 8 1
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 0
4 12 1
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 0
4 12 1
4 13 1
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 1
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 1
4 8 1
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 0
4 12 1
4 13 0
0 100.000
4 6 0
4 7 0
4 8 1
4 9 1
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 1
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 1
4 8 1
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 0
4 12 1
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 0
4 12 1
4 13 0
0 100.000
4 6 0
4 7 0
4 8 1
4 9 1
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
4 6 0
4 7 1
4 8 0
4 9 1
4 10 0
4 11 1
4 12 1
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 1
4 13 1
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
4 6 0
4 7 1
4 8 1
4 9 1
4 10 0
4 11 1
4 12 0
4 13 1
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 0
4 12 1
4 13 0
0 100.000
4 6 0
4 7 0
4 8 1
4 9 1
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
4 6 0
4 7 1
4 8 0
4 9 1
4 10 0
4 11 1
4 12 1
4 13 0
0 100.000
4 6 0
4 7 1
4 8 1
4 9 1
4 10 1
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 0
4 12 1
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 1
4 12 1
4 13 1
0 100.000
4 6 0
4 7 1
4 8 1
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 0
4 12 1
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 1
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 1
4 10 0
4 11 0
4 12 1
4 13 0
0 100.000
4 6 0
4 7 0
4 8 1
4 9 1
4 10 0
4 11 1
4 12 0
4 13 0
0 100.000
1 [LABEL:REG_A]
4 6 0
4 7 1
4 8 0
4 9 1
4 10 0
4 11 1
4 12 1
4 13 0
0 100.000
1 [LABEL:REG_TMP0]
4 6 0
4 7 1
4 8 1
4 9 1
4 10 1
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 3 0
4 4 1
4 1 0
4 2 0
4 6 0
4 7 0
4 8 0
4 9 1
4 10 1
4 11 1
4 12 0
4 13 0
0 100.000
4 1 0
4 2 1
4 3 0
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
4 1 1
4 2 1
4 3 1
4 4 1
4 6 0
4 7 0
4 8 0
4 9 0
4 10 0
4 11 0
4 12 0
4 13 0
0 100.000
0 2000000.000
0 10.000
