//Test the connection of power between DDI and TP. 
//power pin : tp2ddi_pow_ctrl_release, ddi2tp_pow_ctrl_request, dd12tp_pow_ctl_ready, ddi_avdd_ready
FOLDER(POWER) 
#include open_ddi_p_mode.op2.txt
73005=03//WO
73006=06//WO
73008=67//WO
//設置 IO0=tp2ddi_pow_ctrl_release(1), IO1=ddi2tp_pow_ctrl_request(1)
//IO0: 7th bit, IO1: 6th bit 
73007=C0//WO
delay 0.02
//量測 IO0
#get_pin GPO0 1
//量測 IO1
#get_pin GPO1 1

//設置 IO0=ddi2tp_pow_ctrl_ready(1), IO1=ddi_avdd_ready(1)
//IO0: 7th bit, IO1: 6th bit 
73007=C1//WO
delay 0.02
//量測 IO0
#get_pin GPO0 1
//量測 IO1
#get_pin GPO1 1


//發送 11



//測試結束