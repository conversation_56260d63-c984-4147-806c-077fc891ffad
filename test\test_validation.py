"""
測試驗證模組
"""
import pytest
from unittest.mock import Mock, call

from validation.version_validator import (
    SemanticVersion, 
    ConfigStructure, 
    VersionValidator,
    create_version_validator
)
from validation.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, create_error_handler


class TestSemanticVersion:
    """測試語義版本號"""
    
    def test_parse_full_version(self):
        """測試解析完整版本號"""
        version = SemanticVersion("1.2.3")
        assert version.major == 1
        assert version.minor == 2
        assert version.patch == 3
        assert str(version) == "1.2.3"
    
    def test_parse_short_version(self):
        """測試解析短版本號"""
        version = SemanticVersion("0.01")
        assert version.major == 0
        assert version.minor == 1
        assert version.patch == 0
        assert str(version) == "0.1.0"
    
    def test_parse_invalid_version(self):
        """測試解析無效版本號"""
        version = SemanticVersion("invalid")
        assert version.major == 0
        assert version.minor == 0
        assert version.patch == 0
    
    def test_compatibility_same_major(self):
        """測試相同主版本號的兼容性"""
        v1 = SemanticVersion("1.2.0")
        v2 = SemanticVersion("1.1.5")
        assert v1.is_compatible_with(v2) == True
    
    def test_compatibility_different_major(self):
        """測試不同主版本號的兼容性"""
        v1 = SemanticVersion("2.0.0")
        v2 = SemanticVersion("1.9.9")
        assert v1.is_compatible_with(v2) == False
    
    def test_compatibility_higher_minor(self):
        """測試較高次版本號的兼容性"""
        v1 = SemanticVersion("1.1.0")
        v2 = SemanticVersion("1.2.0")
        assert v1.is_compatible_with(v2) == False


class TestConfigStructure:
    """測試配置結構驗證"""
    
    def test_validate_structure_complete(self, sample_params):
        """測試完整結構驗證"""
        is_valid, missing = ConfigStructure.validate_structure(sample_params)
        assert is_valid == True
        assert len(missing) == 0
    
    def test_validate_structure_missing_critical(self):
        """測試缺少關鍵欄位"""
        class IncompleteParams:
            def __init__(self):
                self.version = "0.01"
                # 缺少其他關鍵欄位
        
        params = IncompleteParams()
        is_valid, missing = ConfigStructure.validate_structure(params)
        assert is_valid == False
        assert len(missing) > 0
        assert any("proj" in field for field in missing)
    
    def test_validate_format_valid(self, sample_params):
        """測試有效格式驗證"""
        is_valid, errors = ConfigStructure.validate_format(sample_params)
        assert is_valid == True
        assert len(errors) == 0
    
    def test_validate_format_invalid_width(self, sample_params):
        """測試無效的輸出寬度"""
        sample_params.output_width = "invalid"
        is_valid, errors = ConfigStructure.validate_format(sample_params)
        assert is_valid == False
        assert any("output_width" in error for error in errors)
    
    def test_validate_format_invalid_log_level(self, sample_params):
        """測試無效的日誌級別"""
        sample_params.debug = "2"  # 應該是 0 或 1
        is_valid, errors = ConfigStructure.validate_format(sample_params)
        assert is_valid == False
        assert any("debug" in error for error in errors)


class TestVersionValidator:
    """測試版本驗證器"""
    
    def test_init_default_version(self):
        """測試預設版本初始化"""
        validator = VersionValidator()
        assert str(validator.exe_version) == "0.1.0"  # 0.01 解析為 0.1.0
    
    def test_init_custom_version(self):
        """測試自定義版本初始化"""
        validator = VersionValidator("1.2.3")
        assert str(validator.exe_version) == "1.2.3"
    
    def test_validate_compatibility_success(self, sample_params):
        """測試成功的兼容性驗證"""
        validator = VersionValidator()
        is_compatible, message = validator.validate_compatibility(sample_params)
        assert is_compatible == True
        assert "兼容且格式正確" in message
    
    def test_validate_compatibility_unsupported_version(self, sample_params):
        """測試不支援的版本"""
        sample_params.version = "2.0.0"
        validator = VersionValidator()
        is_compatible, message = validator.validate_compatibility(sample_params)
        assert is_compatible == False
        assert "不支援的 para.txt 版本" in message
    
    def test_get_compatibility_info(self, sample_params):
        """測試獲取兼容性資訊"""
        validator = VersionValidator()
        info = validator.get_compatibility_info(sample_params)
        
        assert "exe_version" in info
        assert "para_version" in info
        assert "supported_versions" in info
        assert "is_compatible" in info
        assert "details" in info
        
        assert "version_check" in info["details"]
        assert "structure_check" in info["details"]
        assert "format_check" in info["details"]
    
    def test_create_version_validator(self):
        """測試工廠函數"""
        validator = create_version_validator()
        assert isinstance(validator, VersionValidator)
        
        validator_custom = create_version_validator("1.0.0")
        assert str(validator_custom.exe_version) == "1.0.0"


class TestErrorHandler:
    """測試錯誤處理器"""
    
    def test_init_with_logger(self, mock_logger):
        """測試帶日誌器的初始化"""
        handler = ErrorHandler(mock_logger)
        assert handler.logger == mock_logger
    
    def test_init_without_logger(self):
        """測試不帶日誌器的初始化"""
        handler = ErrorHandler()
        assert handler.logger is None
    
    def test_handle_command_not_supported(self, mock_logger):
        """測試處理不支援的命令（不應該退出）"""
        handler = ErrorHandler(mock_logger)

        # 這個方法不應該拋出異常或退出
        handler.handle_command_not_supported(1, "invalid_command")

        # 驗證日誌被調用（現在有兩條日誌：主要消息和上下文）
        assert mock_logger.log.call_count == 2

        # 檢查具體的日誌調用
        expected_calls = [
            call('Line 1, command not supported: invalid_command', level='WARN'),
            call('Context: line_number=1, line_content=invalid_command', level='DEBUG')
        ]
        mock_logger.log.assert_has_calls(expected_calls)

        # 檢查第一次調用（主要錯誤消息）
        first_call = mock_logger.log.call_args_list[0]
        assert "command not supported" in first_call[0][0]
        assert first_call[1]["level"] == "WARN"  # 不支援的命令是警告，不是錯誤
    
    def test_create_error_handler(self, mock_logger):
        """測試工廠函數"""
        handler = create_error_handler(mock_logger)
        assert isinstance(handler, ErrorHandler)
        assert handler.logger == mock_logger
        
        handler_no_logger = create_error_handler()
        assert isinstance(handler_no_logger, ErrorHandler)
        assert handler_no_logger.logger is None
