"""
版本兼容性檢查模組
實現混合驗證機制：版本兼容性 + 結構完整性 + 參數格式檢查
"""
import sys
import os
from typing import Tuple, List, Optional, Dict, Any, TYPE_CHECKING

# 添加父目錄到路徑以支援直接執行
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from parameterManger import ParameterManager


class SemanticVersion:
    """語義版本號處理"""

    def __init__(self, version_str: str) -> None:
        """
        解析版本號字串，例如 "1.2.3" -> (1, 2, 3)

        Args:
            version_str: 版本號字串
        """
        try:
            parts = version_str.strip().split('.')
            self.major: int = int(parts[0]) if len(parts) > 0 else 0
            self.minor: int = int(parts[1]) if len(parts) > 1 else 0
            self.patch: int = int(parts[2]) if len(parts) > 2 else 0
        except (ValueError, IndexError):
            # 如果解析失敗，設為 0.0.0
            self.major = self.minor = self.patch = 0

    def __str__(self) -> str:
        """返回版本號字串表示"""
        return f"{self.major}.{self.minor}.{self.patch}"

    def is_compatible_with(self, other: 'SemanticVersion') -> bool:
        """
        檢查版本兼容性

        Args:
            other: 要比較的版本

        Returns:
            True 如果兼容，否則 False
        """
        # 主版本號必須相同
        if self.major != other.major:
            return False

        # 次版本號可以向下兼容（exe版本 >= para版本）
        if self.minor < other.minor:
            return False

        return True


class ConfigStructure:
    """配置檔案結構定義"""
    
    # 關鍵欄位（必須存在且格式正確）
    CRITICAL_FIELDS = {
        'version': str,
        'proj': str,
        'commands': str,
        'output_width': str,
        'author': str
    }
    
    # 日誌級別欄位（必須存在且為 0 或 1）
    LOG_LEVEL_FIELDS = {
        'debug': str,
        'info': str,
        'warn': str,
        'error': str,
        'note': str
    }
    
    # 可選欄位（可以不存在）
    OPTIONAL_FIELDS = {
        'inFileName': str,
        'outFileName': str,
        'use_last_file': str,
        'source_file_format': str
    }
    
    @classmethod
    def validate_structure(cls, params: ParameterManager) -> Tuple[bool, List[str]]:
        """驗證配置檔案結構完整性"""
        missing_fields = []
        
        # 檢查關鍵欄位
        for field in cls.CRITICAL_FIELDS:
            if not hasattr(params, field) or not getattr(params, field):
                missing_fields.append(f"關鍵欄位 '{field}'")
        
        # 檢查日誌級別欄位
        for field in cls.LOG_LEVEL_FIELDS:
            if not hasattr(params, field):
                missing_fields.append(f"日誌級別欄位 '{field}'")
        
        return len(missing_fields) == 0, missing_fields
    
    @classmethod
    def validate_format(cls, params: ParameterManager) -> Tuple[bool, List[str]]:
        """驗證參數格式正確性"""
        format_errors = []
        
        # 檢查 output_width 是否為數字
        try:
            width = int(params.output_width)
            if width <= 0:
                format_errors.append("output_width 必須為正整數")
        except (ValueError, AttributeError):
            format_errors.append("output_width 格式錯誤，必須為數字")
        
        # 檢查 commands 是否可以正確分割
        try:
            commands = params.commands.split(',')
            if len(commands) == 0:
                format_errors.append("commands 不能為空")
        except AttributeError:
            format_errors.append("commands 格式錯誤")
        
        # 檢查日誌級別是否為 0 或 1
        for field in cls.LOG_LEVEL_FIELDS:
            if hasattr(params, field):
                value = getattr(params, field)
                if value not in ['0', '1']:
                    format_errors.append(f"日誌級別 '{field}' 必須為 0 或 1，目前為 '{value}'")
        
        # 檢查專案名稱不為空
        if hasattr(params, 'proj') and not params.proj.strip():
            format_errors.append("proj 專案名稱不能為空")
        
        return len(format_errors) == 0, format_errors


class VersionValidator:
    """版本兼容性驗證器"""
    
    # 當前 exe 版本
    CURRENT_EXE_VERSION = "0.01"
    
    # 支援的 para.txt 版本列表
    SUPPORTED_PARA_VERSIONS = ["0.01"]
    
    def __init__(self, exe_version: Optional[str] = None):
        """初始化驗證器"""
        self.exe_version = SemanticVersion(exe_version or self.CURRENT_EXE_VERSION)
    
    def validate_compatibility(self, params: ParameterManager) -> Tuple[bool, str]:
        """綜合兼容性檢查"""
        
        # 1. 檢查版本兼容性
        is_version_ok, version_msg = self._check_version_compatibility(params)
        if not is_version_ok:
            return False, version_msg
        
        # 2. 檢查結構完整性
        is_structure_ok, missing_fields = ConfigStructure.validate_structure(params)
        if not is_structure_ok:
            return False, f"配置檔案結構不完整，缺少：{', '.join(missing_fields)}"
        
        # 3. 檢查參數格式
        is_format_ok, format_errors = ConfigStructure.validate_format(params)
        if not is_format_ok:
            return False, f"參數格式錯誤：{'; '.join(format_errors)}"
        
        return True, "配置檔案兼容且格式正確"

    def _check_version_compatibility(self, params: ParameterManager) -> Tuple[bool, str]:
        """檢查版本兼容性"""
        try:
            para_version = SemanticVersion(params.version)

            # 檢查是否在支援列表中
            if params.version not in self.SUPPORTED_PARA_VERSIONS:
                return False, (
                    f"不支援的 para.txt 版本：{params.version}。"
                    f"支援的版本：{', '.join(self.SUPPORTED_PARA_VERSIONS)}"
                )

            # 檢查語義版本兼容性
            if not self.exe_version.is_compatible_with(para_version):
                return False, (
                    f"版本不兼容：exe版本 {self.exe_version} 與 para.txt版本 {para_version} 不兼容"
                )

            return True, f"版本兼容：exe {self.exe_version} ↔ para.txt {para_version}"

        except Exception as e:
            return False, f"版本檢查失敗：{e}"

    def get_compatibility_info(self, params: ParameterManager) -> dict:
        """獲取詳細的兼容性資訊"""
        info: dict = {
            'exe_version': str(self.exe_version),
            'para_version': getattr(params, 'version', 'unknown'),
            'supported_versions': self.SUPPORTED_PARA_VERSIONS,
            'is_compatible': False,
            'details': {}
        }

        # 版本檢查
        is_version_ok, version_msg = self._check_version_compatibility(params)
        info['details']['version_check'] = {'passed': is_version_ok, 'message': version_msg}

        # 結構檢查
        is_structure_ok, missing_fields = ConfigStructure.validate_structure(params)
        info['details']['structure_check'] = {
            'passed': is_structure_ok,
            'missing_fields': missing_fields
        }

        # 格式檢查
        is_format_ok, format_errors = ConfigStructure.validate_format(params)
        info['details']['format_check'] = {
            'passed': is_format_ok,
            'errors': format_errors
        }

        info['is_compatible'] = is_version_ok and is_structure_ok and is_format_ok

        return info


def create_version_validator(exe_version: Optional[str] = None) -> VersionValidator:
    """創建版本驗證器的工廠函數"""
    return VersionValidator(exe_version)
