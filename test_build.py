#!/usr/bin/env python3
"""
測試構建腳本
用於驗證構建環境和配置是否正確
"""
import os
import sys
import subprocess
from pathlib import Path

def test_python_version():
    """測試 Python 版本"""
    print("🐍 測試 Python 版本...")
    if sys.version_info >= (3, 8):
        print(f"✅ Python {sys.version.split()[0]} 符合要求")
        return True
    else:
        print(f"❌ Python 版本過低: {sys.version.split()[0]}")
        print("   需要 Python 3.8 或更高版本")
        return False

def test_source_files():
    """測試源文件是否存在"""
    print("📁 測試源文件...")
    
    required_files = [
        "source/application.py",
        "source/config/__init__.py",
        "source/core/engine.py",
        "source/parsers/rgt_parser.py",
        "build_config.py",
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要的源文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print(f"✅ 所有必要的源文件都存在 ({len(required_files)} 個)")
        return True

def test_build_config():
    """測試構建配置"""
    print("⚙️  測試構建配置...")
    
    try:
        from build_config import BUILD_CONFIG, validate_build_environment
        
        # 檢查配置
        script_name = BUILD_CONFIG.get("script_name")
        if not script_name or not Path(script_name).exists():
            print(f"❌ 主腳本文件不存在: {script_name}")
            return False
        
        # 驗證構建環境
        errors = validate_build_environment()
        if errors:
            print("❌ 構建環境驗證失敗:")
            for error in errors:
                print(f"   - {error}")
            return False
        
        print("✅ 構建配置正確")
        return True
        
    except ImportError as e:
        print(f"❌ 無法導入構建配置: {e}")
        return False
    except Exception as e:
        print(f"❌ 構建配置測試失敗: {e}")
        return False

def test_pyinstaller():
    """測試 PyInstaller 是否可用"""
    print("🔧 測試 PyInstaller...")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller {PyInstaller.__version__} 已安裝")
        return True
    except ImportError:
        print("⚠️  PyInstaller 未安裝")
        print("   構建腳本會自動安裝 PyInstaller")
        return True  # 不算錯誤，構建腳本會處理

def test_import_modules():
    """測試主要模組是否可以導入"""
    print("📦 測試模組導入...")
    
    # 添加源碼路徑
    sys.path.insert(0, "source")
    
    modules_to_test = [
        "application",
        "config",
        "core.engine",
        "parsers.rgt_parser",
        "logPrinter",
        "parameterManger",
    ]
    
    failed_imports = []
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"   ✅ {module_name}")
        except ImportError as e:
            print(f"   ❌ {module_name}: {e}")
            failed_imports.append(module_name)
        except Exception as e:
            print(f"   ⚠️  {module_name}: {e}")
    
    if failed_imports:
        print(f"❌ {len(failed_imports)} 個模組導入失敗")
        return False
    else:
        print(f"✅ 所有模組導入成功 ({len(modules_to_test)} 個)")
        return True

def test_build_scripts():
    """測試構建腳本是否存在"""
    print("📜 測試構建腳本...")
    
    build_scripts = [
        ("build_exe.bat", "Windows 批處理腳本"),
        ("build.sh", "Linux/macOS Shell 腳本"),
        ("build.py", "跨平台 Python 腳本"),
        ("build_config.py", "構建配置文件"),
    ]
    
    missing_scripts = []
    for script_path, description in build_scripts:
        if Path(script_path).exists():
            print(f"   ✅ {script_path} ({description})")
        else:
            print(f"   ❌ {script_path} ({description})")
            missing_scripts.append(script_path)
    
    if missing_scripts:
        print(f"❌ {len(missing_scripts)} 個構建腳本缺失")
        return False
    else:
        print("✅ 所有構建腳本都存在")
        return True

def main():
    """主函數"""
    print("=" * 50)
    print("    DBI Transfer Tool 構建環境測試")
    print("=" * 50)
    print()
    
    tests = [
        test_python_version,
        test_source_files,
        test_build_config,
        test_pyinstaller,
        test_import_modules,
        test_build_scripts,
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 測試 {test_func.__name__} 發生異常: {e}")
            failed += 1
        print()
    
    # 顯示結果
    print("=" * 50)
    print("測試結果:")
    print(f"✅ 通過: {passed}")
    print(f"❌ 失敗: {failed}")
    print(f"📊 總計: {passed + failed}")
    print()
    
    if failed == 0:
        print("🎉 所有測試通過！構建環境準備就緒。")
        print()
        print("您可以運行以下命令開始構建:")
        print("  Windows: build_exe.bat")
        print("  Linux/macOS: ./build.sh")
        print("  跨平台: python build.py")
        return 0
    else:
        print("⚠️  部分測試失敗，請檢查上述錯誤信息。")
        print("修復問題後再次運行此測試腳本。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
