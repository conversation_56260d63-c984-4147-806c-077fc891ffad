# DBI Transfer Tool 錯誤測試指南

本指南提供了各種錯誤使用情況的測試方法，幫助驗證程序的錯誤處理能力。

## 🚀 快速測試

### 自動化測試
```cmd
# 運行自動化錯誤測試腳本
test_error_cases.bat
```

### 手動測試
選擇以下任一測試進行手動驗證：

## 📋 測試清單

### ✅ **簡單測試（推薦先試這些）**

#### 1. 無效命令行選項
```cmd
DBI_Transfer.exe --invalid-option
```
**預期**: 顯示錯誤信息，等待按鍵退出

#### 2. 不存在的測試項目
```cmd
DBI_Transfer.exe nonexistent_test
```
**預期**: 顯示測試項目不存在的錯誤

#### 3. 查看幫助信息
```cmd
DBI_Transfer.exe --help
```
**預期**: 顯示幫助信息，等待按鍵退出

#### 4. 查看版本信息
```cmd
DBI_Transfer.exe --version
```
**預期**: 顯示版本信息，等待按鍵退出

### 🔧 **中等難度測試**

#### 5. 配置文件不存在
```cmd
# 備份配置文件
copy para.txt para.txt.backup

# 刪除配置文件
del para.txt

# 運行程序
DBI_Transfer.exe test_item

# 恢復配置文件
copy para.txt.backup para.txt
del para.txt.backup
```

#### 6. 格式錯誤的配置文件
```cmd
# 備份配置文件
copy para.txt para.txt.backup

# 創建錯誤格式的配置文件
echo invalid config content > para.txt

# 運行程序
DBI_Transfer.exe test_item

# 恢復配置文件
copy para.txt.backup para.txt
del para.txt.backup
```

#### 7. 依賴文件缺失
```cmd
# 測試 op2.exe 缺失
ren DBI_Transfer\op2.exe op2.exe.backup
DBI_Transfer.exe test_item
ren DBI_Transfer\op2.exe.backup op2.exe

# 測試 cases 目錄缺失
ren DBI_Transfer\cases cases_backup
DBI_Transfer.exe test_item
ren DBI_Transfer\cases_backup cases
```

### 🎯 **高級測試**

#### 8. 用戶中斷測試
```cmd
# 啟動程序
DBI_Transfer.exe

# 在程序運行過程中按 Ctrl+C
# 檢查是否正確處理中斷並等待用戶確認
```

#### 9. 互動模式測試
```cmd
# 啟動互動模式
DBI_Transfer.exe

# 測試各種輸入:
# - 空輸入
# - 特殊字符
# - 非常長的名稱
# - Ctrl+C 中斷
```

#### 10. 特殊字符測試
```cmd
DBI_Transfer.exe "test*item?with<special>chars"
DBI_Transfer.exe "測試中文項目"
DBI_Transfer.exe "test item with spaces"
```

## 🔍 **測試檢查點**

每個測試完成後，請檢查以下幾點：

### ✅ **錯誤處理檢查**
- [ ] 是否顯示了清晰的錯誤信息？
- [ ] 錯誤信息是否用戶友好（避免技術術語）？
- [ ] 是否提供了解決建議？

### ✅ **用戶體驗檢查**
- [ ] 程序是否等待用戶按鍵才退出？
- [ ] 窗口是否保持打開狀態？
- [ ] 用戶是否有足夠時間閱讀信息？

### ✅ **程序穩定性檢查**
- [ ] 程序是否正常退出（沒有崩潰）？
- [ ] 是否有未處理的異常？
- [ ] 臨時文件是否正確清理？

## 📊 **測試結果記錄**

| 測試項目 | 狀態 | 錯誤信息清晰度 | 等待用戶確認 | 備註 |
|---------|------|---------------|-------------|------|
| 無效選項 | ⭕ | ⭕ | ⭕ | |
| 不存在測試項目 | ⭕ | ⭕ | ⭕ | |
| 配置文件不存在 | ⭕ | ⭕ | ⭕ | |
| 格式錯誤配置 | ⭕ | ⭕ | ⭕ | |
| 依賴文件缺失 | ⭕ | ⭕ | ⭕ | |
| 用戶中斷 | ⭕ | ⭕ | ⭕ | |

**圖例**: ✅ 通過 | ❌ 失敗 | ⭕ 待測試

## 🛠️ **常見問題排查**

### 問題 1: 程序立即關閉
**可能原因**: 
- 修改未生效，需要重新構建
- 異常被未捕獲的處理器攔截

**解決方案**:
```cmd
# 重新構建程序
build_exe.bat

# 或者直接測試源碼
python source/application.py --version
```

### 問題 2: 錯誤信息不清晰
**檢查項目**:
- 查看日誌文件（如果有）
- 檢查錯誤處理代碼
- 確認異常類型是否正確

### 問題 3: 某些測試無法執行
**可能原因**:
- 文件權限問題
- 路徑問題
- 依賴文件缺失

## 💡 **測試建議**

1. **從簡單測試開始**: 先測試命令行選項，再測試複雜場景
2. **逐個測試**: 不要同時進行多個測試，避免混淆結果
3. **記錄結果**: 使用上面的表格記錄測試結果
4. **重複測試**: 對於失敗的測試，修復後重新測試
5. **真實場景**: 模擬真實用戶可能遇到的情況

## 🎯 **測試目標**

通過這些測試，我們希望確保：
- 程序在各種錯誤情況下都能優雅地處理
- 用戶能夠清楚地了解發生了什麼問題
- 程序不會意外崩潰或立即關閉
- 用戶有足夠的時間閱讀錯誤信息和解決建議
