#!/usr/bin/env python3
"""
類型檢查腳本
"""
import sys
import os
import subprocess
from pathlib import Path
from typing import List, Optional, Tu<PERSON>


def run_mypy(paths: List[str], strict: bool = False) -> Tuple[int, str, str]:
    """
    運行 mypy 類型檢查
    
    Args:
        paths: 要檢查的路徑列表
        strict: 是否使用嚴格模式
        
    Returns:
        (退出碼, 標準輸出, 標準錯誤)
    """
    cmd = ["python", "-m", "mypy"]
    
    if strict:
        cmd.extend([
            "--strict",
            "--disallow-any-generics",
            "--disallow-subclassing-any"
        ])
    
    cmd.extend(paths)
    
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            check=False
        )
        return result.returncode, result.stdout, result.stderr
    except FileNotFoundError:
        return 1, "", "mypy not found. Please install with: pip install mypy"


def check_source_code() -> int:
    """檢查源碼類型"""
    print("🔍 檢查源碼類型註解...")
    
    source_paths = [
        "source/",
        "--exclude", "source/Lib",
        "--exclude", "source/Scripts"
    ]
    
    returncode, stdout, stderr = run_mypy(source_paths)
    
    if stdout:
        print(stdout)
    if stderr:
        print(stderr, file=sys.stderr)
    
    if returncode == 0:
        print("✅ 源碼類型檢查通過！")
    else:
        print("❌ 源碼類型檢查失敗")
    
    return returncode


def check_tests() -> int:
    """檢查測試代碼類型"""
    print("\n🧪 檢查測試代碼類型註解...")
    
    test_paths = ["test/"]
    
    returncode, stdout, stderr = run_mypy(test_paths)
    
    if stdout:
        print(stdout)
    if stderr:
        print(stderr, file=sys.stderr)
    
    if returncode == 0:
        print("✅ 測試代碼類型檢查通過！")
    else:
        print("❌ 測試代碼類型檢查失敗")
    
    return returncode


def check_specific_file(file_path: str) -> int:
    """檢查特定文件"""
    print(f"🔍 檢查文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return 1
    
    returncode, stdout, stderr = run_mypy([file_path])
    
    if stdout:
        print(stdout)
    if stderr:
        print(stderr, file=sys.stderr)
    
    if returncode == 0:
        print(f"✅ {file_path} 類型檢查通過！")
    else:
        print(f"❌ {file_path} 類型檢查失敗")
    
    return returncode


def check_strict_mode() -> int:
    """嚴格模式檢查"""
    print("\n🔒 嚴格模式類型檢查...")
    
    # 只檢查核心模組
    core_paths = [
        "source/core/",
        "source/type_definitions.py",
        "source/exceptions.py"
    ]
    
    returncode, stdout, stderr = run_mypy(core_paths, strict=True)
    
    if stdout:
        print(stdout)
    if stderr:
        print(stderr, file=sys.stderr)
    
    if returncode == 0:
        print("✅ 嚴格模式檢查通過！")
    else:
        print("❌ 嚴格模式檢查失敗")
    
    return returncode


def generate_type_coverage_report() -> None:
    """生成類型覆蓋率報告"""
    print("\n📊 生成類型覆蓋率報告...")
    
    try:
        # 使用 mypy 的覆蓋率報告功能
        result = subprocess.run([
            "python", "-m", "mypy",
            "--html-report", "type_coverage_html",
            "--txt-report", "type_coverage_txt",
            "source/"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 類型覆蓋率報告已生成")
            print("   HTML 報告: type_coverage_html/index.html")
            print("   文字報告: type_coverage_txt/index.txt")
        else:
            print("❌ 生成類型覆蓋率報告失敗")
            if result.stderr:
                print(result.stderr)
    
    except FileNotFoundError:
        print("❌ mypy 未安裝，無法生成覆蓋率報告")


def main() -> int:
    """主函數"""
    if len(sys.argv) == 1:
        # 運行所有檢查
        print("🏷️ DBI Transfer Tool 類型檢查")
        print("=" * 50)
        
        source_result = check_source_code()
        test_result = check_tests()
        
        print("\n" + "=" * 50)
        if source_result == 0 and test_result == 0:
            print("🎉 所有類型檢查通過！")
            return 0
        else:
            print("💥 類型檢查失敗")
            return 1
    
    elif len(sys.argv) == 2:
        arg = sys.argv[1]
        
        if arg == "--help" or arg == "-h":
            print("DBI Transfer Tool 類型檢查工具")
            print("\n用法:")
            print("  python scripts/type_check.py              # 檢查所有代碼")
            print("  python scripts/type_check.py <file>       # 檢查特定文件")
            print("  python scripts/type_check.py --source     # 只檢查源碼")
            print("  python scripts/type_check.py --tests      # 只檢查測試")
            print("  python scripts/type_check.py --strict     # 嚴格模式檢查")
            print("  python scripts/type_check.py --coverage   # 生成覆蓋率報告")
            print("  python scripts/type_check.py --help       # 顯示此幫助")
            return 0
        
        elif arg == "--source":
            return check_source_code()
        
        elif arg == "--tests":
            return check_tests()
        
        elif arg == "--strict":
            return check_strict_mode()
        
        elif arg == "--coverage":
            generate_type_coverage_report()
            return 0
        
        else:
            # 檢查特定文件
            return check_specific_file(arg)
    
    else:
        print("❌ 參數過多")
        print("使用 --help 查看用法")
        return 1


if __name__ == "__main__":
    # 確保在專案根目錄運行
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    sys.exit(main())
