"""
DBI 轉換引擎 - 核心業務邏輯

這個模組包含 DBI Transfer Tool 的核心轉換邏輯，負責：
- 將解析後的 RGT 命令轉換為輸出格式
- 處理暫存器讀寫操作
- 管理狀態和資料分組
- 生成 C 語言格式的輸出
"""
import sys
import os
import re
from typing import TextIO, List, Tuple, Optional

# 添加父目錄到路徑以支援直接執行
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from constant import FieldIndex, CommandTypes, Defaults, DataLength
from type_definitions import HexData, Address, Comment, Loggable


def extract_label_value(text: Optional[str], label: str) -> Optional[str]:
    """
    從字串中提取標籤值

    從形如 "LABEL(value)" 的字串中提取 value 部分

    Args:
        text: 要搜尋的字串，可能為 None
        label: 標籤名稱，如 "TAG", "ALIGN" 等

    Returns:
        標籤值，如果未找到則返回 None

    Example:
        >>> extract_label_value("some text TAG(test_value) more", "TAG")
        'test_value'
        >>> extract_label_value("no tag here", "TAG")
        None
    """
    if not text:
        return None

    pattern = rf"{label}\(([^)]+)\)"
    match = re.search(pattern, text)
    return match.group(1) if match else None


class DbiTransferEngine:
    """
    DBI 轉換引擎 - 核心業務邏輯處理器

    負責將解析後的 RGT 命令轉換為目標格式，包括：
    - 暫存器讀寫操作的轉換
    - 狀態管理和資料分組
    - 輸出格式生成
    """

    def __init__(self, logger: Loggable) -> None:
        """
        初始化 DBI 轉換引擎

        Args:
            logger: 日誌記錄器，用於記錄轉換過程
        """
        self.logger: Loggable = logger

        # 狀態追蹤：記錄上次的暫存器值，用於優化輸出
        self._last_register_1A: str = Defaults.LAST_1A
        self._last_register_1B: str = Defaults.LAST_1B
    
    def _write_register_with_optimization(
        self,
        output_file: TextIO,
        register_label: str,
        new_value: str,
        last_value: str
    ) -> str:
        """
        寫入暫存器值，如果與上次相同則加註釋優化

        這個方法實現了輸出優化：如果新值與上次相同，
        則在行前加上 "//" 註釋，避免重複設置相同的暫存器值

        Args:
            output_file: 輸出文件對象
            register_label: 暫存器標籤（如 "R1A", "R1B"）
            new_value: 新的暫存器值
            last_value: 上次的暫存器值

        Returns:
            新值（用於更新狀態）
        """
        # 如果值相同，加上註釋前綴以優化輸出
        comment_prefix = "//" if new_value == last_value else ""
        output_file.write(f"{comment_prefix}{register_label} {new_value}\n")
        return new_value

    def _write_c_register_operation(
        self,
        c_file: TextIO,
        address: Address,
        data: HexData,
        data_length_bytes: int
    ) -> None:
        """
        寫入 C 語言格式的暫存器操作

        根據資料長度生成對應的 C 語言暫存器操作宏

        Args:
            c_file: C 文件輸出對象
            address: 暫存器地址（十六進制字串）
            data: 十六進制資料
            data_length_bytes: 資料長度（字節數：1, 2, 或 4）
        """
        register_macros = {
            1: "REG8",
            2: "REG16",
            4: "REG32"
        }
        if data_length_bytes in register_macros:
            self.logger.log(f"寫入 C 語言格式的暫存器操作：{register_macros[data_length_bytes]}(0x{address})=0X{data};")
            macro = register_macros[data_length_bytes]
            c_file.write(f'{macro}(0x{address})=0X{data};\n')

    def _split_data_into_byte_groups(self, hex_data: HexData, comment: Comment) -> List[str]:
        """
        將十六進制資料分割成字節組並添加標籤

        將連續的十六進制字串（如 "ABCDEF12"）分割成字節對（如 ["AB", "CD", "EF", "12"]），
        並根據註釋中的 TAG 標籤為特定字節組添加標籤

        Args:
            hex_data: 十六進制資料字串（如 "ABCDEF12"）
            comment: 註釋內容，可能包含 TAG(0:label1,1:label2) 格式的標籤

        Returns:
            字節組列表，可能包含標籤（如 ["AB(label1)", "CD(label2)", "EF", "12"]）

        Example:
            >>> engine._split_data_into_byte_groups("ABCD", "TAG(0:addr,1:data)")
            ["AB(addr)", "CD(data)"]
        """
        # 將十六進制字串分割成字節對（每2個字符一組）
        byte_pair_size = DataLength.BYTE_PAIR_SIZE
        byte_groups = [
            hex_data[i:i + byte_pair_size]
            for i in range(0, len(hex_data), byte_pair_size)
        ]

        # 處理 TAG 標籤：從註釋中提取標籤信息並應用到對應的字節組
        tag_definition = extract_label_value(comment, "TAG")
        if tag_definition:
            self._apply_tags_to_byte_groups(byte_groups, tag_definition)

        return byte_groups

    def _apply_tags_to_byte_groups(self, byte_groups: List[str], tag_definition: str) -> None:
        """
        將標籤應用到字節組

        解析 TAG 定義（如 "0:label1,1:label2"）並將標籤添加到對應索引的字節組

        Args:
            byte_groups: 字節組列表（會被修改）
            tag_definition: 標籤定義字串（如 "0:label1,1:label2"）
        """
        try:
            for tag_pair in tag_definition.split(","):
                index_str, label = tag_pair.split(":")
                index = int(index_str.strip())

                if 0 <= index < len(byte_groups):
                    byte_groups[index] += f"({label.strip()})"
        except (ValueError, IndexError) as e:
            # 如果標籤格式錯誤，記錄警告但不中斷處理
            self.logger.log(f"Invalid TAG format: {tag_definition}, error: {e}", level="WARN")
    
    def _write_simple_command(
        self,
        output_file: TextIO,
        c_file: TextIO,
        parsed_line: List[str]
    ) -> None:
        """
        寫入簡單命令（註釋、延遲、命令等）

        簡單命令不需要特殊處理，直接輸出原始內容

        Args:
            output_file: 主輸出文件
            c_file: C 文件輸出
            parsed_line: 解析後的行資料
        """
        full_line_content = parsed_line[FieldIndex.FULL_DAT]

        # 主文件：直接輸出
        output_file.write(f"{full_line_content}\n")

        # C 文件：如果不是註釋則加上註釋前綴，否則直接輸出
        if full_line_content.strip().startswith("//"):
            c_file.write(f"{full_line_content}\n")
        else:
            c_file.write(f"//{full_line_content}\n")
    
    def _process_write_command(
        self,
        output_file: TextIO,
        c_file: TextIO,
        parsed_line: List[str],
        byte_groups: List[str]
    ) -> Tuple[str, str]:
        """
        處理寫入命令（WO - Write Operation）

        生成寫入暫存器的命令序列，包括設置地址、資料和控制暫存器

        Args:
            output_file: 主輸出文件
            c_file: C 文件輸出
            parsed_line: 解析後的行資料
            byte_groups: 分組後的字節資料

        Returns:
            更新後的 (R1A, R1B) 暫存器值
        """
        address = parsed_line[FieldIndex.ADDR_IDX]
        data = parsed_line[FieldIndex.DATA_IDX]
        data_length_bytes = len(data) // DataLength.BYTE_PAIR_SIZE

        # 設置暫存器值
        new_r1a = f"0{data_length_bytes - 1}"  # 資料長度控制
        new_r1b = f"00 {address[0:2]} {address[2:4]} {address[4:6]}"  # 地址設置

        # 寫入暫存器設置（帶優化註釋）
        self._last_register_1A = self._write_register_with_optimization(
            output_file, "R1A", new_r1a, self._last_register_1A
        )
        self._last_register_1B = self._write_register_with_optimization(
            output_file, "R1B", new_r1b, self._last_register_1B
        )

        # 寫入資料和控制命令
        output_file.write(f"R1D {' '.join(byte_groups)}\n")
        output_file.write("R1C 00\n\n")

        # 生成 C 語言格式輸出
        self._generate_c_write_commands(c_file, parsed_line, address, data, data_length_bytes)

        return self._last_register_1A, self._last_register_1B

    def _generate_c_write_commands(
        self,
        c_file: TextIO,
        parsed_line: List[str],
        address: str,
        data: str,
        data_length_bytes: int
    ) -> None:
        """
        生成 C 語言格式的寫入命令

        根據資料長度生成對應的 C 語言暫存器操作
        """
        # 添加原始命令作為註釋

        with self.logger.section("生成 C 語言格式的寫入命令"):
            c_file.write(f"//{parsed_line[FieldIndex.FULL_DAT]}\n")
            # 特殊處理：3字節資料需要分割成多個操作
            if data_length_bytes == 3:
                # 3字節資料分割：先寫1字節，再寫2字節
                addr_offset_1 = f"{int(address, 16):x}"
                self._write_c_register_operation(c_file, addr_offset_1, data[4:6], 1)

                addr_offset_2 = f"{int(address, 16) + 1:x}"
                self._write_c_register_operation(c_file, addr_offset_2, data[0:4], 2)
            else:
                # 標準處理：計算地址偏移並寫入
                addr_offset = f"{int(address, 16):x}"
                self._write_c_register_operation(c_file, addr_offset, data, data_length_bytes)
    
    def _process_read_command(
        self,
        output_file: TextIO,
        parsed_line: List[str],
        byte_groups: List[str]
    ) -> Tuple[str, str]:
        """
        處理讀取命令（RO - Read Operation）

        生成讀取暫存器的命令序列，包括設置地址和驗證資料

        Args:
            output_file: 主輸出文件
            parsed_line: 解析後的行資料
            byte_groups: 分組後的字節資料

        Returns:
            更新後的 (R1A, R1B) 暫存器值
        """
        address = parsed_line[FieldIndex.ADDR_IDX]
        data = parsed_line[FieldIndex.DATA_IDX]
        data_length_bytes = len(data) // DataLength.BYTE_PAIR_SIZE

        # 處理對齊設置
        processed_groups, final_length = self._handle_read_alignment(
            byte_groups, data_length_bytes, parsed_line[FieldIndex.COMMENT_IDX]
        )

        # 找出需要檢查的字節位置（非 XX 的字節）
        check_positions = [
            i + 1 for i, byte_val in enumerate(processed_groups)
            if byte_val.lower() != "xx"
        ]

        # 設置暫存器值
        new_r1a = f"0{final_length - 1}"
        new_r1b = f"00 {address[0:2]} {address[2:4]} {address[4:6]}"

        # 寫入暫存器設置
        self._last_register_1A = self._write_register_with_optimization(
            output_file, "R1A", new_r1a, self._last_register_1A
        )
        self._last_register_1B = self._write_register_with_optimization(
            output_file, "R1B", new_r1b, self._last_register_1B
        )

        # 寫入讀取命令
        output_file.write("R1E 00\n")
        output_file.write(
            f'R1F #{final_length} {" ".join(processed_groups)} '
            f'Read {final_length} bytes from UCS.1F and check the byte {check_positions}\n\n'
        )

        return self._last_register_1A, self._last_register_1B

    def _handle_read_alignment(
        self,
        byte_groups: List[str],
        original_length: int,
        comment: str
    ) -> Tuple[List[str], int]:
        """
        處理讀取命令的對齊設置

        根據 ALIGN 標籤決定是否需要填充資料到最大長度

        Args:
            byte_groups: 原始字節組
            original_length: 原始資料長度
            comment: 註釋內容（可能包含 ALIGN 標籤）

        Returns:
            (處理後的字節組, 最終長度)
        """
        align_setting = extract_label_value(comment, "ALIGN")

        # 如果不是左對齊，則填充到最大長度
        if align_setting != Defaults.ALIGN_LEFT:
            padding_count = DataLength.MAX_DATA_LENGTH - original_length
            padded_groups = [Defaults.PADDING_VALUE] * padding_count + byte_groups
            return padded_groups, DataLength.MAX_DATA_LENGTH

        return byte_groups, original_length
    
    def process_decoded_content(
        self,
        output_file: TextIO,
        c_file: TextIO,
        decoded_content: List[List[str]]
    ) -> None:
        """
        處理解析後的內容並生成輸出

        這是引擎的主要入口點，處理所有類型的命令並生成對應的輸出

        Args:
            output_file: 主輸出文件對象
            c_file: C 語言輸出文件對象
            decoded_content: 解析後的命令列表
        """
        with self.logger.section("處理解析後的內容"):
            total_commands = len(decoded_content)

            for index, parsed_line in enumerate(decoded_content, 1):
                command_type = parsed_line[FieldIndex.COMMAND_IDX]
                command_lower = command_type.lower() if isinstance(command_type, str) else ""

                # 記錄處理進度和詳細信息
                with self.logger.section(f"處理命令 {index}/{total_commands}: {command_type}"):
                    self._log_command_details(parsed_line)

                    # 根據命令類型分發處理
                    if command_lower in CommandTypes.SIMPLE_COMMANDS:
                        self._handle_simple_command(output_file, c_file, parsed_line)
                    elif command_lower in CommandTypes.REGISTER_COMMANDS:
                        self._handle_register_command(output_file, c_file, parsed_line, command_lower)
                    else:
                        self.logger.log(f"未知命令類型: {command_type}", level="WARN")

    def _log_command_details(self, parsed_line: List[str]) -> None:
        """
        記錄命令的詳細信息到日誌

        Args:
            parsed_line: 解析後的行資料
        """
        self.logger.step(f"命令類型: {parsed_line[FieldIndex.COMMAND_IDX]}")
        self.logger.step(f"地址: {parsed_line[FieldIndex.ADDR_IDX]}")
        self.logger.step(f"資料: {parsed_line[FieldIndex.DATA_IDX]}")
        self.logger.step(f"註釋: {parsed_line[FieldIndex.COMMENT_IDX]}")

    def _handle_simple_command(
        self,
        output_file: TextIO,
        c_file: TextIO,
        parsed_line: List[str]
    ) -> None:
        """
        處理簡單命令（註釋、延遲、普通命令等）

        Args:
            output_file: 主輸出文件
            c_file: C 文件輸出
            parsed_line: 解析後的行資料
        """
        self._write_simple_command(output_file, c_file, parsed_line)

    def _handle_register_command(
        self,
        output_file: TextIO,
        c_file: TextIO,
        parsed_line: List[str],
        command_type: str
    ) -> None:
        """
        處理暫存器命令（讀寫操作）

        Args:
            output_file: 主輸出文件
            c_file: C 文件輸出
            parsed_line: 解析後的行資料
            command_type: 命令類型（"wo" 或 "ro"）
        """
        # 添加原始命令作為註釋
        output_file.write(f"//{parsed_line[FieldIndex.FULL_DAT]}\n")

        # 分割資料並處理標籤
        hex_data = parsed_line[FieldIndex.DATA_IDX]
        comment = parsed_line[FieldIndex.COMMENT_IDX]
        byte_groups = self._split_data_into_byte_groups(hex_data, comment)

        # 根據命令類型執行對應操作
        if command_type == CommandTypes.WO:
            self._last_register_1A, self._last_register_1B = self._process_write_command(
                output_file, c_file, parsed_line, byte_groups
            )
        elif command_type == CommandTypes.RO:
            self._last_register_1A, self._last_register_1B = self._process_read_command(
                output_file, parsed_line, byte_groups
            )

    def reset_state(self) -> None:
        """
        重置引擎狀態到初始值

        將所有狀態變數重置為預設值，通常在開始處理新文件時調用
        """
        self._last_register_1A = Defaults.LAST_1A
        self._last_register_1B = Defaults.LAST_1B

        self.logger.log("DBI 轉換引擎狀態已重置", level="DEBUG")
    

