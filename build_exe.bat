@echo off
chcp 65001 >nul
REM === DBI Transfer Tool 構建腳本 ===

echo ========================================
echo    DBI Transfer Tool 構建腳本 v2.0
echo ========================================
echo.

REM === 檢查 Python 環境 ===
echo [1/6] 檢查 Python 環境...
python --version 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ Python 未安裝或不在 PATH 中！
    echo 請安裝 Python 3.8+ 並確保在 PATH 中
    pause
    exit /b 1
)
echo ✅ Python 環境正常

REM === 檢查 PyInstaller ===
echo [2/6] 檢查 PyInstaller...
python -c "import PyInstaller" 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ PyInstaller 未安裝！
    echo 正在安裝 PyInstaller...
    pip install pyinstaller
    if %ERRORLEVEL% neq 0 (
        echo ❌ PyInstaller 安裝失敗！
        pause
        exit /b 1
    )
)
echo ✅ PyInstaller 可用

REM === 驗證構建環境 ===
echo [3/6] 驗證構建環境...
python build_config.py
if %ERRORLEVEL% neq 0 (
    echo ❌ 構建環境驗證失敗！
    pause
    exit /b 1
)
echo ✅ 構建環境驗證通過

REM === 清理舊文件 ===
echo [4/6] 清理舊的構建文件...
if exist "build" rmdir /s /q "build" 2>nul
if exist "DBI_Transfer\DBI_Transfer.exe" del /f /q "DBI_Transfer\DBI_Transfer.exe" 2>nul
if exist "DBI_Transfer.spec" del /q "DBI_Transfer.spec" 2>nul
echo ✅ 清理完成

REM === 執行構建 ===
echo [5/6] 執行 PyInstaller 構建...
echo 這可能需要幾分鐘時間，請耐心等待...
echo.

REM 使用基本的 PyInstaller 命令
pyinstaller ^
    --name DBI_Transfer ^
    --onefile ^
    --console ^
    --distpath DBI_Transfer ^
    --workpath build ^
    --add-data "source/config/templates;config/templates" ^
    --add-data "source/cases;cases" ^
    --add-data "source/op2.exe;." ^
    --add-data "source/para.txt;." ^
    --hidden-import config ^
    --hidden-import core ^
    --hidden-import parsers ^
    --hidden-import output ^
    --hidden-import utils ^
    --hidden-import validation ^
    --hidden-import logPrinter ^
    --hidden-import parameterManger ^
    --hidden-import exceptions ^
    --hidden-import constant ^
    --hidden-import type_definitions ^
    source/application.py

if %ERRORLEVEL% neq 0 (
    echo ❌ PyInstaller 構建失敗！
    echo 請檢查上面的錯誤信息
    pause
    exit /b 1
)

echo ✅ PyInstaller 構建成功

REM === 驗證結果 ===
echo [6/6] 驗證構建結果...
if exist "DBI_Transfer\DBI_Transfer.exe" (
    echo ✅ 可執行檔生成成功！
    echo.
    echo 📁 文件位置: DBI_Transfer\DBI_Transfer.exe
    for %%I in ("DBI_Transfer\DBI_Transfer.exe") do echo 📏 文件大小: %%~zI bytes
    echo.
    echo 🧪 測試可執行檔...
    "DBI_Transfer\DBI_Transfer.exe" --version 2>nul
    if %ERRORLEVEL% equ 0 (
        echo ✅ 可執行檔運行正常
    ) else (
        echo ⚠️  可執行檔可能有問題，請手動測試
    )
) else (
    echo ❌ 可執行檔生成失敗！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 構建完成！
echo ========================================
echo.
echo 📋 使用說明:
echo   1. 可執行檔位於: DBI_Transfer\DBI_Transfer.exe
echo   2. 測試命令: DBI_Transfer\DBI_Transfer.exe --help
echo   3. 運行命令: DBI_Transfer\DBI_Transfer.exe [test_item]
echo.
echo 📦 發布包內容:
echo   - DBI_Transfer.exe (主程式)
echo   - cases\ (測試案例)
echo   - op2.exe (OP2 工具)
echo   - para.txt (配置文件)
echo.

pause