"""
DBI Transfer Tool 類型定義
"""
from __future__ import annotations

from typing import (
    TypeVar, Protocol, Union, Optional, Dict, List, Tuple,
    Any, Callable, Iterator, ContextManager, TypedDict, Literal
)
from pathlib import Path
import sys

# Python 版本兼容性 - 不再需要 TypeAlias

# 基本類型別名
FilePath = Union[str, Path]
LineNumber = int
ErrorCode = str
LogLevel = Literal["DEBUG", "INFO", "WARN", "ERROR", "STEP"]

# 資料相關類型
HexData = str  # 十六進制資料，如 "ABCD"
Address = str  # 地址，如 "012345"
Command = str  # 命令，如 "WO", "RO"
Comment = str  # 註釋內容

# 解析相關類型
ParsedLine = List[Optional[str]]  # 解析後的行資料
RawContent = List[str]  # 原始文件內容
ValidContent = List[ParsedLine]  # 有效的解析內容

# 配置相關類型
class ParameterDict(TypedDict):
    """參數字典類型 - 必需欄位"""
    version: str
    proj: str
    commands: str
    output_width: str
    author: str
    debug: str
    info: str
    warn: str
    error: str
    note: str

class ParameterDictOptional(TypedDict, total=False):
    """參數字典類型 - 可選欄位"""
    inFileName: str
    outFileName: str
    use_last_file: str
    source_file_format: str
    product_id: str

# 完整的參數字典類型（繼承必需和可選欄位）
class FullParameterDict(ParameterDict, ParameterDictOptional):
    """完整的參數字典類型"""
    pass

# 錯誤相關類型
class ErrorContext(TypedDict, total=False):
    """錯誤上下文類型"""
    line_number: LineNumber
    line_content: str
    file_path: FilePath
    operation: str
    stage: str
    expected_format: str
    actual_value: str
    suggestion: str

class ErrorRecord(TypedDict):
    """錯誤記錄類型"""
    timestamp: str
    type: str
    message: str
    context: ErrorContext
    fatal: bool

class ErrorSummary(TypedDict):
    """錯誤摘要類型"""
    total_errors: int
    total_warnings: int
    error_types: Dict[str, int]
    recent_errors: List[ErrorRecord]
    has_fatal_errors: bool

# 輸出相關類型
class TitleInfo(TypedDict):
    """標題信息類型"""
    Tool_version: str
    Proj: str
    Test_Item: str
    Author: str
    Date: str

# 版本相關類型
class VersionInfo(TypedDict):
    """版本信息類型"""
    major: int
    minor: int
    patch: int

class CompatibilityInfo(TypedDict):
    """兼容性信息類型"""
    exe_version: str
    para_version: str
    supported_versions: List[str]
    is_compatible: bool
    details: Dict[str, Dict[str, Any]]

# 協議定義
class Loggable(Protocol):
    """可記錄日誌的協議"""
    
    def log(self, message: str, *, level: LogLevel = "INFO") -> None:
        """記錄日誌"""
        ...
    
    def step(self, message: str) -> None:
        """記錄步驟"""
        ...

class Parsable(Protocol):
    """可解析的協議"""
    
    def parse(self, content: str) -> ParsedLine:
        """解析內容"""
        ...
    
    def is_valid(self, content: str) -> bool:
        """驗證內容是否有效"""
        ...

class Validatable(Protocol):
    """可驗證的協議"""
    
    def validate(self, data: Any) -> Tuple[bool, Optional[str]]:
        """驗證資料"""
        ...

class Configurable(Protocol):
    """可配置的協議"""
    
    def load_config(self, config_path: FilePath) -> ParameterDict:
        """載入配置"""
        ...
    
    def validate_config(self, config: ParameterDict) -> Tuple[bool, List[str]]:
        """驗證配置"""
        ...

# 泛型類型
T = TypeVar('T')
P = TypeVar('P', bound=Parsable)
L = TypeVar('L', bound=Loggable)
V = TypeVar('V', bound=Validatable)

# 協變類型變量（用於 Protocol）
T_co = TypeVar('T_co', covariant=True)

class Repository(Protocol[T]):
    """通用倉庫協議"""

    def save(self, item: T) -> None:
        """保存項目"""
        ...

    def load(self, identifier: str) -> Optional[T]:
        """載入項目"""
        ...

    def delete(self, identifier: str) -> bool:
        """刪除項目"""
        ...

class Parser(Protocol[T_co]):
    """通用解析器協議"""

    def parse(self, content: str) -> T_co:
        """解析內容"""
        ...

    def validate(self, content: str) -> bool:
        """驗證內容"""
        ...

# 函數類型
ErrorHandler = Callable[[Exception, Optional[ErrorContext]], bool]
LineProcessor = Callable[[str, LineNumber], Optional[ParsedLine]]
FileProcessor = Callable[[FilePath], Tuple[RawContent, ValidContent]]
OutputGenerator = Callable[[ValidContent, TitleInfo], str]

# 上下文管理器類型
ConfigManager = ContextManager[ParameterDict]
LogManager = ContextManager[Loggable]

# 迭代器類型
LineIterator = Iterator[Tuple[LineNumber, str]]
ParsedLineIterator = Iterator[ParsedLine]

# 常數類型
class FieldIndices:
    """欄位索引常數"""
    COMMAND_IDX: Literal[0] = 0
    ADDR_IDX: Literal[1] = 1
    DATA_IDX: Literal[2] = 2
    COMMENT_IDX: Literal[3] = 3
    FULL_DAT: Literal[4] = 4

class CommandTypes:
    """命令類型常數"""
    COMMENT: Literal["comment"] = "comment"
    DELAY: Literal["delay"] = "delay"
    CMD: Literal["cmd"] = "cmd"
    WO: Literal["WO"] = "WO"
    RO: Literal["RO"] = "RO"

class LogLevels:
    """日誌級別常數"""
    DEBUG: Literal["DEBUG"] = "DEBUG"
    INFO: Literal["INFO"] = "INFO"
    WARN: Literal["WARN"] = "WARN"
    ERROR: Literal["ERROR"] = "ERROR"
    STEP: Literal["STEP"] = "STEP"

# 聯合類型
ProcessResult = Union[
    Tuple[RawContent, ValidContent, str],  # 成功結果
    Tuple[None, None, str]  # 失敗結果
]

ValidationResult = Union[
    Tuple[Literal[True], None],  # 驗證成功
    Tuple[Literal[False], str]   # 驗證失敗，包含錯誤信息
]

# 回調函數類型
ProgressCallback = Callable[[int, int], None]  # (current, total)
ErrorCallback = Callable[[Exception], bool]  # 返回是否繼續
CompletionCallback = Callable[[ErrorSummary], None]

# 工廠函數類型
LoggerFactory = Callable[[ParameterDict, str], Loggable]
ParserFactory = Callable[[Loggable], Parsable]
ValidatorFactory = Callable[[], Validatable]

# 配置選項類型
class ProcessingOptions(TypedDict, total=False):
    """處理選項類型"""
    continue_on_error: bool
    max_errors: int
    output_format: Literal["txt", "c", "both"]
    validate_data: bool
    generate_summary: bool
    progress_callback: Optional[ProgressCallback]
    error_callback: Optional[ErrorCallback]
    completion_callback: Optional[CompletionCallback]

# 應用程式狀態類型
class ApplicationState(TypedDict):
    """應用程式狀態類型"""
    initialized: bool
    config_loaded: bool
    logger_created: bool
    workflow_ready: bool
    processing: bool
    completed: bool
    error_count: int
    warning_count: int
