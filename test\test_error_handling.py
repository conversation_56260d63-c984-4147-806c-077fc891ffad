"""
測試錯誤處理系統
"""
import pytest
from unittest.mock import Mock, patch
import sys
import os

# 添加 source 目錄到路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'source'))

from exceptions import (
    DbiTransferError, 
    ConfigurationError,
    FileOperationError,
    ParseError,
    ValidationError,
    VersionCompatibilityError,
    ProcessingError,
    OutputError,
    CommandNotSupportedError,
    DataFormatError,
    create_error_from_exception,
    ErrorCodes
)
from validation.error_handler import ErrorHandler, create_error_handler


class TestCustomExceptions:
    """測試自定義異常類"""
    
    def test_dbi_transfer_error_basic(self):
        """測試基礎異常類"""
        error = DbiTransferError("Test error")
        assert str(error) == "Test error"
        assert error.error_code == "DbiTransferError"
        assert error.context == {}
    
    def test_dbi_transfer_error_with_context(self):
        """測試帶上下文的異常"""
        context = {"file": "test.rgt", "line": 10}
        error = DbiTransferError("Test error", "TEST001", context)
        
        assert error.error_code == "TEST001"
        assert error.context == context
        assert "file=test.rgt" in str(error)
        assert "line=10" in str(error)
    
    def test_parse_error_with_line_info(self):
        """測試解析錯誤"""
        error = ParseError(
            "Invalid syntax", 
            line_number=5, 
            line_content="invalid line", 
            file_path="test.rgt"
        )
        
        assert "Line 5" in str(error)
        assert "test.rgt" in str(error)
        assert error.context['line_number'] == 5
        assert error.context['line_content'] == "invalid line"
    
    def test_validation_error(self):
        """測試驗證錯誤"""
        error = ValidationError(
            "Invalid value",
            field_name="output_width",
            field_value="invalid"
        )
        
        assert error.context['field_name'] == "output_width"
        assert error.context['field_value'] == "invalid"
    
    def test_command_not_supported_error(self):
        """測試不支援命令錯誤"""
        error = CommandNotSupportedError(
            "invalid_cmd",
            supported_commands=["set_pin", "get_pin"]
        )
        
        assert "invalid_cmd" in str(error)
        assert "set_pin" in str(error)
        assert error.context['command'] == "invalid_cmd"
    
    def test_error_to_dict(self):
        """測試錯誤轉字典"""
        error = ParseError("Test", line_number=1)
        error_dict = error.to_dict()
        
        assert error_dict['error_type'] == "ParseError"
        assert error_dict['message'] == "Line 1: Test"
        assert error_dict['context']['line_number'] == 1


class TestErrorCreation:
    """測試錯誤創建函數"""
    
    def test_create_from_file_not_found(self):
        """測試從 FileNotFoundError 創建"""
        original = FileNotFoundError("test.txt not found")
        error = create_error_from_exception(original)
        
        assert isinstance(error, FileOperationError)
        assert error.error_code == ErrorCodes.FILE_NOT_FOUND
        assert "test.txt not found" in str(error)
    
    def test_create_from_permission_error(self):
        """測試從 PermissionError 創建"""
        original = PermissionError("Access denied")
        error = create_error_from_exception(original)
        
        assert isinstance(error, FileOperationError)
        assert error.error_code == ErrorCodes.FILE_PERMISSION_DENIED
    
    def test_create_from_value_error(self):
        """測試從 ValueError 創建"""
        original = ValueError("Invalid value")
        error = create_error_from_exception(original)
        
        assert isinstance(error, ValidationError)
        assert error.error_code == ErrorCodes.VALIDATION_FAILED
    
    def test_create_from_generic_exception(self):
        """測試從通用異常創建"""
        original = RuntimeError("Runtime error")
        error = create_error_from_exception(original)
        
        assert isinstance(error, DbiTransferError)
        assert "Runtime error" in str(error)


class TestErrorHandler:
    """測試錯誤處理器"""
    
    def test_init(self, mock_logger):
        """測試初始化"""
        handler = ErrorHandler(mock_logger)
        assert handler.logger == mock_logger
        assert handler.error_count == 0
        assert handler.warning_count == 0
        assert handler.error_history == []
    
    def test_handle_error_with_context_non_fatal(self, mock_logger):
        """測試處理非致命錯誤"""
        handler = ErrorHandler(mock_logger)
        error = ParseError("Test error", line_number=1)
        
        result = handler.handle_error_with_context(error, fatal=False)
        
        assert result == True  # 可以繼續執行
        assert handler.warning_count == 1
        assert handler.error_count == 0
        assert len(handler.error_history) == 1
    
    def test_handle_error_with_context_fatal(self, mock_logger):
        """測試處理致命錯誤"""
        handler = ErrorHandler(mock_logger)
        handler.set_continue_on_error(True)  # 設置繼續執行
        
        error = FileOperationError("File not found")
        result = handler.handle_error_with_context(error, fatal=True)
        
        assert result == True  # 因為設置了繼續執行
        assert handler.error_count == 1
        assert handler.warning_count == 0
    
    def test_command_not_supported(self, mock_logger):
        """測試不支援命令處理"""
        handler = ErrorHandler(mock_logger)
        
        handler.handle_command_not_supported(5, "invalid_command")
        
        assert handler.warning_count == 1
        mock_logger.log.assert_called()
    
    def test_error_summary(self, mock_logger):
        """測試錯誤摘要"""
        handler = ErrorHandler(mock_logger)

        # 設置繼續執行模式，避免測試中退出
        handler.set_continue_on_error(True)

        # 添加一些錯誤
        handler.handle_error_with_context(ParseError("Error 1"), fatal=False)
        handler.handle_error_with_context(ValidationError("Error 2"), fatal=True)
        handler.handle_error_with_context(ParseError("Error 3"), fatal=False)
        
        summary = handler.get_error_summary()
        
        assert summary['total_errors'] == 1
        assert summary['total_warnings'] == 2
        assert 'ParseError' in summary['error_types']
        assert 'ValidationError' in summary['error_types']
        assert summary['error_types']['ParseError'] == 2
        assert summary['error_types']['ValidationError'] == 1
        assert summary['has_fatal_errors'] == True
    
    def test_clear_history(self, mock_logger):
        """測試清除歷史"""
        handler = ErrorHandler(mock_logger)
        
        handler.handle_error_with_context(ParseError("Error"), fatal=False)
        assert handler.warning_count == 1
        assert len(handler.error_history) == 1
        
        handler.clear_history()
        assert handler.warning_count == 0
        assert handler.error_count == 0
        assert len(handler.error_history) == 0
    
    def test_create_error_handler(self, mock_logger):
        """測試工廠函數"""
        handler = create_error_handler(mock_logger)
        assert isinstance(handler, ErrorHandler)
        assert handler.logger == mock_logger


class TestErrorHandlerIntegration:
    """測試錯誤處理器整合"""
    
    def test_multiple_error_types(self, mock_logger):
        """測試處理多種錯誤類型"""
        handler = ErrorHandler(mock_logger)
        handler.set_continue_on_error(True)
        
        # 模擬各種錯誤
        errors = [
            ParseError("Parse error", line_number=1),
            ValidationError("Validation error", field_name="test"),
            FileOperationError("File error", file_path="test.txt"),
            CommandNotSupportedError("cmd", supported_commands=["valid_cmd"])
        ]
        
        for i, error in enumerate(errors):
            fatal = i % 2 == 0  # 交替設置致命/非致命
            handler.handle_error_with_context(error, fatal=fatal)
        
        summary = handler.get_error_summary()
        assert summary['total_errors'] == 2
        assert summary['total_warnings'] == 2
        assert len(summary['error_types']) == 4
    
    def test_error_context_preservation(self, mock_logger):
        """測試錯誤上下文保存"""
        handler = ErrorHandler(mock_logger)
        
        context = {
            'file_path': 'test.rgt',
            'line_number': 10,
            'operation': 'parsing'
        }
        
        error = ParseError("Test error")
        handler.handle_error_with_context(error, context=context, fatal=False)
        
        # 檢查歷史記錄中的上下文
        history = handler.error_history[0]
        assert history['context'] == context
        
        # 檢查日誌調用
        mock_logger.log.assert_called()
        log_calls = mock_logger.log.call_args_list
        assert any("Context:" in str(call) for call in log_calls)
