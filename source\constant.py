from enum import IntEnum

class FieldIndex(IntEnum):
    COMMAND_IDX = 0
    ADDR_IDX = 1
    DATA_IDX = 2
    COMMENT_IDX = 3
    FULL_DAT = 4

# 資料長度常數
class DataLength:
    VALID_LENGTHS = {2, 4, 6, 8}
    BYTE_PAIR_SIZE = 2
    MAX_DATA_LENGTH = 4

# 正則表達式模式
class RegexPatterns:
    HEX_OR_XX = r'(?:[0-9a-fA-F]{2}|XX)+'
    DELAY_PATTERN = r"^delay (\d{1,4})(\.\d{1,4})?$"
    COMMAND_PATTERN = r"#(\w+)"
    REGISTER_PATTERN = r"""
        ^
        (?P<addr>[0-9A-Fa-f]{5,6})     # address: 5~6 hex chars
        =
        (?P<data>.{2}|.{4}|.{6}|.{8})  # data length 2,4,6,8
        (?://(?P<cmd>[A-Z]{2,}))?      # optional cmd
        (?://(?P<comment>[^/].*)?)?    # optional comment, not starting with '/'
        $
        """

# 命令類型
class CommandTypes:
    COMMENT = "comment"
    DELAY = "delay"
    CMD = "cmd"
    WO = "wo"
    RO = "ro"
    SIMPLE_COMMANDS = {COMMENT, DELAY, CMD}
    REGISTER_COMMANDS = {WO, RO}

# 預設值
class Defaults:
    LAST_1A = "XX"
    LAST_1B = "XX XX XX XX"
    ALIGN_LEFT = "L"
    PADDING_VALUE = "XX"

# 檔案和路徑
class FilePaths:
    LOG_DIR = "log"
    CASES_DIR = "cases"
    RESULTS_DIR = "results"
    RGT_DIR = "rgt"
    INCLUDE_FILE = "open_ddi_p_mode.op2.txt"

# 日誌級別
class LogLevels:
    DEBUG = "DEBUG"
    INFO = "INFO"
    NOTE = "NOTE"
    WARN = "WARN"
    ERROR = "ERROR"
    ALL_LEVELS = ["debug", "warn", "note", "error", "info"]