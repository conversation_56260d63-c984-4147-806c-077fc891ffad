#!/usr/bin/env python3
"""
測試輸入等待修復

驗證在測試環境中是否正確跳過用戶輸入等待
"""
import sys
import os

# 添加源碼路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'source'))

def test_testing_mode_detection():
    """測試測試模式檢測"""
    print("🧪 測試模式檢測測試")
    print("=" * 40)
    
    try:
        from application import _is_testing_environment, Application
        
        # 檢測當前是否在測試環境
        is_testing = _is_testing_environment()
        print(f"當前是否在測試環境: {is_testing}")
        
        # 創建應用實例
        app = Application()
        print(f"應用程序測試模式: {app.testing_mode}")
        
        # 手動指定測試模式
        app_test = Application(testing_mode=True)
        print(f"手動指定測試模式: {app_test.testing_mode}")
        
        # 手動指定非測試模式
        app_normal = Application(testing_mode=False)
        print(f"手動指定非測試模式: {app_normal.testing_mode}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_error_handler():
    """測試錯誤處理器"""
    print("\n🧪 錯誤處理器測試")
    print("=" * 40)
    
    try:
        from validation.error_handler import ErrorHandler, create_error_handler
        
        # 創建測試模式的錯誤處理器
        handler_test = ErrorHandler(testing_mode=True)
        print(f"錯誤處理器測試模式: {handler_test.testing_mode}")
        
        # 使用工廠函數創建
        handler_factory = create_error_handler(testing_mode=True)
        print(f"工廠函數創建的錯誤處理器測試模式: {handler_factory.testing_mode}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_wait_functions():
    """測試等待函數"""
    print("\n🧪 等待函數測試")
    print("=" * 40)
    
    try:
        from application import _wait_for_exit
        
        print("測試 _wait_for_exit 函數...")
        # 這應該不會等待輸入，因為我們在測試環境中
        _wait_for_exit()
        print("✅ _wait_for_exit 函數正常返回")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_application_methods():
    """測試應用程序方法"""
    print("\n🧪 應用程序方法測試")
    print("=" * 40)
    
    try:
        from application import Application
        
        # 創建測試模式的應用程序
        app = Application(testing_mode=True)
        
        print("測試 _wait_for_user_exit 方法...")
        app._wait_for_user_exit()
        print("✅ _wait_for_user_exit 方法正常返回")
        
        print("測試 _handle_user_interruption 方法...")
        app._handle_user_interruption()
        print("✅ _handle_user_interruption 方法正常返回")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔧 輸入等待修復驗證測試")
    print("=" * 50)
    
    # 運行測試
    tests = [
        ("測試模式檢測", test_testing_mode_detection),
        ("錯誤處理器", test_error_handler),
        ("等待函數", test_wait_functions),
        ("應用程序方法", test_application_methods),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 測試通過")
            else:
                failed += 1
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} 測試異常: {e}")
    
    # 顯示結果
    print("\n" + "=" * 50)
    print("📊 測試結果:")
    print(f"✅ 通過: {passed}")
    print(f"❌ 失敗: {failed}")
    print(f"📋 總計: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 所有測試通過！輸入等待修復成功。")
        print("✅ 在測試環境中不再等待用戶輸入")
    else:
        print(f"\n⚠️  {failed} 個測試失敗，請檢查修復。")
    
    return 0 if failed == 0 else 1

if __name__ == "__main__":
    sys.exit(main())
