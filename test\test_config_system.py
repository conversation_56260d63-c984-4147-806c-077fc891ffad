"""
測試配置管理系統
"""
import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock

# 添加源碼路徑
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'source'))

from config import (
    ConfigManager, ConfigLoader, ConfigValidator, ConfigSchema,
    CONFIG_LOADER, CONFIG_VALIDATOR, CONFIG_SCHEMA,
    ConfigValidationError, create_config_manager
)
from config.tools import ConfigTools


class TestConfigSchema:
    """測試配置模式"""
    
    def test_schema_initialization(self):
        """測試模式初始化"""
        schema = ConfigSchema()
        
        assert len(schema.sections) > 0
        assert "basic" in schema.sections
        assert "files" in schema.sections
        assert "logging" in schema.sections
    
    def test_get_defaults(self):
        """測試獲取預設值"""
        defaults = CONFIG_SCHEMA.get_defaults()

        assert "version" in defaults
        # proj 沒有預設值，因為它是必需欄位但需要用戶提供
        assert "proj" not in defaults
        assert defaults["version"] == "0.01"
    
    def test_validate_config(self):
        """測試配置驗證"""
        valid_config = {
            "version": "1.0",
            "proj": "TEST_PROJECT",
            "output_width": 100,
            "commands": ["set_pin", "get_pin"]  # 必需欄位
        }
        
        is_valid, errors = CONFIG_SCHEMA.validate_config(valid_config)
        if not is_valid:
            print(f"驗證錯誤: {errors}")
        assert is_valid
        assert len(errors) == 0


class TestConfigValidator:
    """測試配置驗證器"""
    
    def test_valid_config(self):
        """測試有效配置"""
        config = {
            "version": "1.0",
            "proj": "VALID_PROJECT",
            "output_width": 100,
            "debug": True,
            "info": True,  # 啟用至少一個日誌級別
            "commands": ["set_pin", "get_pin"],
            "product_id": "PROD_001"
        }

        is_valid, errors = CONFIG_VALIDATOR.validate(config)
        if not is_valid:
            print(f"驗證錯誤: {errors}")
        assert is_valid
        assert len(errors) == 0
    
    def test_invalid_project_name(self):
        """測試無效專案名稱"""
        config = {
            "version": "1.0",
            "proj": "invalid_project",  # 應該是大寫開頭
            "commands": ["set_pin", "get_pin"]  # 必需欄位
        }
        
        is_valid, errors = CONFIG_VALIDATOR.validate(config)
        assert not is_valid
        assert any("專案名稱" in error for error in errors)
    
    def test_invalid_output_width(self):
        """測試無效輸出寬度"""
        config = {
            "version": "1.0",  # 必需欄位
            "proj": "VALID_PROJECT",
            "commands": ["set_pin", "get_pin"],  # 必需欄位
            "output_width": 300  # 超出範圍
        }
        
        is_valid, errors = CONFIG_VALIDATOR.validate(config)
        assert not is_valid
        assert any("輸出寬度" in error for error in errors)
    
    def test_invalid_product_id(self):
        """測試無效產品識別碼"""
        config = {
            "version": "1.0",  # 必需欄位
            "proj": "VALID_PROJECT",
            "commands": ["set_pin", "get_pin"],  # 必需欄位
            "product_id": "invalid@product"  # 包含非法字符
        }

        is_valid, errors = CONFIG_VALIDATOR.validate(config)
        assert not is_valid
        assert any("產品識別碼" in error for error in errors)

    def test_valid_product_id(self):
        """測試有效產品識別碼"""
        config = {
            "version": "1.0",  # 必需欄位
            "proj": "VALID_PROJECT",
            "commands": ["set_pin", "get_pin"],  # 必需欄位
            "product_id": "PROD_001-TEST_V2"  # 有效格式
        }

        is_valid, errors = CONFIG_VALIDATOR.validate(config)
        if not is_valid:
            print(f"驗證錯誤: {errors}")
        assert is_valid
        assert len(errors) == 0

    def test_validation_report(self):
        """測試驗證報告"""
        config = {
            "version": "1.0",  # 必需欄位
            "proj": "invalid",
            "commands": ["set_pin", "get_pin"]  # 必需欄位
        }
        report = CONFIG_VALIDATOR.get_validation_report(config)

        assert "配置驗證報告" in report
        assert "❌" in report or "✅" in report


class TestConfigLoader:
    """測試配置載入器"""
    
    def test_load_simple_format(self):
        """測試載入簡單格式"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("version=1.0\n")
            f.write("proj=TEST_PROJECT\n")
            f.write("debug=true\n")
            f.write("output_width=120\n")
            f.write("commands=set_pin,get_pin\n")  # 必需欄位
            f.flush()
            temp_file_name = f.name

        # 文件已關閉，現在可以安全地讀取和刪除
        config = CONFIG_LOADER.load_from_file(temp_file_name)

        assert config["version"] == 1.0  # 載入器會轉換為浮點數
        assert config["proj"] == "TEST_PROJECT"
        assert config["debug"] is True  # 載入器會轉換為布林值
        assert config["output_width"] == 120

        os.unlink(temp_file_name)
    
    def test_load_json_format(self):
        """測試載入 JSON 格式"""
        test_config = {
            "version": "1.0",
            "proj": "TEST_PROJECT",
            "debug": True,
            "output_width": 120
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_config, f)
            f.flush()
            temp_file_name = f.name

        # 文件已關閉，現在可以安全地讀取和刪除
        config = CONFIG_LOADER.load_from_file(temp_file_name)

        assert config["version"] == "1.0"
        assert config["proj"] == "TEST_PROJECT"
        assert config["debug"] is True
        assert config["output_width"] == 120

        os.unlink(temp_file_name)
    
    def test_load_from_env(self):
        """測試從環境變數載入"""
        import os
        
        # 設置測試環境變數
        os.environ["DBI_TEST_VAR"] = "test_value"
        os.environ["DBI_DEBUG"] = "true"
        
        config = CONFIG_LOADER.load_from_env("DBI_")
        
        assert config.get("test_var") == "test_value"
        assert config.get("debug") is True  # 載入器會轉換為布林值
        
        # 清理環境變數
        del os.environ["DBI_TEST_VAR"]
        del os.environ["DBI_DEBUG"]
    
    def test_load_layered_config(self):
        """測試分層配置載入"""
        # 創建測試配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("version=1.0\n")
            f.write("proj=FILE_PROJECT\n")
            f.write("commands=set_pin,get_pin\n")  # 必需欄位
            f.flush()
            temp_file_name = f.name

        # 設置環境變數
        os.environ["DBI_PROJ"] = "ENV_PROJECT"

        # 載入分層配置
        config = CONFIG_LOADER.load_layered_config(
            config_file=temp_file_name,
            env_prefix="DBI_"
        )

        # 環境變數應該覆蓋文件配置
        assert config["proj"] == "ENV_PROJECT"
        assert config["version"] == 1.0  # 從文件載入會轉換為浮點數

        # 清理
        os.unlink(temp_file_name)
        del os.environ["DBI_PROJ"]


class TestConfigManager:
    """測試配置管理器"""
    
    def test_config_manager_creation(self):
        """測試配置管理器創建"""
        manager = ConfigManager()
        
        assert not manager.is_loaded
        assert len(manager.config) == 0
    
    def test_load_config(self):
        """測試載入配置"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("version=1.0\n")
            f.write("proj=TEST_PROJECT\n")
            f.write("commands=set_pin,get_pin\n")  # 必需欄位
            f.flush()
            temp_file_name = f.name

        manager = ConfigManager()
        manager.load_config(temp_file_name, validate=False)  # 禁用驗證以避免複雜的驗證規則

        assert manager.is_loaded
        assert manager.get("version") == 1.0  # 從文件載入會轉換為浮點數
        assert manager.get("proj") == "TEST_PROJECT"

        os.unlink(temp_file_name)
    
    def test_set_and_get(self):
        """測試設置和獲取配置"""
        manager = ConfigManager()
        manager.config = {"version": "1.0"}  # 初始化配置
        
        manager.set("debug", True, validate=False)
        assert manager.get("debug") is True
        
        # 測試預設值
        assert manager.get("nonexistent", "default") == "default"
    
    def test_update_config(self):
        """測試批量更新配置"""
        manager = ConfigManager()
        manager.config = {"version": "1.0"}
        
        updates = {
            "debug": True,
            "proj": "NEW_PROJECT"
        }
        
        manager.update(updates, validate=False)
        
        assert manager.get("debug") is True
        assert manager.get("proj") == "NEW_PROJECT"
    
    def test_temporary_config(self):
        """測試臨時配置"""
        manager = ConfigManager()
        manager.config = {"debug": False, "proj": "ORIGINAL"}
        
        with manager.temporary_config({"debug": True, "proj": "TEMP"}):
            assert manager.get("debug") is True
            assert manager.get("proj") == "TEMP"
        
        # 離開上下文後應該恢復
        assert manager.get("debug") is False
        assert manager.get("proj") == "ORIGINAL"
    
    def test_change_callbacks(self):
        """測試配置變更回調"""
        manager = ConfigManager()
        manager.config = {}
        
        changes = []
        
        def callback(key, old_value, new_value):
            changes.append((key, old_value, new_value))
        
        manager.add_change_callback(callback)
        manager.set("debug", True, validate=False)
        
        assert len(changes) == 1
        assert changes[0] == ("debug", None, True)

    def test_remove_change_callback(self):
        """測試移除配置變更回調"""
        manager = ConfigManager()
        manager.config = {}

        changes = []

        def callback(key, old_value, new_value):
            changes.append((key, old_value, new_value))

        manager.add_change_callback(callback)
        manager.remove_change_callback(callback)
        manager.set("debug", True, validate=False)

        # 回調被移除後不應該被調用
        assert len(changes) == 0

    def test_callback_exception_handling(self):
        """測試回調異常處理"""
        from unittest.mock import Mock
        logger = Mock()

        manager = ConfigManager(logger)
        manager.config = {}

        def failing_callback(key, old_value, new_value):
            raise Exception("Callback error")

        manager.add_change_callback(failing_callback)
        manager.set("debug", True, validate=False)

        # 檢查錯誤日誌
        logger.log.assert_called()

    def test_save_config_with_path(self):
        """測試保存配置到指定路徑"""
        from unittest.mock import Mock
        logger = Mock()

        manager = ConfigManager(logger)
        manager.config = {"version": "0.01", "proj": "TEST"}

        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_file_name = f.name

        os.unlink(temp_file_name)  # 刪除文件讓保存方法創建它

        manager.save_config(temp_file_name)

        assert Path(temp_file_name).exists()
        logger.log.assert_called()

        os.unlink(temp_file_name)

    def test_save_config_without_path_error(self):
        """測試保存配置時沒有路徑的錯誤"""
        manager = ConfigManager()
        manager.config = {"version": "0.01"}

        try:
            manager.save_config()  # 沒有指定路徑且沒有載入文件
            assert False, "應該拋出 ValueError"
        except ValueError:
            pass  # 預期的異常

    def test_save_config_with_loaded_file(self):
        """測試保存配置到載入的文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("version=1.0\n")
            f.write("proj=TEST_PROJECT\n")
            f.flush()
            temp_file_name = f.name

        manager = ConfigManager()
        manager.load_config(temp_file_name, validate=False)

        # 修改配置
        manager.set("debug", True, validate=False)

        # 保存到原文件
        manager.save_config()

        # 重新載入驗證
        new_manager = ConfigManager()
        new_manager.load_config(temp_file_name, validate=False)
        assert new_manager.get("debug") is True

        os.unlink(temp_file_name)

    def test_get_validation_report(self):
        """測試獲取驗證報告"""
        manager = ConfigManager()
        manager.config = {"version": "0.01", "proj": "TEST"}

        report = manager.get_validation_report()
        assert isinstance(report, str)
        assert len(report) > 0

    def test_reset_to_defaults(self):
        """測試重置為預設配置"""
        from unittest.mock import Mock
        logger = Mock()

        manager = ConfigManager(logger)
        manager.config = {"custom_setting": "custom_value"}

        manager.reset_to_defaults()

        # 檢查是否重置為預設值
        assert "custom_setting" not in manager.config
        assert "version" in manager.config  # 預設配置應該包含版本
        logger.log.assert_called()

    def test_get_config_summary(self):
        """測試獲取配置摘要"""
        manager = ConfigManager()
        manager.config = {"version": "0.01", "proj": "TEST"}
        manager.is_loaded = True

        summary = manager.get_config_summary()

        assert summary["total_settings"] == 2
        assert summary["is_loaded"] is True
        assert "validation_status" in summary
        assert "sections" in summary

    def test_attribute_access(self):
        """測試屬性訪問"""
        manager = ConfigManager()
        manager.config = {"debug": True, "proj": "TEST"}

        # 測試獲取屬性
        assert manager.debug is True
        assert manager.proj == "TEST"

        # 測試設置屬性（禁用驗證以避免驗證錯誤）
        manager.set("output_width", 120, validate=False)
        assert manager.get("output_width") == 120

        # 測試不存在的屬性
        try:
            _ = manager.nonexistent_attr
            assert False, "應該拋出 AttributeError"
        except AttributeError:
            pass  # 預期的異常

    def test_context_manager(self):
        """測試上下文管理器"""
        manager = ConfigManager()

        with manager as ctx:
            assert ctx is manager

        # 上下文退出後應該正常
        assert manager is not None

    def test_update_with_validation_error(self):
        """測試批量更新時驗證錯誤"""
        manager = ConfigManager()
        manager.config = {"version": "0.01", "proj": "VALID_PROJECT", "commands": ["set_pin"]}

        old_config = manager.config.copy()

        # 嘗試無效更新
        try:
            manager.update({"proj": "invalid_project_name"}, validate=True)
            assert False, "應該拋出 ConfigValidationError"
        except ConfigValidationError:
            pass  # 預期的異常

        # 檢查配置是否被恢復
        assert manager.config == old_config


class TestConfigManagerUtilities:
    """測試配置管理器的便捷函數"""

    def test_find_config_file_project_specific(self):
        """測試查找專案特定配置文件"""
        from config.manager import _find_config_file

        # 創建專案特定配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as f:
            f.write("version=0.01\n")
            f.flush()
            temp_file = f.name

        # 重命名為專案特定名稱（文件已關閉）
        project_file = temp_file.replace('.conf', '_icna3611.conf')
        os.rename(temp_file, project_file)

        try:
            # 應該找到專案特定文件
            found_file = _find_config_file()
            # 注意：由於我們在臨時目錄創建文件，可能找不到，這是正常的
            # 主要測試函數不會拋出異常
            assert found_file is None or isinstance(found_file, Path)
        finally:
            if Path(project_file).exists():
                os.unlink(project_file)

    def test_find_config_file_modern(self):
        """測試查找現代化配置文件"""
        from config.manager import _find_config_file

        # 在當前目錄創建現代化配置文件
        modern_file = "dbi.conf"

        with open(modern_file, 'w') as f:
            f.write("version=0.01\n")

        try:
            found_file = _find_config_file()
            assert found_file is not None
            assert found_file.name == "dbi.conf"
        finally:
            if Path(modern_file).exists():
                os.unlink(modern_file)

    def test_find_config_file_legacy(self):
        """測試查找傳統配置文件"""
        from config.manager import _find_config_file

        # 在當前目錄創建傳統配置文件
        legacy_file = "para.txt"

        with open(legacy_file, 'w') as f:
            f.write("version=0.01\n")

        try:
            found_file = _find_config_file()
            assert found_file is not None
            assert found_file.name == "para.txt"
        finally:
            if Path(legacy_file).exists():
                os.unlink(legacy_file)

    def test_find_config_file_none(self):
        """測試沒有找到配置文件"""
        from config.manager import _find_config_file

        # 確保當前目錄沒有配置文件
        found_file = _find_config_file()
        # 可能為 None 或找到其他配置文件
        assert found_file is None or isinstance(found_file, Path)

    def test_detect_project_from_config(self):
        """測試從配置文件檢測專案名稱"""
        from config.manager import _detect_project_from_config

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("version=0.01\n")
            f.write("proj=DETECTED_PROJECT\n")
            f.flush()
            temp_file_name = f.name

        try:
            project = _detect_project_from_config(Path(temp_file_name))
            assert project == "DETECTED_PROJECT"
        finally:
            os.unlink(temp_file_name)

    def test_detect_project_from_config_error(self):
        """測試從無效配置文件檢測專案名稱"""
        from config.manager import _detect_project_from_config

        # 使用不存在的文件
        project = _detect_project_from_config(Path("nonexistent.txt"))
        assert project is None


class TestCreateConfigManager:
    """測試創建配置管理器函數"""

    def test_create_without_file(self):
        """測試不指定文件創建"""
        manager = create_config_manager(auto_load=False)

        assert isinstance(manager, ConfigManager)
        assert not manager.is_loaded

    def test_create_with_file(self):
        """測試使用文件創建"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("version=1.0\n")
            f.write("proj=TEST_PROJECT\n")
            f.write("commands=set_pin,get_pin\n")  # 必需欄位
            f.flush()
            temp_file_name = f.name

        # 創建配置管理器但禁用驗證，因為測試配置可能不完整
        manager = ConfigManager()
        manager.load_config(temp_file_name, validate=False)

        assert manager.is_loaded
        assert manager.get("version") == 1.0  # 從文件載入會轉換為浮點數

        os.unlink(temp_file_name)

    def test_create_with_logger(self):
        """測試帶日誌器創建"""
        from unittest.mock import Mock
        logger = Mock()

        manager = create_config_manager(logger=logger, auto_load=False)

        assert manager.logger == logger


class TestConfigTools:
    """測試配置工具"""

    def test_generate_config_simple_format(self):
        """測試生成簡單格式配置"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_file_name = f.name

        ConfigTools.generate_config(temp_file_name, "simple", "default")

        # 檢查文件是否存在且有內容
        assert Path(temp_file_name).exists()
        assert Path(temp_file_name).stat().st_size > 0

        # 檢查內容
        with open(temp_file_name, 'r', encoding='utf-8') as rf:
            content = rf.read()
            assert "version=" in content
            assert "proj=" in content
            assert "# DBI Transfer Tool 配置文件" in content

        os.unlink(temp_file_name)

    def test_generate_config_json_format(self):
        """測試生成 JSON 格式配置"""
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            temp_file_name = f.name

        ConfigTools.generate_config(temp_file_name, "json", "default")

        # 檢查文件是否存在且有內容
        assert Path(temp_file_name).exists()
        assert Path(temp_file_name).stat().st_size > 0

        # 檢查是否為有效的 JSON
        with open(temp_file_name, 'r', encoding='utf-8') as rf:
            content = json.load(rf)
            assert "version" in content
            assert "proj" in content

        os.unlink(temp_file_name)

    def test_generate_config_ini_format(self):
        """測試生成 INI 格式配置"""
        with tempfile.NamedTemporaryFile(suffix='.ini', delete=False) as f:
            temp_file_name = f.name

        ConfigTools.generate_config(temp_file_name, "ini", "default")

        # 檢查文件是否存在且有內容
        assert Path(temp_file_name).exists()
        assert Path(temp_file_name).stat().st_size > 0

        # 檢查內容
        with open(temp_file_name, 'r', encoding='utf-8') as rf:
            content = rf.read()
            assert "[" in content  # INI 格式應該有區段

        os.unlink(temp_file_name)

    def test_generate_config_with_template_not_exists(self):
        """測試使用不存在的模板生成配置"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_file_name = f.name

        # 使用不存在的模板，應該使用預設配置
        ConfigTools.generate_config(temp_file_name, "simple", "nonexistent")

        # 檢查文件是否存在
        assert Path(temp_file_name).exists()

        os.unlink(temp_file_name)

    def test_validate_config_tool_valid(self):
        """測試驗證有效配置文件"""
        # 使用生成的配置文件進行測試，這樣可以確保格式正確
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_file_name = f.name

        # 生成一個有效的配置文件
        ConfigTools.generate_config(temp_file_name, "simple", "default")

        # 驗證應該成功（不拋出異常）
        try:
            ConfigTools.validate_config(temp_file_name)
            validation_passed = True
        except SystemExit:
            validation_passed = False

        assert validation_passed
        os.unlink(temp_file_name)

    def test_validate_config_tool_invalid(self):
        """測試驗證無效配置文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("invalid_field=invalid_value\n")
            f.write("proj=invalid_project_name\n")  # 無效的專案名稱
            f.flush()
            temp_file_name = f.name

        # 驗證應該失敗
        try:
            ConfigTools.validate_config(temp_file_name)
            validation_passed = True
        except SystemExit:
            validation_passed = False

        assert not validation_passed
        os.unlink(temp_file_name)

    def test_validate_config_tool_strict_mode(self):
        """測試嚴格模式驗證"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_file_name = f.name

        ConfigTools.generate_config(temp_file_name, "simple", "default")

        # 嚴格模式驗證
        try:
            ConfigTools.validate_config(temp_file_name, strict=True)
            validation_passed = True
        except SystemExit:
            validation_passed = False

        assert validation_passed
        os.unlink(temp_file_name)

    def test_validate_config_tool_file_not_exists(self):
        """測試驗證不存在的配置文件"""
        try:
            ConfigTools.validate_config("nonexistent_file.txt")
            validation_passed = True
        except SystemExit:
            validation_passed = False

        assert not validation_passed

    def test_migrate_config_with_backup(self):
        """測試配置遷移（帶備份）"""
        # 創建舊配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("version='0.01'\n")
            f.write("proj=OLD_PROJECT\n")
            f.write("debug=true\n")
            f.write("old_setting=old_value\n")  # 這個設定應該被跳過
            f.flush()
            old_file = f.name

        new_file = old_file.replace('.txt', '_new.txt')
        backup_file = f"{old_file}.backup"

        try:
            ConfigTools.migrate_config(old_file, new_file, backup=True)

            # 檢查新文件是否存在
            assert Path(new_file).exists()

            # 檢查備份文件是否存在
            assert Path(backup_file).exists()

            # 檢查新配置內容
            migrated_config = CONFIG_LOADER.load_from_file(new_file)
            assert migrated_config["proj"] == "OLD_PROJECT"  # 保留舊值
            assert "old_setting" not in migrated_config  # 無效設定被移除

        finally:
            # 清理文件
            for file_path in [new_file, backup_file]:
                if Path(file_path).exists():
                    os.unlink(file_path)

    def test_migrate_config_without_backup(self):
        """測試配置遷移（不備份）"""
        # 創建舊配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("version='0.01'\n")
            f.write("proj=OLD_PROJECT\n")
            f.flush()
            old_file = f.name

        new_file = old_file.replace('.txt', '_new.txt')
        backup_file = f"{old_file}.backup"

        try:
            ConfigTools.migrate_config(old_file, new_file, backup=False)

            # 檢查新文件是否存在
            assert Path(new_file).exists()

            # 檢查備份文件不存在
            assert not Path(backup_file).exists()

            # 檢查舊文件仍然存在（因為沒有備份）
            assert Path(old_file).exists()

        finally:
            # 清理文件
            for file_path in [old_file, new_file]:
                if Path(file_path).exists():
                    os.unlink(file_path)

    def test_migrate_config_file_not_exists(self):
        """測試遷移不存在的配置文件"""
        try:
            ConfigTools.migrate_config("nonexistent.txt", "new.txt")
            migration_failed = False
        except SystemExit:
            migration_failed = True

        assert migration_failed

    def test_compare_configs_identical(self):
        """測試比較相同的配置文件"""
        # 創建兩個相同的配置文件
        config_content = "version='0.01'\nproj=TEST_PROJECT\n"

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f1:
            f1.write(config_content)
            f1.flush()
            file1 = f1.name

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f2:
            f2.write(config_content)
            f2.flush()
            file2 = f2.name

        try:
            # 比較應該成功（不拋出異常）
            ConfigTools.compare_configs(file1, file2)
            comparison_passed = True
        except SystemExit:
            comparison_passed = False

        assert comparison_passed

        # 清理文件
        os.unlink(file1)
        os.unlink(file2)

    def test_compare_configs_different(self):
        """測試比較不同的配置文件"""
        # 創建兩個不同的配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f1:
            f1.write("version='0.01'\nproj=PROJECT1\n")
            f1.flush()
            file1 = f1.name

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f2:
            f2.write("version='0.02'\nproj=PROJECT2\n")
            f2.flush()
            file2 = f2.name

        try:
            # 比較應該成功（不拋出異常）
            ConfigTools.compare_configs(file1, file2)
            comparison_passed = True
        except SystemExit:
            comparison_passed = False

        assert comparison_passed

        # 清理文件
        os.unlink(file1)
        os.unlink(file2)

    def test_compare_configs_file_not_exists(self):
        """測試比較不存在的配置文件"""
        try:
            ConfigTools.compare_configs("nonexistent1.txt", "nonexistent2.txt")
            comparison_failed = False
        except SystemExit:
            comparison_failed = True

        assert comparison_failed

    def test_show_schema(self):
        """測試顯示配置模式"""
        try:
            # 顯示模式應該成功（不拋出異常）
            ConfigTools.show_schema()
            schema_shown = True
        except Exception:
            schema_shown = False

        assert schema_shown


class TestConfigToolsUtilities:
    """測試配置工具的便捷函數"""

    def test_quick_validate_valid_config(self):
        """測試快速驗證有效配置"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_file_name = f.name

        # 生成有效配置
        ConfigTools.generate_config(temp_file_name, "simple", "default")

        # 快速驗證
        from config.tools import quick_validate
        result = quick_validate(temp_file_name)

        assert result is True
        os.unlink(temp_file_name)

    def test_quick_validate_invalid_config(self):
        """測試快速驗證無效配置"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("invalid_config=true\n")
            f.flush()
            temp_file_name = f.name

        # 快速驗證
        from config.tools import quick_validate
        result = quick_validate(temp_file_name)

        assert result is False
        os.unlink(temp_file_name)

    def test_quick_generate_config(self):
        """測試快速生成配置"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_file_name = f.name

        # 刪除臨時文件，讓 quick_generate 創建它
        os.unlink(temp_file_name)

        # 快速生成
        from config.tools import quick_generate
        result = quick_generate(temp_file_name)

        assert result is True
        assert Path(temp_file_name).exists()

        os.unlink(temp_file_name)


class TestConfigToolsCommandLine:
    """測試配置工具的命令行接口"""

    def test_main_function_no_command(self):
        """測試主函數無命令參數"""
        import sys
        from unittest.mock import patch
        from config.tools import main

        # 模擬無命令參數
        with patch.object(sys, 'argv', ['config_tools']):
            try:
                main()
                execution_completed = True
            except SystemExit:
                execution_completed = True  # 正常退出
            except Exception:
                execution_completed = False

        assert execution_completed

    def test_main_function_generate_command(self):
        """測試主函數生成命令"""
        import sys
        from unittest.mock import patch
        from config.tools import main

        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_file_name = f.name

        # 刪除臨時文件，讓生成命令創建它
        os.unlink(temp_file_name)

        # 模擬生成命令
        with patch.object(sys, 'argv', ['config_tools', 'generate', '-o', temp_file_name]):
            try:
                main()
                execution_completed = True
            except SystemExit:
                execution_completed = True  # 正常退出
            except Exception:
                execution_completed = False

        assert execution_completed

        # 檢查文件是否被創建
        if Path(temp_file_name).exists():
            os.unlink(temp_file_name)

    def test_main_function_schema_command(self):
        """測試主函數模式命令"""
        import sys
        from unittest.mock import patch
        from config.tools import main

        # 模擬模式命令
        with patch.object(sys, 'argv', ['config_tools', 'schema']):
            try:
                main()
                execution_completed = True
            except SystemExit:
                execution_completed = True  # 正常退出
            except Exception:
                execution_completed = False

        assert execution_completed

    def test_main_function_keyboard_interrupt(self):
        """測試主函數鍵盤中斷"""
        import sys
        from unittest.mock import patch, MagicMock
        from config.tools import main

        # 模擬鍵盤中斷
        with patch.object(sys, 'argv', ['config_tools', 'generate']):
            with patch('config.tools.ConfigTools.generate_config', side_effect=KeyboardInterrupt()):
                try:
                    main()
                    execution_completed = False
                except SystemExit as e:
                    execution_completed = (e.code == 1)  # 應該以錯誤碼1退出
                except Exception:
                    execution_completed = False

        assert execution_completed


class TestConfigToolsErrorHandling:
    """測試配置工具的錯誤處理"""

    def test_generate_config_save_error(self):
        """測試生成配置時保存錯誤"""
        from unittest.mock import patch

        # 模擬保存錯誤
        with patch('config.tools.CONFIG_LOADER.save_to_file', side_effect=Exception("Save error")):
            try:
                ConfigTools.generate_config("/invalid/path/config.txt")
                error_handled = False
            except SystemExit:
                error_handled = True

        assert error_handled

    def test_validate_config_load_error(self):
        """測試驗證配置時載入錯誤"""
        # 使用不存在的文件路徑
        try:
            ConfigTools.validate_config("/nonexistent/path/config.txt")
            error_handled = False
        except SystemExit:
            error_handled = True

        assert error_handled

    def test_migrate_config_load_error(self):
        """測試遷移配置時載入錯誤"""
        try:
            ConfigTools.migrate_config("/nonexistent/old.txt", "/tmp/new.txt")
            error_handled = False
        except SystemExit:
            error_handled = True

        assert error_handled

    def test_compare_configs_load_error(self):
        """測試比較配置時載入錯誤"""
        try:
            ConfigTools.compare_configs("/nonexistent/file1.txt", "/nonexistent/file2.txt")
            error_handled = False
        except SystemExit:
            error_handled = True

        assert error_handled


class TestCreateConfigManager:
    """測試配置管理器工廠函數"""
    
    def test_create_with_file(self):
        """測試使用文件創建"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("version=1.0\n")
            f.write("proj=TEST_PROJECT\n")
            f.write("commands=set_pin,get_pin\n")  # 必需欄位
            f.flush()
            temp_file_name = f.name

        # 創建配置管理器但禁用驗證，因為測試配置可能不完整
        manager = ConfigManager()
        manager.load_config(temp_file_name, validate=False)

        assert manager.is_loaded
        assert manager.get("version") == 1.0  # 從文件載入會轉換為浮點數

        os.unlink(temp_file_name)
    
    def test_create_without_file(self):
        """測試不使用文件創建"""
        manager = create_config_manager(auto_load=False)
        
        assert not manager.is_loaded
        assert len(manager.config) == 0


# 整合測試
class TestConfigIntegration:
    """配置系統整合測試"""
    
    def test_full_workflow(self):
        """測試完整工作流程"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            temp_file_name = f.name

        # 1. 生成配置
        ConfigTools.generate_config(temp_file_name)

        # 2. 載入配置
        manager = create_config_manager(temp_file_name)

        # 3. 修改配置
        manager.set("debug", True, validate=False)
        manager.set("proj", "INTEGRATION_TEST", validate=False)

        # 4. 保存配置
        manager.save_config()

        # 5. 重新載入驗證
        new_manager = create_config_manager(temp_file_name)
        assert new_manager.get("debug") is True
        assert new_manager.get("proj") == "INTEGRATION_TEST"

        os.unlink(temp_file_name)


if __name__ == "__main__":
    pytest.main([__file__])
