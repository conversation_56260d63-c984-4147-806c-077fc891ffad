LPAT OPEN_DDI_P_MODE
RDX 10
CHANNEL 140,141,180,78,51,115,26,27,28,29,30,31,32,33,122,71,58,72,185,120,73,56,136,57,52,53,76,182,139,144,181,116,75,135,186,121,77,138,55,74,119,184,137
CFPF

;                            n nDTTD DDDDDDDD GGGGGGGG SS E IC EH PDD F TTTT FFFFFF
;                            R CEHVC BBBBBBBB PPPPPPPP WW X MP XZ SSS P EEEE       
;                            E S SSL 00000000 OOOOOOOO II T 1H TO WWW S SSSS SSSSSS
;                            S  D  K 76543210 76543210 RR    Y DP AAA E TTTT PPPPPP
;                            E  nWR              T      2 O  E VT PPP N 4320 IIIIII
;                            T  CRD              E        S  N D   10              
;                                                S        C    D             SSSRWC
;                                                T             E             CIOEPS
;                                                1             N             L  S  
;                                                                            K  E  
;                                                                               T  
;
; T1 = 10MHz T2 = 10MHz T3 = 1MHz T4 = 60MHz 


; Log: This configue to change TP_boot_state to DDI_PRGM
    NOP                /T3 ! 1 00011 11111101 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0000][C:FD]   ; 
    NOP                /T3 ! 1 01011 01011010 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0001][D:5A]  0; 
    NOP                /T3 ! 1 01011 01011010 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0002][D:5A]  1; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0003]         ; 
    NOP                /T3 ! 1 00011 10011111 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0004][C:9F]   ; 
    NOP                /T3 ! 1 01011 00000010 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0005][D:02]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0006]         ; 
    NOP                /T3 ! 1 00011 10011011 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0007][C:9B]   ; 
    NOP                /T3 ! 1 01011 00010001 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0008][D:11]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0009]         ; 
    NOP                /T3 ! 1 00011 10111000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0010][C:B8]   ; 
    NOP                /T3 ! 1 01011 01011010 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0011][D:5A]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0012]         ; 
    NOP                /T3 ! 1 00011 10011011 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0013][C:9B]   ; 
    NOP                /T3 ! 1 01011 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0014][D:00]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0015]         ; 
    NOP                /T3 ! 1 00011 00010111 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0016][C:17]   ; 
    NOP                /T3 ! 1 01011 00000001 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0017][D:01]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0018]         ; 

; Log: read back
    NOP                /T3 ! 1 00011 00010111 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0019][C:17]   ; (Read 1 byte from GR00.17 and chek)
    NOP                /T3 ! 1 01101 XXXXXXXX XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0020]         ; 
    NOP                /T3 ! 1 01101 LLLLLLLH XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0021][R:01]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0022]         ; 
    IDXI         1999  /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0023]         ; 

; Log: finish

; Log: This configue to change TP_boot_state to DDI_PRGM
    NOP                /T3 ! 1 00011 11111101 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0024][C:FD]   ; 
    NOP                /T3 ! 1 01011 01011010 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0025][D:5A]  0; 
    NOP                /T3 ! 1 01011 01011010 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0026][D:5A]  1; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0027]         ; 
    NOP                /T3 ! 1 00011 10011111 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0028][C:9F]   ; 
    NOP                /T3 ! 1 01011 00000010 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0029][D:02]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0030]         ; 
    NOP                /T3 ! 1 00011 10011011 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0031][C:9B]   ; 
    NOP                /T3 ! 1 01011 00010001 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0032][D:11]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0033]         ; 
    NOP                /T3 ! 1 00011 10111000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0034][C:B8]   ; 
    NOP                /T3 ! 1 01011 01011010 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0035][D:5A]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0036]         ; 
    NOP                /T3 ! 1 00011 10011011 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0037][C:9B]   ; 
    NOP                /T3 ! 1 01011 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0038][D:00]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0039]         ; 
    NOP                /T3 ! 1 00011 00010111 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0040][C:17]   ; 
    NOP                /T3 ! 1 01011 00000001 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0041][D:01]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0042]         ; 

; Log: read back
    NOP                /T3 ! 1 00011 00010111 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0043][C:17]   ; (Read 1 byte from GR00.17 and chek)
    NOP                /T3 ! 1 01101 XXXXXXXX XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0044]         ; 
    NOP                /T3 ! 1 01101 LLLLLLLH XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0045][R:01]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0046]         ; 
    IDXI         1999  /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0047]         ; 

; Log: finish
    NOP                /T3 ! 1 00011 00011010 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0048][C:1A]   ; 
    NOP                /T3 ! 1 01011 00000001 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0049][D:01]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0050]         ; 
    NOP                /T3 ! 1 00011 00011011 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0051][C:1B]   ; 
    NOP                /T3 ! 1 01011 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0052][D:00]  0; 
    NOP                /T3 ! 1 01011 00000111 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0053][D:07]  1; 
    NOP                /T3 ! 1 01011 00110000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0054][D:30]  2; 
    NOP                /T3 ! 1 01011 00000101 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0055][D:05]  3; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0056]         ; 
    NOP                /T3 ! 1 00011 00011101 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0057][C:1D]   ; 
    NOP                /T3 ! 1 01011 00000110 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0058][D:06]  0; 
    NOP                /T3 ! 1 01011 00000011 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0059][D:03]  1; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0060]         ; 
    NOP                /T3 ! 1 00011 00011100 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0061][C:1C]   ; 
    NOP                /T3 ! 1 01011 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0062][D:00]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0063]         ; 
    NOP                /T3 ! 1 00011 00011011 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0064][C:1B]   ; 
    NOP                /T3 ! 1 01011 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0065][D:00]  0; 
    NOP                /T3 ! 1 01011 00000111 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0066][D:07]  1; 
    NOP                /T3 ! 1 01011 00110000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0067][D:30]  2; 
    NOP                /T3 ! 1 01011 00000111 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0068][D:07]  3; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0069]         ; 
    NOP                /T3 ! 1 00011 00011101 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0070][C:1D]   ; 
    NOP                /T3 ! 1 01011 01100111 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0071][D:67]  0; 
    NOP                /T3 ! 1 01011 11000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0072][D:C0]  1; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0073]         ; 
    NOP                /T3 ! 1 00011 00011100 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0074][C:1C]   ; 
    NOP                /T3 ! 1 01011 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0075][D:00]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0076]         ; 
    IDXI           19  /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0077]         ; 
    NOP                /T3 ! 1 00011 00011101 XXXXXXHH XX 1 10 10 111 0 0001 XXXXXX; [S0078][C:1D]   ; 
    NOP                /T3 ! 1 01011 01100111 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0079][D:67]  0; 
    NOP                /T3 ! 1 01011 11000001 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0080][D:C1]  1; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0081]         ; 
    NOP                /T3 ! 1 00011 00011100 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0082][C:1C]   ; 
    NOP                /T3 ! 1 01011 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0083][D:00]  0; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0084]         ; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0085]         ; 
    IDXI           18  /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0086]         ; 
    NOP                /T3 ! 1 11111 00000000 XXXXXXHH XX 1 10 10 111 0 0001 XXXXXX; [S0087]         ; (Finish)
    STPS               /T3 ! 1 11111 00000000 XXXXXXXX XX 1 10 10 111 0 0001 XXXXXX; [S0088]         ; (Finish)
END
