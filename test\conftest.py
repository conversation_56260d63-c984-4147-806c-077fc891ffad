"""
pytest 配置文件
"""
import sys
import os
from pathlib import Path

# 添加 source 目錄到 Python 路徑
project_root = Path(__file__).parent.parent
source_dir = project_root / "source"
sys.path.insert(0, str(source_dir))

import pytest
from unittest.mock import Mock
from parameterManger import ParameterManager
from logPrinter import LogPrinter


@pytest.fixture
def mock_logger():
    """創建模擬的日誌器"""
    logger = Mock(spec=LogPrinter)
    logger.section.return_value.__enter__ = Mock(return_value=None)
    logger.section.return_value.__exit__ = Mock(return_value=None)
    logger.step = Mock()
    logger.log = Mock()
    return logger


@pytest.fixture
def sample_params():
    """創建測試用的參數"""
    class MockParams:
        def __init__(self):
            self.version = "0.01"
            self.proj = "TEST_PROJ"
            self.commands = "set_pin,get_pin,set_title"
            self.output_width = "100"
            self.author = "Test Author"
            self.debug = "1"
            self.info = "1"
            self.warn = "1"
            self.error = "1"
            self.note = "0"
    
    return MockParams()


@pytest.fixture
def temp_rgt_file(tmp_path):
    """創建臨時的 RGT 測試文件"""
    rgt_content = """// Test RGT file
FOLDER(test_folder)
#set_pin
delay 100
12345=ABCD//WO//Test write command
67890=XX12//RO//Test read command
"""
    rgt_file = tmp_path / "test.rgt"
    rgt_file.write_text(rgt_content, encoding="utf-8")
    return str(rgt_file)


@pytest.fixture
def temp_para_file(tmp_path):
    """創建臨時的參數文件"""
    para_content = """para=DBI_Transfer
version=0.01
author=Test Author
proj=TEST_PROJ
debug=1
info=1
note=0
warn=1
error=1
output_width=100
commands=set_pin, get_pin, set_title, set_regif, log, label
inFileName=
outFileName=
use_last_file=
source_file_format=
"""
    para_file = tmp_path / "para.txt"
    para_file.write_text(para_content, encoding="utf-8")
    return str(para_file)
