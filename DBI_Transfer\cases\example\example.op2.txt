// Author: zhili
// -----------------------------------------------------------------------------
// 配置这个pattern的名称为OTP_PGM1，在生成vector的时候会以这个名称为标准，默认为idle
#set_title OTP_PGM1

// -----------------------------------------------------------------------------
// 配置寄存器接口为DBI，目前只支持这一种配置
#set_regif dbi

// -----------------------------------------------------------------------------
// 添加一行log，log会在生成的vector中以注释的形式存在
#log This pattern is used for auto program trim data into otp_cell

// -----------------------------------------------------------------------------
// 引用一个op2脚本，不同的op2脚本之间可以多层嵌套以实现复用
#include pinbuild.op2.txt   // 用于建立各个pin的模型，默认必须导入
#include reset.op2.txt      // 硬复位 + DCS解锁

// -----------------------------------------------------------------------------
// 使用OP1语法配置寄存器及添加delay，delay单位是ms，支持小数
#log sleep-out
R11
delay 20

#log setup program env
R9F 0F
RB3 00 40
RB7 CC 80 D2
delay 0.1 // 100us

// -----------------------------------------------------------------------------
// 在配置寄存器的时候可以用小括号在指定字节处添加label
#log select program groups
RB5 00 20(LABEL_FOR_BYTE1) 00 00(LABEL_FOR_BYTE3) 00 00
RC1 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00

#log start program trim_otp/trim0/trim_osc
RB3 40
delay 20
RB3 00

// -----------------------------------------------------------------------------
// 也可以直接使用label命令添加label
#label PROGRAM_DONE
delay 0.1

#log relaod programed data
RB3 20
delay 1
RB3 00

// -----------------------------------------------------------------------------
// 使用类似于OP1的语法回读寄存器，顺序指定地址、回读字节数、对应数量的golden、注释
// 与写寄存器类似，也可以使用小括号添加字节对应label
R9F 0F
RC7 #3 xx xx 10(R_REG_PGM_CNT) Read&Check PGM_CNT reload from otp_cell

// -----------------------------------------------------------------------------
// 可以使用set_pin命令直接指定一个pin上的值，取值范围是0/1/Z
// 需要注意在生成vector的时候如果后续没有delay，set_pin是不会生效的
// 可以多个不同的pin一起配置，然后使用一个delay使其一并生效
#set_pin nRESET 0

// -----------------------------------------------------------------------------
// 在生成vector时，可以使用使用如下形式在delay时通过EXT_OSC外灌时钟T4
#set_pin EXT_OSC 0
delay 1
#set_pin EXT_OSC 1

// -----------------------------------------------------------------------------
// 可以使用get_pin命令采样一个pin上的值并与golden比对，golden取值范围是0/1/X
// 同样需要注意在生成vector的时候如果后续没有delay，get_pin是不会生效的，同时，只有
//     delay的第一个cycle会进行采样，后续cycle只是普通delay
// 可以多个不同的pin一起配置，然后使用一个delay使其一并生效
#get_pin GPO0 0
#get_pin GPO1 0
#get_pin GPO2 x
#get_pin GPO3 0
delay 0.001  // based on T3=1MHz

#get_pin GPO3 1
delay 2

#log finish