"""
配置管理器
"""
from typing import Any


class ConfigManager:
    """配置管理器類別"""
    
    @staticmethod
    def parse_user_input() -> str:
        """解析使用者輸入"""
        return input("Enter test item: ")
    
    @staticmethod
    def copy_result_files(src_folder: str, dest_folder: str, base_name: str, logger: Any) -> None:
        """複製結果檔案"""
        from utils.file_utils import FileUtils
        FileUtils.copy_and_rename_files(src_folder, dest_folder, base_name, logger=logger)
