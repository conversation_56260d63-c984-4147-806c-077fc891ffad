import unittest
import os
import shutil
from typing import Optional
from logPrinter import LogPrinter

class SimpleParameterManager():
    def __init__(self, info, note, warn, error, debug):
        self.debug =debug
        self.info = info
        self.note = note
        self.warn = warn
        self.error = error

class TestFileUtils(unittest.TestCase):
    def setUp(self):
        os.makedirs("test_src", exist_ok=True)
        with open("test_src/file1.txt", "w") as f:
            f.write('Hello')
        with open("test_src/file2.txt", "w") as f:
            f.write("World")
        params = SimpleParameterManager(1, 1, 1, 1, 1)
        self.logger = LogPrinter(params, "test.log")
    
    def tearDown(self):
        return super().tearDown()