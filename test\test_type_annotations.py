"""
測試類型註解
"""
import pytest
import sys
import os
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加 source 目錄到路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'source'))

from type_definitions import (
    FilePath, LineNumber, ErrorCode, LogLevel,
    HexData, Address, Command, Comment,
    ParsedLine, RawContent, ValidContent,
    ParameterDict, ErrorContext, ErrorRecord, ErrorSummary,
    TitleInfo, VersionInfo, CompatibilityInfo
)


class TestTypeDefinitions:
    """測試類型定義"""
    
    def test_basic_type_aliases(self):
        """測試基本類型別名"""
        # FilePath
        file_path: FilePath = "test.rgt"
        assert isinstance(file_path, str)
        
        file_path_pathlib: FilePath = Path("test.rgt")
        assert isinstance(file_path_pathlib, Path)
        
        # LineNumber
        line_num: LineNumber = 10
        assert isinstance(line_num, int)
        
        # ErrorCode
        error_code: ErrorCode = "ERR001"
        assert isinstance(error_code, str)
        
        # LogLevel
        log_level: LogLevel = "INFO"
        assert log_level in ["DEBUG", "INFO", "WARN", "ERROR", "STEP"]
    
    def test_data_types(self):
        """測試資料相關類型"""
        # HexData
        hex_data: HexData = "ABCD"
        assert isinstance(hex_data, str)
        
        # Address
        address: Address = "012345"
        assert isinstance(address, str)
        
        # Command
        command: Command = "WO"
        assert isinstance(command, str)
        
        # Comment
        comment: Comment = "Test comment"
        assert isinstance(comment, str)
    
    def test_parsing_types(self):
        """測試解析相關類型"""
        # ParsedLine
        parsed_line: ParsedLine = ["WO", "012345", "ABCD", "comment", "full_line"]
        assert isinstance(parsed_line, list)
        assert len(parsed_line) == 5
        
        # RawContent
        raw_content: RawContent = ["line1", "line2", "line3"]
        assert isinstance(raw_content, list)
        assert all(isinstance(line, str) for line in raw_content)
        
        # ValidContent
        valid_content: ValidContent = [
            ["WO", "012345", "ABCD", "comment", "full_line"],
            ["RO", "067890", "XX12", "read", "read_line"]
        ]
        assert isinstance(valid_content, list)
        assert all(isinstance(line, list) for line in valid_content)
    
    def test_typed_dict_structures(self):
        """測試 TypedDict 結構"""
        # ParameterDict
        params: ParameterDict = {
            'version': '0.01',
            'proj': 'TEST',
            'commands': 'set_pin,get_pin',
            'output_width': '100',
            'author': 'Test Author',
            'debug': '1',
            'info': '1',
            'warn': '1',
            'error': '1',
            'note': '0'
        }
        assert params['version'] == '0.01'
        assert params['proj'] == 'TEST'
        
        # ErrorContext
        error_context: ErrorContext = {
            'line_number': 10,
            'line_content': 'test line',
            'file_path': 'test.rgt'
        }
        assert error_context['line_number'] == 10
        
        # TitleInfo
        title_info: TitleInfo = {
            'Tool_version': '0.01',
            'Proj': 'TEST',
            'Test_Item': 'test_item',
            'Author': 'Test Author',
            'Date': '2023/12/25'
        }
        assert title_info['Tool_version'] == '0.01'


class TestTypeCompatibility:
    """測試類型兼容性"""
    
    def test_optional_types(self):
        """測試可選類型"""
        def process_optional_string(s: Optional[str]) -> str:
            return s if s is not None else "default"
        
        assert process_optional_string("test") == "test"
        assert process_optional_string(None) == "default"
    
    def test_union_types(self):
        """測試聯合類型"""
        from typing import Union
        
        def process_path(path: Union[str, Path]) -> str:
            if isinstance(path, Path):
                return str(path)
            return path
        
        assert process_path("test.txt") == "test.txt"
        assert process_path(Path("test.txt")) == "test.txt"
    
    def test_generic_types(self):
        """測試泛型類型"""
        from typing import List, Dict
        
        def process_lines(lines: List[str]) -> Dict[int, str]:
            return {i: line for i, line in enumerate(lines)}
        
        result = process_lines(["line1", "line2"])
        assert result == {0: "line1", 1: "line2"}


class TestProtocolCompliance:
    """測試協議遵循"""
    
    def test_loggable_protocol(self):
        """測試 Loggable 協議"""
        from type_definitions import Loggable
        from unittest.mock import Mock
        
        # 創建符合協議的模擬對象
        mock_logger = Mock(spec=Loggable)
        mock_logger.log = Mock()
        mock_logger.step = Mock()
        
        # 測試協議方法
        mock_logger.log("test message", level="INFO")
        mock_logger.step("test step")
        
        mock_logger.log.assert_called_with("test message", level="INFO")
        mock_logger.step.assert_called_with("test step")
    
    def test_parsable_protocol(self):
        """測試 Parsable 協議"""
        from type_definitions import Parsable
        from unittest.mock import Mock
        
        mock_parser = Mock(spec=Parsable)
        mock_parser.parse = Mock(return_value=["parsed", "data"])
        mock_parser.is_valid = Mock(return_value=True)
        
        result = mock_parser.parse("test content")
        is_valid = mock_parser.is_valid("test content")
        
        assert result == ["parsed", "data"]
        assert is_valid is True


@pytest.mark.skipif(
    subprocess.run(["python", "-c", "import mypy"], capture_output=True).returncode != 0,
    reason="mypy not installed"
)
class TestMypyIntegration:
    """測試 mypy 整合"""
    
    def test_mypy_check_types_module(self):
        """測試 types.py 模組的 mypy 檢查"""
        result = subprocess.run([
            "python", "-m", "mypy", 
            "source/types.py",
            "--ignore-missing-imports"
        ], capture_output=True, text=True)
        
        # mypy 應該沒有錯誤
        assert result.returncode == 0, f"mypy errors: {result.stdout}"
    
    def test_mypy_check_core_engine(self):
        """測試核心引擎的 mypy 檢查"""
        result = subprocess.run([
            "python", "-m", "mypy", 
            "source/core/engine.py",
            "--ignore-missing-imports"
        ], capture_output=True, text=True)
        
        # 允許一些警告，但不應該有錯誤
        if result.returncode != 0:
            # 檢查是否只是警告
            output_lines = result.stdout.split('\n')
            error_lines = [line for line in output_lines if 'error:' in line.lower()]
            assert len(error_lines) == 0, f"mypy errors: {result.stdout}"


class TestTypeHints:
    """測試類型提示的實際使用"""
    
    def test_function_annotations(self):
        """測試函數註解"""
        def annotated_function(
            data: HexData, 
            line_num: LineNumber, 
            comment: Optional[Comment] = None
        ) -> ParsedLine:
            return [data, str(line_num), comment or "", "", ""]
        
        result = annotated_function("ABCD", 10, "test")
        assert result == ["ABCD", "10", "test", "", ""]
        
        result = annotated_function("EFGH", 20)
        assert result == ["EFGH", "20", "", "", ""]
    
    def test_class_annotations(self):
        """測試類別註解"""
        class AnnotatedClass:
            def __init__(self, logger: Optional[Any] = None) -> None:
                self.logger: Optional[Any] = logger
                self.error_count: int = 0
                self.warnings: List[str] = []
            
            def add_warning(self, message: str) -> None:
                self.warnings.append(message)
            
            def get_summary(self) -> Dict[str, Any]:
                return {
                    'error_count': self.error_count,
                    'warning_count': len(self.warnings),
                    'warnings': self.warnings
                }
        
        obj = AnnotatedClass()
        obj.add_warning("test warning")
        summary = obj.get_summary()
        
        assert summary['warning_count'] == 1
        assert summary['warnings'] == ["test warning"]


class TestTypeValidation:
    """測試類型驗證"""
    
    def test_runtime_type_checking(self):
        """測試運行時類型檢查"""
        def validate_hex_data(data: HexData) -> bool:
            """驗證十六進制資料格式"""
            if not isinstance(data, str):
                return False
            if len(data) % 2 != 0:
                return False
            try:
                int(data, 16)
                return True
            except ValueError:
                return False
        
        assert validate_hex_data("ABCD") is True
        assert validate_hex_data("1234") is True
        assert validate_hex_data("GHIJ") is False
        assert validate_hex_data("ABC") is False
    
    def test_type_narrowing(self):
        """測試類型縮窄"""
        def process_optional_data(data: Optional[HexData]) -> str:
            if data is None:
                return "No data"
            
            # 這裡 mypy 知道 data 不是 None
            return f"Data: {data.upper()}"
        
        assert process_optional_data("abcd") == "Data: ABCD"
        assert process_optional_data(None) == "No data"
