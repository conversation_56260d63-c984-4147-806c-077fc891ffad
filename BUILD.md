# DBI Transfer Tool 構建指南

本文檔說明如何構建 DBI Transfer Tool 的可執行文件。

## 📋 目錄

- [系統要求](#系統要求)
- [快速開始](#快速開始)
- [構建腳本說明](#構建腳本說明)
- [構建配置](#構建配置)
- [故障排除](#故障排除)
- [高級用法](#高級用法)

## 🔧 系統要求

### 基本要求
- **Python**: 3.8 或更高版本
- **PyInstaller**: 自動安裝（如果未安裝）
- **磁盤空間**: 至少 500MB 可用空間

### 平台支持
- ✅ Windows 10/11
- ✅ Linux (Ubuntu 18.04+, CentOS 7+)
- ✅ macOS 10.14+

## 🚀 快速開始

### Windows 用戶

1. **使用批處理腳本（推薦）**：
   ```cmd
   # 雙擊運行或在命令提示符中執行
   build_exe.bat
   ```

2. **使用 Python 腳本**：
   ```cmd
   python build.py
   ```

### Linux/macOS 用戶

1. **使用 Shell 腳本**：
   ```bash
   chmod +x build.sh
   ./build.sh
   ```

2. **使用 Python 腳本**：
   ```bash
   python3 build.py
   ```

## 📁 構建腳本說明

### 文件結構
```
dbi_transfer_tool/
├── build_exe.bat          # Windows 批處理腳本
├── build.sh               # Linux/macOS Shell 腳本
├── build.py               # 跨平台 Python 腳本
├── build_config.py        # 構建配置文件
└── BUILD.md               # 本說明文件
```

### 腳本功能對比

| 功能 | build_exe.bat | build.sh | build.py |
|------|---------------|----------|----------|
| Windows 支持 | ✅ | ❌ | ✅ |
| Linux 支持 | ❌ | ✅ | ✅ |
| macOS 支持 | ❌ | ✅ | ✅ |
| 自動安裝依賴 | ✅ | ✅ | ✅ |
| 環境驗證 | ✅ | ✅ | ✅ |
| 構建測試 | ✅ | ✅ | ✅ |
| 彩色輸出 | ✅ | ✅ | ✅ |

## ⚙️ 構建配置

構建配置在 `build_config.py` 中定義，您可以根據需要修改：

### 基本配置
```python
BUILD_CONFIG = {
    "script_name": "source/application.py",    # 主程式入口
    "dist_name": "DBI_Transfer",               # 輸出檔名
    "dist_folder": "DBI_Transfer",             # 輸出目錄
    "onefile": True,                           # 單一可執行檔
    "console": True,                           # 控制台應用程式
}
```

### 包含的文件
```python
"add_data": [
    ("source/config/templates", "config/templates"),  # 配置模板
    ("source/cases", "cases"),                        # 測試案例
    ("source/op2.exe", "."),                          # OP2 工具
    ("source/para.txt", "."),                         # 配置文件
]
```

### 隱藏導入
```python
"hidden_imports": [
    "config", "core", "parsers", "output", 
    "utils", "validation", ...
]
```

## 🔍 構建過程

構建過程包含以下步驟：

1. **[1/7] 檢查 Python 環境** - 驗證 Python 版本
2. **[2/7] 檢查依賴項** - 安裝 PyInstaller（如需要）
3. **[3/7] 驗證構建環境** - 檢查源文件和配置
4. **[4/7] 清理舊文件** - 刪除之前的構建文件
5. **[5/7] 執行 PyInstaller** - 打包可執行文件
6. **[6/7] 驗證構建結果** - 測試生成的可執行文件
7. **[7/7] 顯示構建摘要** - 顯示結果和使用說明

## 📦 輸出結果

構建成功後，您將得到：

```
DBI_Transfer/
├── DBI_Transfer.exe        # 主可執行文件 (Windows)
├── DBI_Transfer            # 主可執行文件 (Linux/macOS)
├── cases/                  # 測試案例目錄
├── op2.exe                 # OP2 工具
└── para.txt               # 配置文件
```

## 🛠️ 故障排除

### 常見問題

#### 1. Python 版本過低
```
❌ 需要 Python 3.8 或更高版本
```
**解決方案**: 升級 Python 到 3.8+

#### 2. PyInstaller 安裝失敗
```
❌ PyInstaller 安裝失敗
```
**解決方案**: 
```bash
pip install --upgrade pip
pip install pyinstaller
```

#### 3. 源文件不存在
```
❌ 主腳本文件不存在: source/application.py
```
**解決方案**: 確保在正確的項目根目錄執行構建腳本

#### 4. 權限問題 (Linux/macOS)
```
Permission denied: ./build.sh
```
**解決方案**: 
```bash
chmod +x build.sh
```

#### 5. 構建文件被佔用 (Windows)
```
❌ 無法刪除構建文件
```
**解決方案**: 關閉所有相關程序，或重啟後重試

### 調試模式

如需詳細的調試信息，可以修改 `build_config.py`：

```python
BUILD_CONFIG = {
    ...
    "debug": True,  # 啟用調試模式
    ...
}
```

## 🔧 高級用法

### 自定義構建

1. **修改輸出名稱**：
   ```python
   BUILD_CONFIG["dist_name"] = "MyCustomName"
   ```

2. **添加圖標** (Windows)：
   ```python
   # 在 get_pyinstaller_args() 中添加
   args.extend(["--icon", "path/to/icon.ico"])
   ```

3. **創建 Windows 服務**：
   ```python
   BUILD_CONFIG["console"] = False  # 無控制台窗口
   ```

### 批量構建

創建 `build_all.py` 用於多平台構建：

```python
import subprocess
import sys

platforms = ["windows", "linux", "macos"]
for platform in platforms:
    print(f"Building for {platform}...")
    # 根據平台調用相應的構建腳本
```

### CI/CD 集成

在 GitHub Actions 中使用：

```yaml
- name: Build executable
  run: python build.py
  
- name: Upload artifact
  uses: actions/upload-artifact@v3
  with:
    name: DBI_Transfer
    path: DBI_Transfer/
```

## 📞 支持

如果遇到問題：

1. 檢查 [故障排除](#故障排除) 部分
2. 查看構建日誌中的錯誤信息
3. 確保所有依賴項都已正確安裝
4. 在項目 Issues 中報告問題

## 📝 更新日誌

### v2.0 (當前版本)
- ✅ 支持跨平台構建
- ✅ 自動依賴檢查和安裝
- ✅ 改進的錯誤處理
- ✅ 彩色輸出和進度顯示
- ✅ 構建結果驗證

### v1.0
- ✅ 基本的 Windows 構建支持
- ✅ PyInstaller 集成
