# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['source\\application.py'],
    pathex=[],
    binaries=[],
    datas=[('source/config/templates', 'config/templates'), ('source/cases', 'cases'), ('source/op2.exe', '.'), ('source/para.txt', '.')],
    hiddenimports=['config', 'core', 'parsers', 'output', 'utils', 'validation', 'logPrinter', 'parameterManger', 'exceptions', 'constant', 'type_definitions'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='DBI_Transfer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
