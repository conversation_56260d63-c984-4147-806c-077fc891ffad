"""
測試輸出管理器
"""
import pytest
from unittest.mock import Mock, patch, mock_open
from io import StringIO
import tempfile
import os
from pathlib import Path

from output.output_manager import OutputManager
from core.engine import DbiTransferEngine


class TestOutputManager:
    """測試輸出管理器"""
    
    def test_init(self, mock_logger):
        """測試初始化"""
        manager = OutputManager(mock_logger)
        assert manager.logger == mock_logger
    
    def test_get_front_text_basic(self, mock_logger):
        """測試基本前置文字生成"""
        manager = OutputManager(mock_logger)
        texts = {
            "Version": "1.0",
            "Author": "Test"
        }
        
        result = manager.get_front_text(texts, 50)
        
        assert "Version" in result
        assert "Author" in result
        assert "1.0" in result
        assert "Test" in result
        assert result.startswith("//")
        assert result.endswith("//\n")
    
    def test_get_front_text_custom_delimiter(self, mock_logger):
        """測試自定義分隔符"""
        manager = OutputManager(mock_logger)
        texts = {"Key": "Value"}
        
        result = manager.get_front_text(texts, 30, "##")
        
        assert result.startswith("##")
        assert "##" in result
    
    def test_build_title(self, mock_logger, sample_params):
        """測試建立標題"""
        manager = OutputManager(mock_logger)
        
        with patch('datetime.datetime') as mock_datetime:
            mock_datetime.now.return_value.strftime.return_value = "2023/12/25 10:30:00"
            
            result = manager.build_title(sample_params, "test_item")
            
            assert result["Tool version"] == "0.01"
            assert result["Proj"] == "TEST_PROJ"
            assert result["Test Item"] == "test_item"
            assert result["Author"] == "Test Author"
            assert result["Date"] == "2023/12/25 10:30:00"
    
    def test_get_output_folder(self, mock_logger, tmp_path):
        """測試獲取輸出資料夾"""
        manager = OutputManager(mock_logger)
        
        with patch('pathlib.Path') as mock_path:
            mock_path.return_value = tmp_path / "cases" / "TEST_PROJ" / "test_folder"
            
            with patch('os.makedirs') as mock_makedirs:
                result = manager.get_output_folder("TEST_PROJ", "test_folder")
                
                mock_makedirs.assert_called_once()
    
    def test_prepare_filenames(self, mock_logger):
        """測試準備檔案名稱"""
        manager = OutputManager(mock_logger)
        
        in_file, out_file, c_file = manager.prepare_filenames("TEST_PROJ", "test_item")
        
        assert in_file == "TEST_PROJ_test_item.rgt"
        assert out_file == "TEST_PROJ_test_item.txt"
        assert c_file == "TEST_PROJ_test_item.c"
    
    def test_write_title_section(self, mock_logger):
        """測試寫入標題區段"""
        manager = OutputManager(mock_logger)
        output = StringIO()
        
        title = {"Version": "1.0", "Author": "Test"}
        rgt_content = ["line1", "line2"]
        
        manager.write_title_section(output, title, 80, "test.rgt", rgt_content)
        
        result = output.getvalue()
        assert "Version" in result
        assert "Author" in result
        assert "test.rgt start" in result
        assert "test.rgt end" in result
        assert "line1" in result
        assert "line2" in result
        assert "#include open_ddi_p_mode.op2.txt" in result
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('pathlib.Path.mkdir')
    def test_write_output_files(self, mock_mkdir, mock_file, mock_logger):
        """測試寫入輸出檔案"""
        manager = OutputManager(mock_logger)
        engine = Mock(spec=DbiTransferEngine)
        
        output_folder = Path("/tmp/test")
        title = {"Version": "1.0"}
        decoded_content = []
        rgt_content = ["test line"]
        
        manager.write_output_files(
            output_folder, "test.txt", "test.c", title, 
            decoded_content, 80, rgt_content, "test.rgt", engine
        )
        
        # 驗證檔案被開啟
        assert mock_file.call_count == 2  # 兩個檔案
        
        # 驗證引擎方法被調用
        engine.reset_state.assert_called_once()
        engine.process_decoded_content.assert_called_once()


class TestOutputManagerIntegration:
    """測試輸出管理器整合"""
    
    def test_complete_workflow(self, mock_logger, tmp_path):
        """測試完整的輸出工作流程"""
        manager = OutputManager(mock_logger)
        
        # 準備測試資料
        title = {
            "Tool version": "0.01",
            "Proj": "TEST",
            "Test Item": "integration_test",
            "Author": "Test Author",
            "Date": "2023/12/25 10:30:00"
        }
        
        decoded_content = [
            ["comment", None, None, None, "// Test comment"],
            ["delay", None, None, None, "delay 100"]
        ]
        
        rgt_content = ["// Original RGT content", "delay 100"]
        
        # 創建臨時檔案
        output_file = tmp_path / "test_output.txt"
        c_file = tmp_path / "test_output.c"
        
        # 模擬引擎
        engine = Mock(spec=DbiTransferEngine)
        
        # 執行寫入
        with patch('builtins.open', mock_open()) as mock_file:
            manager.write_output_files(
                tmp_path, "test_output.txt", "test_output.c",
                title, decoded_content, 100, rgt_content, 
                "test.rgt", engine
            )
            
            # 驗證檔案操作
            assert mock_file.call_count == 2
            engine.reset_state.assert_called_once()
            engine.process_decoded_content.assert_called_once()
