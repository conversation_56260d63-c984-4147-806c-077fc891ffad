{"data_mtime": 1750670846, "dep_lines": [4, 6, 10, 11, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["__future__", "typing", "pathlib", "sys", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "contextlib", "os", "types"], "hash": "09a6ed97dc52393a8eb181a807f48e605a7a1d4e", "id": "type_definitions", "ignore_all": false, "interface_hash": "9af56f766e86dd891889bf0cf74e2c1c8de6154e", "mtime": 1750670827, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\type_definitions.py", "plugin_data": null, "size": 6843, "suppressed": [], "version_id": "1.16.1"}