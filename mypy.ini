[mypy]
# 基本配置
python_version = 3.9
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True

# 嚴格模式
strict_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True

# 錯誤報告
show_error_codes = True
show_column_numbers = True
pretty = True
color_output = True

# 導入處理
ignore_missing_imports = True
follow_imports = normal

# 特定模組配置
[mypy-tests.*]
disallow_untyped_defs = False
disallow_incomplete_defs = False

[mypy-conftest]
disallow_untyped_defs = False

# 第三方庫忽略
[mypy-pytest.*]
ignore_missing_imports = True

[mypy-unittest.*]
ignore_missing_imports = True
