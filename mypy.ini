[mypy]
# Basic configuration
python_version = 3.9
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True

# Strict mode
strict_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True

# Error reporting
show_error_codes = True
show_column_numbers = True
pretty = True
color_output = True

# Import handling
ignore_missing_imports = True
follow_imports = normal

# Module-specific configuration
[mypy-tests.*]
disallow_untyped_defs = False
disallow_incomplete_defs = False

[mypy-conftest]
disallow_untyped_defs = False

# Third-party library ignores
[mypy-pytest.*]
ignore_missing_imports = True

[mypy-unittest.*]
ignore_missing_imports = True
