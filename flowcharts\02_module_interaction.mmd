graph TB
    subgraph "主程序層"
        APP[Application.py<br/>主應用程序]
        MAIN[main函數<br/>命令行入口]
    end
    
    subgraph "配置管理層"
        PM[ParameterManager<br/>參數管理器]
        CM[ConfigManager<br/>配置管理器]
        CV[ConfigValidator<br/>配置驗證器]
        CS[ConfigSchema<br/>配置架構]
    end
    
    subgraph "核心處理層"
        WM[WorkflowManager<br/>工作流管理器]
        CE[CoreEngine<br/>核心引擎]
        RP[RgtParser<br/>RGT解析器]
    end
    
    subgraph "輸出管理層"
        OM[OutputManager<br/>輸出管理器]
        LP[LogPrinter<br/>日誌打印器]
        LF[LoggerFactory<br/>日誌工廠]
    end
    
    subgraph "驗證處理層"
        EH[ErrorHandler<br/>錯誤處理器]
        VV[VersionValidator<br/>版本驗證器]
    end
    
    subgraph "工具層"
        MSG[Messages<br/>多語言消息]
        ENC[EncodingFix<br/>編碼修復]
        FU[FileUtils<br/>文件工具]
    end
    
    subgraph "外部工具"
        OP2[OP2.exe<br/>外部處理工具]
        RGT[RGT文件<br/>輸入文件]
        OUT[輸出文件<br/>results目錄]
    end
    
    %% 主要流程連接
    MAIN --> APP
    APP --> PM
    APP --> WM
    APP --> EH
    
    %% 配置管理流程
    PM --> CM
    CM --> CV
    CV --> CS
    
    %% 核心處理流程
    WM --> CE
    CE --> RP
    RP --> RGT
    
    %% 輸出管理流程
    APP --> LF
    LF --> LP
    CE --> OM
    OM --> OUT
    CE --> OP2
    
    %% 驗證處理流程
    EH --> VV
    RP --> EH
    CE --> EH
    
    %% 工具層連接
    APP --> MSG
    APP --> ENC
    OM --> FU
    RP --> FU
    
    %% 數據流向
    RGT -.-> RP
    RP -.-> CE
    CE -.-> OP2
    OP2 -.-> OUT
    
    %% 錯誤流向
    PM -.-> EH
    RP -.-> EH
    CE -.-> EH
    
    %% 日誌流向
    APP -.-> LP
    WM -.-> LP
    EH -.-> LP
    
    %% 樣式定義
    classDef mainLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef configLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef coreLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef outputLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef validationLayer fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef utilLayer fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef externalLayer fill:#fafafa,stroke:#616161,stroke-width:2px
    
    class APP,MAIN mainLayer
    class PM,CM,CV,CS configLayer
    class WM,CE,RP coreLayer
    class OM,LP,LF outputLayer
    class EH,VV validationLayer
    class MSG,ENC,FU utilLayer
    class OP2,RGT,OUT externalLayer
