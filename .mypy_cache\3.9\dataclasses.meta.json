{"data_mtime": 1750670042, "dep_lines": [6, 1, 2, 3, 4, 5, 7, 8, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "enum", "sys", "types", "_typeshed", "builtins", "typing", "typing_extensions", "_frozen_importlib", "abc"], "hash": "5128266de504407a0f4aa2372f2cdd8bb6f0a1fe", "id": "dataclasses", "ignore_all": true, "interface_hash": "d391487c02d07d0cb36972e4899f9fc15e8721d5", "mtime": 1750572233, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\dataclasses.pyi", "plugin_data": null, "size": 10450, "suppressed": [], "version_id": "1.16.1"}