"""
測試工作流管理器
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 添加源碼路徑
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'source'))

from core.workflow import WorkflowManager
from logPrinter import LogPrinter
from parameterManger import ParameterManager


class TestWorkflowManager:
    """測試工作流管理器"""
    
    def setup_method(self):
        """設置測試環境"""
        self.logger = Mock(spec=LogPrinter)
        self.workflow = WorkflowManager(self.logger)
        
        # 模擬參數管理器
        self.params = Mock(spec=ParameterManager)
        self.params.commands = "set_pin,get_pin"
        self.params.proj = "TEST_PROJECT"
        self.params.output_width = "120"
    
    def test_init(self):
        """測試初始化"""
        logger = Mock(spec=LogPrinter)
        workflow = WorkflowManager(logger)
        
        assert workflow.logger == logger
        assert workflow.parser is not None
        assert workflow.output_manager is not None
        assert workflow.engine is not None
    
    @patch('core.workflow.create_version_validator')
    def test_validate_configuration_success(self, mock_create_validator):
        """測試配置驗證成功"""
        # 模擬驗證器
        mock_validator = Mock()
        mock_validator.validate_compatibility.return_value = (True, "配置兼容")
        mock_create_validator.return_value = mock_validator
        
        result = self.workflow.validate_configuration(self.params)
        
        assert result is True
        self.logger.log.assert_called()
        mock_validator.validate_compatibility.assert_called_once_with(self.params)
    
    @patch('core.workflow.create_version_validator')
    @patch('builtins.input', return_value='')  # 模擬用戶輸入
    def test_validate_configuration_failure(self, mock_input, mock_create_validator):
        """測試配置驗證失敗"""
        # 模擬驗證器
        mock_validator = Mock()
        mock_validator.validate_compatibility.return_value = (False, "配置不兼容")
        mock_validator.get_compatibility_info.return_value = {
            'exe_version': '1.0',
            'para_version': '0.9',
            'supported_versions': ['1.0', '1.1'],
            'details': {
                'version_check': {'passed': False, 'message': '版本不匹配'}
            }
        }
        mock_create_validator.return_value = mock_validator
        
        result = self.workflow.validate_configuration(self.params)
        
        assert result is False
        self.logger.log.assert_called()
        mock_validator.validate_compatibility.assert_called_once_with(self.params)
        mock_validator.get_compatibility_info.assert_called_once_with(self.params)
    
    def test_process_rgt_file(self):
        """測試處理 RGT 文件"""
        # 模擬解析器
        mock_parser_result = (["rgt_line1", "rgt_line2"], ["decoded1", "decoded2"], "test_cases")
        self.workflow.parser.parse_file = Mock(return_value=mock_parser_result)
        
        result = self.workflow.process_rgt_file(self.params, "test.rgt")
        
        assert result == mock_parser_result
        self.workflow.parser.parse_file.assert_called_once()
    
    def test_generate_output(self):
        """測試生成輸出"""
        # 模擬輸出管理器
        self.workflow.output_manager.prepare_filenames = Mock(return_value=("in.txt", "out.txt", "test.c"))
        self.workflow.output_manager.get_output_folder = Mock(return_value="/output/folder")
        self.workflow.output_manager.build_title = Mock(return_value="Test Title")
        self.workflow.output_manager.write_output_files = Mock()
        
        rgt_lines = ["line1", "line2"]
        decoded_lines = ["decoded1", "decoded2"]
        
        result = self.workflow.generate_output(
            self.params, "test_item", rgt_lines, decoded_lines, "test_cases"
        )
        
        assert result == ("/output/folder", "out.txt")
        self.workflow.output_manager.prepare_filenames.assert_called_once()
        self.workflow.output_manager.get_output_folder.assert_called_once()
        self.workflow.output_manager.build_title.assert_called_once()
        self.workflow.output_manager.write_output_files.assert_called_once()
    
    @patch('core.workflow.FileUtils')
    @patch('core.workflow.ConfigManager')
    def test_run_batch_and_copy(self, mock_config_manager, mock_file_utils):
        """測試執行批次處理並複製文件"""
        # 模擬日誌器的 section 上下文管理器
        self.logger.section = Mock()
        self.logger.section.return_value.__enter__ = Mock()
        self.logger.section.return_value.__exit__ = Mock()
        
        self.workflow.run_batch_and_copy(self.params, "test_cases", "out.txt", "test_item")
        
        # 檢查是否調用了相關方法
        mock_file_utils.update_batch_file.assert_called_once()
        mock_file_utils.execute_batch_file.assert_called_once()
        mock_config_manager.copy_result_files.assert_called_once()
        self.logger.section.assert_called_once_with('Run batch and copy')
    
    @patch('core.workflow.create_version_validator')
    def test_execute_workflow_validation_failure(self, mock_create_validator):
        """測試執行工作流程時驗證失敗"""
        # 模擬驗證失敗
        mock_validator = Mock()
        mock_validator.validate_compatibility.return_value = (False, "配置不兼容")
        mock_validator.get_compatibility_info.return_value = {
            'exe_version': '1.0',
            'para_version': '0.9',
            'supported_versions': ['1.0'],
            'details': {}
        }
        mock_create_validator.return_value = mock_validator
        
        with patch('builtins.input', return_value=''):
            self.workflow.execute_workflow(self.params, "test_item")
        
        # 驗證失敗後應該直接返回，不執行後續步驟
        mock_validator.validate_compatibility.assert_called_once()
    
    @patch('core.workflow.create_version_validator')
    @patch('core.workflow.FileUtils')
    @patch('core.workflow.ConfigManager')
    def test_execute_workflow_success(self, mock_config_manager, mock_file_utils, mock_create_validator):
        """測試執行工作流程成功"""
        # 模擬驗證成功
        mock_validator = Mock()
        mock_validator.validate_compatibility.return_value = (True, "配置兼容")
        mock_create_validator.return_value = mock_validator
        
        # 模擬各個組件
        self.workflow.output_manager.prepare_filenames = Mock(return_value=("in.txt", "out.txt", "test.c"))
        self.workflow.parser.parse_file = Mock(return_value=(["rgt1"], ["decoded1"], "cases"))
        self.workflow.output_manager.get_output_folder = Mock(return_value="/output")
        self.workflow.output_manager.build_title = Mock(return_value="Title")
        self.workflow.output_manager.write_output_files = Mock()
        
        # 模擬日誌器的 section 上下文管理器
        self.logger.section = Mock()
        self.logger.section.return_value.__enter__ = Mock()
        self.logger.section.return_value.__exit__ = Mock()
        
        self.workflow.execute_workflow(self.params, "test_item")
        
        # 檢查是否執行了所有步驟
        mock_validator.validate_compatibility.assert_called_once()
        self.workflow.output_manager.prepare_filenames.assert_called()
        self.workflow.parser.parse_file.assert_called_once()
        self.workflow.output_manager.write_output_files.assert_called_once()
        mock_file_utils.update_batch_file.assert_called_once()
        mock_file_utils.execute_batch_file.assert_called_once()
        mock_config_manager.copy_result_files.assert_called_once()


class TestWorkflowIntegration:
    """測試工作流集成"""
    
    def test_workflow_with_real_logger(self):
        """測試使用真實日誌器的工作流"""
        # 創建真實的日誌器（但不輸出到控制台）
        logger = LogPrinter()
        logger.enable_console = False  # 禁用控制台輸出
        
        workflow = WorkflowManager(logger)
        
        assert workflow.logger == logger
        assert workflow.parser is not None
        assert workflow.output_manager is not None
        assert workflow.engine is not None
    
    def test_workflow_error_handling(self):
        """測試工作流錯誤處理"""
        logger = Mock(spec=LogPrinter)
        workflow = WorkflowManager(logger)
        
        # 模擬解析器拋出異常
        workflow.parser.parse_file = Mock(side_effect=Exception("解析錯誤"))
        
        params = Mock(spec=ParameterManager)
        params.commands = "set_pin"
        params.proj = "TEST"
        
        try:
            workflow.process_rgt_file(params, "test.rgt")
            assert False, "應該拋出異常"
        except Exception as e:
            assert str(e) == "解析錯誤"
