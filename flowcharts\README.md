# DBI Transfer Tool 流程圖

本目錄包含 DBI Transfer Tool 的三個主要流程圖的 Mermaid 源代碼。

## 📁 文件說明

- `01_main_process_flow.mmd` - 主程序運行流程圖
- `02_module_interaction.mmd` - 模組交互流程圖  
- `03_data_flow.mmd` - 數據流程圖
- `README.md` - 本說明文件

## 🖼️ 轉換為 JPG 的方法

### 方法 1: 使用在線 Mermaid 編輯器 (推薦)

1. **訪問 Mermaid Live Editor**: https://mermaid.live/
2. **複製 .mmd 文件內容** 到編輯器中
3. **點擊 "Actions" → "Download PNG"** 或 **"Download SVG"**
4. **使用圖片編輯軟體轉換為 JPG**

### 方法 2: 使用 VS Code 擴展

1. **安裝 Mermaid Preview 擴展**
2. **打開 .mmd 文件**
3. **右鍵選擇 "Preview Mermaid"**
4. **截圖或導出為圖片**

### 方法 3: 使用 Mermaid CLI

```bash
# 安裝 Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# 轉換為 PNG
mmdc -i 01_main_process_flow.mmd -o 01_main_process_flow.png

# 轉換為 SVG
mmdc -i 01_main_process_flow.mmd -o 01_main_process_flow.svg
```

### 方法 4: 使用瀏覽器截圖

1. **在聊天界面中查看流程圖**
2. **右鍵點擊圖片 → "在新標籤頁中打開圖片"**
3. **使用截圖工具保存**:
   - Windows: `Win + Shift + S`
   - macOS: `Cmd + Shift + 4`
   - 或使用瀏覽器的截圖功能

## 🎨 圖片質量建議

- **PNG 格式**: 最佳質量，支持透明背景
- **SVG 格式**: 向量圖，可無限縮放
- **JPG 格式**: 較小文件大小，適合文檔嵌入

## 📋 流程圖說明

### 1. 主程序運行流程圖 (01_main_process_flow.mmd)
展示從程序啟動到結束的完整執行流程，包括：
- 命令行參數處理
- 配置文件載入和驗證
- RGT 文件解析和處理
- OP2 工具調用
- 錯誤處理和用戶交互

### 2. 模組交互流程圖 (02_module_interaction.mmd)
展示各個模組之間的關係和交互，分為六個層次：
- 主程序層
- 配置管理層
- 核心處理層
- 輸出管理層
- 驗證處理層
- 工具層

### 3. 數據流程圖 (03_data_flow.mmd)
展示數據在系統中的流動路徑：
- 輸入階段
- 處理階段
- 輸出階段
- 錯誤處理
- 多語言支持

## 🔧 自定義修改

如需修改流程圖：
1. 編輯對應的 .mmd 文件
2. 使用上述方法重新生成圖片
3. Mermaid 語法參考: https://mermaid.js.org/

## 📞 技術支持

如果在轉換過程中遇到問題，可以：
1. 檢查 Mermaid 語法是否正確
2. 嘗試不同的轉換工具
3. 調整圖片輸出設置
