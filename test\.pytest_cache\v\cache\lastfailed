{"test_log_printer.py::TestLogPrinter::test_init_with_parameters": true, "test_log_printer.py::TestLogPrinter::test_log_with_indent": true, "test_log_printer.py::TestLogPrinter::test_section_context_manager": true, "test_log_printer.py::TestLogPrinter::test_section_indent_level": true, "test_log_printer.py::TestLogPrinter::test_log_file_creation": true, "test_log_printer.py::TestLogPrinter::test_log_file_append": true, "test_log_printer.py::TestLogPrinter::test_disable_console": true, "test_log_printer.py::TestLogPrinter::test_disable_file": true, "test_log_printer.py::TestLogPrinter::test_file_write_error": true, "test_log_printer.py::TestLogPrinter::test_log_formatting": true, "test_log_printer.py::TestLogPrinter::test_empty_message": true, "test_log_printer.py::TestLogPrinter::test_none_message": true, "test_core_engine.py::TestAddressOffsetCalculation::test_address_offset_different_base_addresses": true}