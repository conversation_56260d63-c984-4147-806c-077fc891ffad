////////////////////////////////////////////////////////////////////////////////////////////////////
//Tool version : 0.01                                                                             //
//Proj         : ICNA3611                                                                         //
//Test Item    : test                                                                             //
//Author       : Sychang                                                                          //
//Date         : 2025/06/23 18:54:23                                                              //
////////////////////////////////////////////////////////////////////////////////////////////////////
//;-------------------------------------------------------------------------------------------------
//;ICNA3611_test.rgt start
//;FOLDER(POWER)                                                                                  //
//;//this file is used for test dbi transfter tool                                                //
//;                                                                                               //
//;//READ format                                                                                  //
//;70000=68//RO                                                                                   //
//;470000=68//RO//                                                                                //
//;                                                                                               //
//;76000=12//RO                                                                                   //
//;76004=1234//RO                                                                                 //
//;76012=123456//RO//ALIGN(L)                                                                     //
//;47504=12XXXXXX//RO                                                                             //
//;                                                                                               //
//;76000=12XX//RO                                                                                 //
//;47504=12XX34//RO                                                                               //
//;76012=XX34XX56//RO                                                                             //
//;                                                                                               //
//;//WRITE format                                                                                 //
//;70000=68//WO                                                                                   //
//;470000=68//WO                                                                                  //
//;                                                                                               //
//;76000=12//WO                                                                                   //
//;76000=13//WO                                                                                   //
//;76000=14//WO                                                                                   //
//;76004=1234//WO//ALIGN(L)                                                                       //
//;76012=123456//WO                                                                               //
//;47504=12345678//WO                                                                             //
//;                                                                                               //
//;//TAG                                                                                          //
//;76012=12345678//WO//TAG(3:REG_TMP0, 2:REG_A)                                                   //
//;                                                                                               //
//;//COMMAND                                                                                      //
//;                                                                                               //
//;                                                                                               //
//;//delay                                                                                        //
//;delay 2                                                                                        //
//;                                                                                               //
//;ICNA3611_test.rgt end
//;-------------------------------------------------------------------------------------------------
#include open_ddi_p_mode.op2.txt

//this file is used for test dbi transfter tool
//READ format
//70000=68//RO
R1A 03
R1B 00 07 00 00
R1E 00
R1F #4 XX XX XX 68 Read 4 bytes from UCS.1F and check the byte [4]

//470000=68//RO//
//R1A 03
R1B 00 47 00 00
R1E 00
R1F #4 XX XX XX 68 Read 4 bytes from UCS.1F and check the byte [4]

//76000=12//RO
//R1A 03
R1B 00 07 60 00
R1E 00
R1F #4 XX XX XX 12 Read 4 bytes from UCS.1F and check the byte [4]

//76004=1234//RO
//R1A 03
R1B 00 07 60 04
R1E 00
R1F #4 XX XX 12 34 Read 4 bytes from UCS.1F and check the byte [3, 4]

//76012=123456//RO//ALIGN(L)
R1A 02
R1B 00 07 60 12
R1E 00
R1F #3 12 34 56 Read 3 bytes from UCS.1F and check the byte [1, 2, 3]

//47504=12XXXXXX//RO
R1A 03
R1B 00 04 75 04
R1E 00
R1F #4 12 XX XX XX Read 4 bytes from UCS.1F and check the byte [1]

//76000=12XX//RO
//R1A 03
R1B 00 07 60 00
R1E 00
R1F #4 XX XX 12 XX Read 4 bytes from UCS.1F and check the byte [3]

//47504=12XX34//RO
//R1A 03
R1B 00 04 75 04
R1E 00
R1F #4 XX 12 XX 34 Read 4 bytes from UCS.1F and check the byte [2, 4]

//76012=XX34XX56//RO
//R1A 03
R1B 00 07 60 12
R1E 00
R1F #4 XX 34 XX 56 Read 4 bytes from UCS.1F and check the byte [2, 4]

//WRITE format
//70000=68//WO
R1A 00
R1B 00 07 00 00
R1D 68
R1C 00

//470000=68//WO
//R1A 00
R1B 00 47 00 00
R1D 68
R1C 00

//76000=12//WO
//R1A 00
R1B 00 07 60 00
R1D 12
R1C 00

//76000=13//WO
//R1A 00
//R1B 00 07 60 00
R1D 13
R1C 00

//76000=14//WO
//R1A 00
//R1B 00 07 60 00
R1D 14
R1C 00

//76004=1234//WO//ALIGN(L)
R1A 01
R1B 00 07 60 04
R1D 12 34
R1C 00

//76012=123456//WO
R1A 02
R1B 00 07 60 12
R1D 12 34 56
R1C 00

//47504=12345678//WO
R1A 03
R1B 00 04 75 04
R1D 12 34 56 78
R1C 00

//TAG
//76012=12345678//WO//TAG(3:REG_TMP0, 2:REG_A)
//R1A 03
R1B 00 07 60 12
R1D 12 34 56(REG_A) 78(REG_TMP0)
R1C 00

//COMMAND
//delay
delay 2
