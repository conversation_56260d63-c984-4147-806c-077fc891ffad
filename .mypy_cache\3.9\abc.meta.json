{"data_mtime": 1750670042, "dep_lines": [4, 1, 2, 5, 6, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 30], "dependencies": ["collections.abc", "_typeshed", "sys", "typing", "typing_extensions", "builtins", "_frozen_importlib"], "hash": "c9b7741802bc1bc63edbfba0ae97a7c4397bb9db", "id": "abc", "ignore_all": true, "interface_hash": "69a689ca39af5379087c6222a525e0f7128ca9d0", "mtime": 1750572233, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\abc.pyi", "plugin_data": null, "size": 2038, "suppressed": [], "version_id": "1.16.1"}