"""
輸出管理器 - 負責所有輸出相關功能
"""
import os
import datetime
import sys
from typing import Dict, List, TextIO
from pathlib import Path

# 添加父目錄到路徑以支援直接執行
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from constant import FilePaths
from logPrinter import LogPrinter
from parameterManger import ParameterManager
from core.engine import DbiTransferEngine


class OutputManager:
    """輸出管理器 - 負責檔案輸出、標題生成、路徑管理等"""
    
    def __init__(self, logger: LogPrinter):
        self.logger = logger
    
    def get_front_text(self, texts: Dict[str, str], width: int, delimiter: str = "//") -> str:
        """生成前置文字格式"""
        max_length = max(len(k) for k in texts)
        line = delimiter * (width // len(delimiter))
        content = line + "\n"
        for key, val in texts.items():
            full_text = f"{key.strip():<{max_length}} : {val}"
            blank_width = width - len(full_text) - 2 * len(delimiter)
            content += delimiter + full_text + " " * blank_width + delimiter + "\n"
        content += line + "\n"
        return content
    
    def build_title(self, params: ParameterManager, test_item: str) -> Dict[str, str]:
        """建立標題資訊"""
        return {
            "Tool version": params.version,
            "Proj": params.proj,
            "Test Item": test_item,
            "Author": params.author,
            "Date": datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S"),
        }
    
    def get_output_folder(self, proj: str, folder: str) -> Path:
        """取得輸出資料夾路徑"""
        output_path = Path(FilePaths.CASES_DIR) / proj / folder
        os.makedirs(output_path, exist_ok=True)
        return output_path
    
    def write_title_section(self, f: TextIO, title: Dict[str, str], output_width: int, 
                           rgt_file: str, rgt_content: List[str]) -> None:
        """寫入標題區段"""
        self.logger.step("Writing header section")
        f.write(self.get_front_text(title, width=output_width))
        f.write(f"//;{'-'*(output_width-3)}\n")
        f.write(f"//;{rgt_file} start\n")
        for line in rgt_content:
            f.write(f"//;{line.strip():<{output_width-5}}//\n")
        f.write(f"//;{rgt_file} end\n")
        f.write(f"//;{'-'*(output_width-3)}\n")

        self.logger.step("Writing include section")
        f.write(f"#include {FilePaths.INCLUDE_FILE}\n\n")
    
    def write_output_files(self, output_folder: Path, transferred_file: str, c_file_for_sim: str,
                          title: Dict[str, str], decoded_content: List[List], output_width: int,
                          rgt_content: List[str], in_file: str, engine: DbiTransferEngine) -> None:
        """寫入輸出檔案"""
        with open(output_folder / transferred_file, "w", encoding="utf-8") as f, \
             open(output_folder / c_file_for_sim, "w", encoding="utf-8") as cf, \
             self.logger.section("Start output transferred file"):
            
            # 重置引擎狀態
            engine.reset_state()
            
            # 寫入標題
            self.write_title_section(f, title, output_width, in_file, rgt_content)
            
            # 處理主要內容
            engine.process_decoded_content(f, cf, decoded_content)
    
    def prepare_filenames(self, proj: str, test_item: str) -> tuple[str, str, str]:
        """準備檔案名稱"""
        base = f"{proj}_{test_item}"
        return f"{base}.rgt", f"{base}.txt", f"{base}.c"
