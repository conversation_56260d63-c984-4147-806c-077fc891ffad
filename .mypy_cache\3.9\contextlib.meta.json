{"data_mtime": 1750670042, "dep_lines": [5, 1, 2, 3, 6, 7, 8, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 30], "dependencies": ["collections.abc", "abc", "sys", "_typeshed", "types", "typing", "typing_extensions", "builtins", "_frozen_importlib"], "hash": "220307e935ba4def7da41a381c69703b68d2725f", "id": "contextlib", "ignore_all": true, "interface_hash": "2a583e44f8ec2dfecc0b66aef9b1f0e721b26b90", "mtime": 1750572233, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\contextlib.pyi", "plugin_data": null, "size": 9549, "suppressed": [], "version_id": "1.16.1"}