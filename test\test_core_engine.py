"""
測試核心轉換引擎
"""
import pytest
from unittest.mock import Mock, mock_open, patch
from io import StringIO

from core.engine import DbiTransferEngine, extract_label_value
from constant import FieldIndex, CommandTypes


class TestExtractLabelValue:
    """測試標籤值提取函數"""

    def test_extract_label_value_success(self):
        """測試成功提取標籤值"""
        text = "some text TAG(test_value) more text"
        result = extract_label_value(text, "TAG")
        assert result == "test_value"

    def test_extract_label_value_not_found(self):
        """測試標籤不存在"""
        text = "some text without tag"
        result = extract_label_value(text, "TAG")
        assert result is None

    def test_extract_label_value_empty_string(self):
        """測試空字串"""
        result = extract_label_value("", "TAG")
        assert result is None

    def test_extract_label_value_none(self):
        """測試 None 輸入"""
        result = extract_label_value(None, "TAG")
        assert result is None


class TestDbiTransferEngine:
    """測試 DBI 轉換引擎"""
    
    def test_init(self, mock_logger):
        """測試初始化"""
        engine = DbiTransferEngine(mock_logger)
        assert engine.logger == mock_logger
        assert engine._last_register_1A == "XX"
        assert engine._last_register_1B == "XX XX XX XX"
    
    def test_write_register_with_optimization_same_value(self, mock_logger):
        """測試寫入相同值（應該有註釋）"""
        engine = DbiTransferEngine(mock_logger)
        output = StringIO()

        result = engine._write_register_with_optimization(output, "R1A", "01", "01")

        assert result == "01"
        assert output.getvalue() == "//R1A 01\n"

    def test_write_register_with_optimization_different_value(self, mock_logger):
        """測試寫入不同值（不應該有註釋）"""
        engine = DbiTransferEngine(mock_logger)
        output = StringIO()

        result = engine._write_register_with_optimization(output, "R1A", "02", "01")

        assert result == "02"
        assert output.getvalue() == "R1A 02\n"
    
    def test_write_c_register_operation_reg8(self, mock_logger):
        """測試寫入 8 位暫存器"""
        engine = DbiTransferEngine(mock_logger)
        output = StringIO()

        engine._write_c_register_operation(output, "1234", "AB", 1)

        assert output.getvalue() == "REG8(0x1234)=0XAB;\n"

    def test_write_c_register_operation_reg16(self, mock_logger):
        """測試寫入 16 位暫存器"""
        engine = DbiTransferEngine(mock_logger)
        output = StringIO()

        engine._write_c_register_operation(output, "1234", "ABCD", 2)

        assert output.getvalue() == "REG16(0x1234)=0XABCD;\n"

    def test_write_c_register_operation_reg32(self, mock_logger):
        """測試寫入 32 位暫存器"""
        engine = DbiTransferEngine(mock_logger)
        output = StringIO()

        engine._write_c_register_operation(output, "1234", "ABCDEF12", 4)

        assert output.getvalue() == "REG32(0x1234)=0XABCDEF12;\n"
    
    def test_split_data_into_byte_groups_basic(self, mock_logger):
        """測試基本資料分組"""
        engine = DbiTransferEngine(mock_logger)

        result = engine._split_data_into_byte_groups("ABCD", "")

        assert result == ["AB", "CD"]

    def test_split_data_into_byte_groups_with_tags(self, mock_logger):
        """測試帶標籤的資料分組"""
        engine = DbiTransferEngine(mock_logger)

        result = engine._split_data_into_byte_groups("ABCD", "TAG(0:label1,1:label2)")

        assert result == ["AB(label1)", "CD(label2)"]
    
    def test_write_simple_command(self, mock_logger):
        """測試寫入簡單命令"""
        engine = DbiTransferEngine(mock_logger)
        f_output = StringIO()
        cf_output = StringIO()

        line = [None] * 5
        line[FieldIndex.FULL_DAT] = "// test comment"

        engine._write_simple_command(f_output, cf_output, line)

        assert f_output.getvalue() == "// test comment\n"
        assert cf_output.getvalue() == "// test comment\n"
    
    def test_reset_state(self, mock_logger):
        """測試重置狀態"""
        engine = DbiTransferEngine(mock_logger)
        engine._last_register_1A = "01"
        engine._last_register_1B = "01 02 03 04"

        engine.reset_state()

        assert engine._last_register_1A == "XX"
        assert engine._last_register_1B == "XX XX XX XX"

    def test_generate_c_write_commands_1_byte(self, mock_logger):
        """測試 1 字節 C 寫入命令生成"""
        engine = DbiTransferEngine(mock_logger)
        c_file = StringIO()

        parsed_line = [None, "001234", "AB", "WO", "//Test 1 byte write"]
        address = "001234"
        data = "AB"
        data_length_bytes = 1

        engine._generate_c_write_commands(c_file, parsed_line, address, data, data_length_bytes)

        output = c_file.getvalue()
        assert "//Test 1 byte write" in output
        assert "REG8(0x1234)=0XAB;" in output

    def test_generate_c_write_commands_2_byte(self, mock_logger):
        """測試 2 字節 C 寫入命令生成"""
        engine = DbiTransferEngine(mock_logger)
        c_file = StringIO()

        parsed_line = [None, "001234", "ABCD", "WO", "//Test 2 byte write"]
        address = "001234"
        data = "ABCD"
        data_length_bytes = 2

        engine._generate_c_write_commands(c_file, parsed_line, address, data, data_length_bytes)

        output = c_file.getvalue()
        assert "//Test 2 byte write" in output
        assert "REG16(0x1234)=0XABCD;" in output

    def test_generate_c_write_commands_4_byte(self, mock_logger):
        """測試 4 字節 C 寫入命令生成"""
        engine = DbiTransferEngine(mock_logger)
        c_file = StringIO()

        parsed_line = [None, "001234", "ABCDEF12", "WO", "//Test 4 byte write"]
        address = "001234"
        data = "ABCDEF12"
        data_length_bytes = 4

        engine._generate_c_write_commands(c_file, parsed_line, address, data, data_length_bytes)

        output = c_file.getvalue()
        assert "//Test 4 byte write" in output
        assert "REG32(0x1234)=0XABCDEF12;" in output

    def test_generate_c_write_commands_3_byte_special(self, mock_logger):
        """測試 3 字節特殊情況的 C 寫入命令生成"""
        from logPrinter import LogPrinter
    
        print("\n" + "="*60)
        print("🧪 測試 3字節特殊情況")
        print("="*60)
   
        real_logger = LogPrinter()
        engine = DbiTransferEngine(real_logger)
        #engine = DbiTransferEngine(mock_logger)
        c_file = StringIO()

        parsed_line = [None, "001234", "ABCDEF", "WO", "//Test 3 byte write"]
        address = "001234"
        data = "ABCDEF"
        data_length_bytes = 3

        engine._generate_c_write_commands(c_file, parsed_line, address, data, data_length_bytes)

        output = c_file.getvalue()
        print(f"sychang: {output}\n")
        print("\n📤 生成的 C 代碼:")
        print("─" * 40)
        print(f'disapear: <{output}>\n')
        print("─" * 40)
        print("✅ 測試完成\n")
        assert "//Test 3 byte write" in output
        # 3字節資料應該分割成兩個操作
        assert "REG8(0x1234)" in output   # 第一個字節，原地址
        assert "REG16(0x1235)" in output  # 後兩個字節，地址 + 1


class TestAddressOffsetCalculation:
    """測試地址偏移計算邏輯"""

    def test_address_offset_calculation_logic(self, mock_logger):
        """測試地址偏移計算的核心邏輯"""
        engine = DbiTransferEngine(mock_logger)

        # 測試案例：驗證當前的地址偏移計算邏輯
        test_cases = [
            {
                "name": "1字節資料",
                "address": "001234",
                "data_length_bytes": 1,
                "expected_addr": "1234",  # 當前邏輯：直接使用原地址
            },
            {
                "name": "2字節資料",
                "address": "001234",
                "data_length_bytes": 2,
                "expected_addr": "1234",  # 當前邏輯：直接使用原地址
            },
            {
                "name": "4字節資料",
                "address": "001234",
                "data_length_bytes": 4,
                "expected_addr": "1234",  # 當前邏輯：直接使用原地址
            }
        ]

        for case in test_cases:
            c_file = StringIO()
            parsed_line = [None, case["address"], "A" * (case["data_length_bytes"] * 2), "WO", f"//Test {case['name']}"]

            engine._generate_c_write_commands(
                c_file, parsed_line, case["address"],
                parsed_line[FieldIndex.DATA_IDX], case["data_length_bytes"]
            )

            output = c_file.getvalue()
            # 驗證地址偏移計算結果
            assert f"0x{case['expected_addr'].upper()}" in output, f"地址偏移計算錯誤：{case['name']}"

    def test_address_offset_different_addresses(self, mock_logger):
        """測試不同地址的偏移計算"""
        engine = DbiTransferEngine(mock_logger)

        test_addresses = [
            ("001000", "1000"),
            ("00ABCD", "abcd"),
            ("00FFFF", "ffff"),
        ]

        for input_addr, expected_addr in test_addresses:
            c_file = StringIO()
            parsed_line = [None, input_addr, "AB", "WO", f"//Test addr {input_addr}"]

            engine._generate_c_write_commands(c_file, parsed_line, input_addr, "AB", 1)

            output = c_file.getvalue()
            # 檢查地址是否正確（不區分大小寫）
            assert f"0x{expected_addr}".upper() in output.upper(), f"地址 {input_addr} 處理錯誤"

    def test_address_offset_regression_check(self, mock_logger):
        """回歸測試：確保地址偏移計算邏輯的一致性"""
        engine = DbiTransferEngine(mock_logger)

        # 這個測試確保地址偏移計算邏輯的一致性
        # 如果將來需要修改地址計算邏輯，這個測試會提醒開發者

        address = "001234"

        # 測試 1字節資料
        c_file_1 = StringIO()
        engine._generate_c_write_commands(c_file_1, [None, address, "AB", "WO", "//1 byte"], address, "AB", 1)
        output_1 = c_file_1.getvalue()

        # 測試 2字節資料
        c_file_2 = StringIO()
        engine._generate_c_write_commands(c_file_2, [None, address, "ABCD", "WO", "//2 byte"], address, "ABCD", 2)
        output_2 = c_file_2.getvalue()

        # 當前邏輯：所有資料長度都使用相同的基地址
        assert "REG8(0x1234)" in output_1
        assert "REG16(0x1234)" in output_2

        # 如果這個測試失敗，說明地址偏移計算邏輯發生了變化
        # 需要檢查是否是有意的修改
