#!/usr/bin/env python3
"""
測試運行腳本
"""
import sys
import os
import subprocess
import re
from pathlib import Path


def print_failed_test_commands(output):
    """解析測試輸出並顯示失敗測試的詳細查看指令"""
    print("\n🔍 查看詳細錯誤信息的指令：")

    # 使用正則表達式找到失敗的測試
    failed_pattern = r'(test[^:]+::[^:]+::[^\s]+)\s+FAILED'
    failed_tests = re.findall(failed_pattern, output)

    if failed_tests:
        for test in failed_tests:
            # 將 Windows 路徑分隔符轉換為 Unix 風格
            test_path = test.replace('\\', '/')
            print(f"   pytest {test_path} -v -s --tb=long")
    else:
        # 如果正則表達式沒有匹配到，嘗試另一種模式
        lines = output.split('\n')
        for line in lines:
            if 'FAILED' in line and '::' in line:
                # 提取測試路徑
                parts = line.split()
                for part in parts:
                    if '::' in part and 'test_' in part:
                        test_path = part.split()[0] if ' ' in part else part
                        # 將 Windows 路徑分隔符轉換為 Unix 風格
                        test_path = test_path.replace('\\', '/')
                        print(f"   pytest {test_path} -v -s --tb=long")
                        break


def run_tests():
    """運行所有測試"""
    print("🧪 開始運行 DBI Transfer Tool 測試套件...")

    # 確保在正確的目錄
    test_dir = Path(__file__).parent
    os.chdir(test_dir.parent)  # 切換到專案根目錄

    try:
        # 運行 pytest 並捕獲輸出
        result = subprocess.run([
            sys.executable, "-m", "pytest",
            "test/",
            "-v",
            "--tb=short",
            "--color=yes"
        ], check=False, capture_output=True, text=True)

        # 顯示輸出
        print(result.stdout)
        if result.stderr:
            print(result.stderr)

        if result.returncode == 0:
            print("\n✅ 所有測試通過！")
        else:
            print(f"\n❌ 測試失敗，退出碼：{result.returncode}")
            print_failed_test_commands(result.stdout)

        return result.returncode

    except FileNotFoundError:
        print("❌ 錯誤：找不到 pytest。請安裝 pytest：")
        print("   pip install pytest")
        return 1
    except Exception as e:
        print(f"❌ 運行測試時發生錯誤：{e}")
        return 1


def run_specific_test(test_name):
    """運行特定測試"""
    print(f"🧪 運行特定測試：{test_name}")

    test_dir = Path(__file__).parent
    os.chdir(test_dir.parent)

    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest",
            f"test/{test_name}",
            "-v",
            "--tb=short",
            "--color=yes"
        ], check=False, capture_output=True, text=True)

        # 顯示輸出
        print(result.stdout)
        if result.stderr:
            print(result.stderr)

        if result.returncode != 0:
            print(f"\n❌ 測試失敗，退出碼：{result.returncode}")
            print_failed_test_commands(result.stdout)

        return result.returncode

    except Exception as e:
        print(f"❌ 運行測試時發生錯誤：{e}")
        return 1


def run_coverage():
    """運行測試覆蓋率分析"""
    print("📊 運行測試覆蓋率分析...")

    test_dir = Path(__file__).parent
    os.chdir(test_dir.parent)

    try:
        # 運行帶覆蓋率的測試
        result = subprocess.run([
            sys.executable, "-m", "pytest",
            "test/",
            "--cov=source",
            "--cov-report=html",
            "--cov-report=term-missing",
            "-v"
        ], check=False, capture_output=True, text=True)

        # 顯示輸出
        print(result.stdout)
        if result.stderr:
            print(result.stderr)

        if result.returncode == 0:
            print("\n📊 覆蓋率報告已生成在 htmlcov/ 目錄")
        else:
            print(f"\n❌ 覆蓋率測試失敗，退出碼：{result.returncode}")
            print_failed_test_commands(result.stdout)

        return result.returncode

    except FileNotFoundError:
        print("❌ 錯誤：找不到 pytest-cov。請安裝：")
        print("   pip install pytest-cov")
        return 1


def main():
    """主函數"""
    if len(sys.argv) == 1:
        # 運行所有測試
        return run_tests()
    
    elif len(sys.argv) == 2:
        arg = sys.argv[1]
        
        if arg == "--help" or arg == "-h":
            print("DBI Transfer Tool 測試運行器")
            print("\n用法：")
            print("  python run_tests.py              # 運行所有測試")
            print("  python run_tests.py <test_file>  # 運行特定測試檔案")
            print("  python run_tests.py --coverage   # 運行覆蓋率分析")
            print("  python run_tests.py --help       # 顯示此幫助")
            return 0
            
        elif arg == "--coverage":
            return run_coverage()
            
        else:
            # 運行特定測試
            return run_specific_test(arg)
    
    else:
        print("❌ 錯誤：參數過多")
        print("使用 --help 查看用法")
        return 1


if __name__ == "__main__":
    sys.exit(main())
