"""
測試解析器模組
"""
import pytest
from unittest.mock import Mock, patch, mock_open

from parsers.rgt_parser import Rgt<PERSON>arser
from constant import FieldIndex, CommandTypes


class TestRgtParser:
    """測試 RGT 解析器"""
    
    def test_init(self, mock_logger):
        """測試初始化"""
        parser = Rgt<PERSON>arser(mock_logger)
        assert parser.logger == mock_logger
        assert parser.error_handler is not None
    
    def test_is_valid_data_pattern_valid(self, mock_logger):
        """測試有效的資料格式"""
        parser = RgtParser(mock_logger)
        
        assert parser.is_valid_data_pattern("AB") == True
        assert parser.is_valid_data_pattern("ABCD") == True
        assert parser.is_valid_data_pattern("ABCDEF") == True
        assert parser.is_valid_data_pattern("ABCDEF12") == True
        assert parser.is_valid_data_pattern("XX") == True
        assert parser.is_valid_data_pattern("XXXX") == True
    
    def test_is_valid_data_pattern_invalid(self, mock_logger):
        """測試無效的資料格式"""
        parser = RgtParser(mock_logger)
        
        assert parser.is_valid_data_pattern("A") == False  # 長度不對
        assert parser.is_valid_data_pattern("ABC") == False  # 長度不對
        assert parser.is_valid_data_pattern("ABCDEFGH1") == False  # 長度不對
        assert parser.is_valid_data_pattern("XY") == False  # 無效字符
        assert parser.is_valid_data_pattern("ABCG") == False  # 無效字符
    
    def test_get_label_value(self, mock_logger):
        """測試標籤值提取"""
        parser = RgtParser(mock_logger)
        
        result = parser.get_label_value("test FOLDER(value) more", "FOLDER")
        assert result == "value"
        
        result = parser.get_label_value("no folder here", "FOLDER")
        assert result is None
        
        result = parser.get_label_value(None, "FOLDER")
        assert result is None
    
    def test_parse_comment_line(self, mock_logger):
        """測試解析註釋行"""
        parser = RgtParser(mock_logger)
        
        result = parser.parse_comment_line("// This is a comment")
        
        assert result[FieldIndex.COMMAND_IDX] == CommandTypes.COMMENT
        assert result[FieldIndex.FULL_DAT] == "// This is a comment"
    
    def test_parse_delay_line_valid(self, mock_logger):
        """測試解析有效的延遲行"""
        parser = RgtParser(mock_logger)
        
        result = parser.parse_delay_line("delay 100", 1)
        
        assert result[FieldIndex.COMMAND_IDX] == CommandTypes.DELAY
        assert result[FieldIndex.FULL_DAT] == "delay 100"
    
    def test_parse_delay_line_with_decimal(self, mock_logger):
        """測試解析帶小數的延遲行"""
        parser = RgtParser(mock_logger)
        
        result = parser.parse_delay_line("delay 100.5", 1)
        
        assert result[FieldIndex.COMMAND_IDX] == CommandTypes.DELAY
        assert result[FieldIndex.FULL_DAT] == "delay 100.5"
    
    @patch('builtins.open', mock_open(read_data="// Test file\ndelay 100\n12345=ABCD"))
    def test_load_file_content_success(self, mock_logger):
        """測試成功載入檔案"""
        parser = RgtParser(mock_logger)
        
        result = parser.load_file_content("test.rgt", "TEST")
        
        assert len(result) == 3
        assert result[0] == "// Test file\n"
        assert result[1] == "delay 100\n"
        assert result[2] == "12345=ABCD"
    
    def test_parse_register_command_wo(self, mock_logger):
        """測試解析寫入暫存器命令"""
        parser = RgtParser(mock_logger)
        
        result = parser.parse_register_command("12345=ABCD//WO//Test comment", 1)
        
        assert result[FieldIndex.COMMAND_IDX] == "WO"
        assert result[FieldIndex.ADDR_IDX] == "012345"
        assert result[FieldIndex.DATA_IDX] == "ABCD"
        assert result[FieldIndex.COMMENT_IDX] == "Test comment"
        assert result[FieldIndex.FULL_DAT] == "12345=ABCD//WO//Test comment"
    
    def test_parse_register_command_ro(self, mock_logger):
        """測試解析讀取暫存器命令"""
        parser = RgtParser(mock_logger)
        
        result = parser.parse_register_command("67890=XX12//RO//Read test", 1)
        
        assert result[FieldIndex.COMMAND_IDX] == "RO"
        assert result[FieldIndex.ADDR_IDX] == "067890"
        assert result[FieldIndex.DATA_IDX] == "XX12"
        assert result[FieldIndex.COMMENT_IDX] == "Read test"
        assert result[FieldIndex.FULL_DAT] == "67890=XX12//RO//Read test"
    
    def test_parse_register_command_6_digit_addr(self, mock_logger):
        """測試解析 6 位地址"""
        parser = RgtParser(mock_logger)
        
        result = parser.parse_register_command("123456=ABCD//WO", 1)
        
        assert result[FieldIndex.ADDR_IDX] == "123456"  # 6位地址不需要補0
    
    @patch('builtins.open', mock_open(read_data="""// Test RGT file
FOLDER(test_folder)
#set_pin
delay 100
12345=ABCD//WO//Test write
67890=XX12//RO//Test read
"""))
    def test_parse_file_complete(self, mock_logger):
        """測試完整的檔案解析"""
        parser = RgtParser(mock_logger)
        commands = ["set_pin", "get_pin"]
        
        content, valid_content, folder = parser.parse_file("test.rgt", commands, "TEST")
        
        # 檢查原始內容
        assert len(content) == 6
        
        # 檢查解析後內容
        assert len(valid_content) == 5  # 註釋、命令、延遲、兩個暫存器命令
        
        # 檢查資料夾名稱
        assert folder == "test_folder"
        
        # 檢查各種命令類型
        assert valid_content[0][FieldIndex.COMMAND_IDX] == CommandTypes.COMMENT
        assert valid_content[1][FieldIndex.COMMAND_IDX] == CommandTypes.CMD
        assert valid_content[2][FieldIndex.COMMAND_IDX] == CommandTypes.DELAY
        assert valid_content[3][FieldIndex.COMMAND_IDX] == "WO"
        assert valid_content[4][FieldIndex.COMMAND_IDX] == "RO"
