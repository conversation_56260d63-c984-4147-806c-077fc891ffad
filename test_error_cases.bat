@echo off
chcp 65001 >nul
REM === DBI Transfer Tool 錯誤情況測試腳本 ===

echo ========================================
echo    DBI Transfer Tool 錯誤測試
echo ========================================
echo.

REM 檢查可執行文件是否存在
if not exist "DBI_Transfer\DBI_Transfer.exe" (
    echo ❌ 找不到 DBI_Transfer.exe
    echo 請先運行 build_exe.bat 構建程序
    pause
    exit /b 1
)

set EXE_PATH=DBI_Transfer\DBI_Transfer.exe

echo 🧪 開始錯誤情況測試...
echo.

REM === 測試 1: 無效命令行選項 ===
echo [測試 1] 無效命令行選項
echo 命令: %EXE_PATH% --invalid-option
echo.
"%EXE_PATH%" --invalid-option
echo.
echo 按任意鍵繼續下一個測試...
pause >nul
echo.

REM === 測試 2: 不存在的測試項目 ===
echo [測試 2] 不存在的測試項目
echo 命令: %EXE_PATH% nonexistent_test_item
echo.
"%EXE_PATH%" nonexistent_test_item
echo.
echo 按任意鍵繼續下一個測試...
pause >nul
echo.

REM === 測試 3: 空的測試項目名稱 ===
echo [測試 3] 空的測試項目名稱
echo 命令: %EXE_PATH% ""
echo.
"%EXE_PATH%" ""
echo.
echo 按任意鍵繼續下一個測試...
pause >nul
echo.

REM === 測試 4: 配置文件備份和恢復測試 ===
echo [測試 4] 配置文件不存在
if exist "para.txt" (
    echo 備份原配置文件...
    copy para.txt para.txt.backup >nul
    del para.txt
    
    echo 命令: %EXE_PATH% test_item
    echo.
    "%EXE_PATH%" test_item
    
    echo.
    echo 恢復配置文件...
    copy para.txt.backup para.txt >nul
    del para.txt.backup
) else (
    echo ⚠️  para.txt 不存在，跳過此測試
)
echo.
echo 按任意鍵繼續下一個測試...
pause >nul
echo.

REM === 測試 5: 格式錯誤的配置文件 ===
echo [測試 5] 格式錯誤的配置文件
if exist "para.txt" (
    echo 備份原配置文件...
    copy para.txt para.txt.backup >nul
    
    echo 創建格式錯誤的配置文件...
    echo invalid config content without equals signs > para.txt
    echo another invalid line >> para.txt
    
    echo 命令: %EXE_PATH% test_item
    echo.
    "%EXE_PATH%" test_item
    
    echo.
    echo 恢復配置文件...
    copy para.txt.backup para.txt >nul
    del para.txt.backup
) else (
    echo ⚠️  para.txt 不存在，跳過此測試
)
echo.
echo 按任意鍵繼續下一個測試...
pause >nul
echo.

REM === 測試 6: op2.exe 不存在 ===
echo [測試 6] op2.exe 不存在
if exist "DBI_Transfer\op2.exe" (
    echo 備份 op2.exe...
    ren "DBI_Transfer\op2.exe" "op2.exe.backup"
    
    echo 命令: %EXE_PATH% test_item
    echo.
    "%EXE_PATH%" test_item
    
    echo.
    echo 恢復 op2.exe...
    ren "DBI_Transfer\op2.exe.backup" "op2.exe"
) else (
    echo ⚠️  op2.exe 不存在，跳過此測試
)
echo.
echo 按任意鍵繼續下一個測試...
pause >nul
echo.

REM === 測試 7: cases 目錄不存在 ===
echo [測試 7] cases 目錄不存在
if exist "DBI_Transfer\cases" (
    echo 備份 cases 目錄...
    ren "DBI_Transfer\cases" "cases_backup"
    
    echo 命令: %EXE_PATH% test_item
    echo.
    "%EXE_PATH%" test_item
    
    echo.
    echo 恢復 cases 目錄...
    ren "DBI_Transfer\cases_backup" "cases"
) else (
    echo ⚠️  cases 目錄不存在，跳過此測試
)
echo.
echo 按任意鍵繼續下一個測試...
pause >nul
echo.

REM === 測試 8: 特殊字符的測試項目名稱 ===
echo [測試 8] 特殊字符的測試項目名稱
echo 命令: %EXE_PATH% "test*item?with<special>chars"
echo.
"%EXE_PATH%" "test*item?with<special>chars"
echo.
echo 按任意鍵繼續...
pause >nul
echo.

echo ========================================
echo 🎉 錯誤測試完成！
echo ========================================
echo.
echo 📋 測試總結:
echo   - 測試了 8 種常見的錯誤情況
echo   - 檢查了程序的錯誤處理能力
echo   - 驗證了用戶友好的錯誤信息
echo.
echo 💡 如果發現任何問題，請檢查:
echo   1. 錯誤信息是否清晰易懂
echo   2. 程序是否正確等待用戶按鍵
echo   3. 是否有未處理的異常
echo.
pause
