# DBI Transfer Tool 測試套件

這是 DBI Transfer Tool 的完整測試套件，包含單元測試、整合測試和工具測試。

## 📁 測試結構

```
test/
├── __init__.py              # 測試套件初始化
├── conftest.py             # pytest 配置和共用 fixtures
├── pytest.ini             # pytest 配置文件
├── requirements.txt        # 測試依賴
├── run_tests.py           # 測試運行腳本
├── test_application.py    # 應用程式測試
├── test_core_engine.py    # 核心引擎測試
├── test_output.py         # 輸出管理器測試
├── test_parsers.py        # 解析器測試
├── test_utils.py          # 工具模組測試
├── test_validation.py     # 驗證模組測試
└── README.md              # 本文件
```

## 🚀 快速開始

### 1. 安裝測試依賴

```bash
pip install -r test/requirements.txt
```

### 2. 運行所有測試

```bash
# 方法 1：使用測試腳本
python test/run_tests.py

# 方法 2：直接使用 pytest
pytest test/ -v
```

### 3. 運行特定測試

```bash
# 運行特定測試文件
python test/run_tests.py test_core_engine.py

# 運行特定測試類別
pytest test/test_core_engine.py::TestDbiTransferEngine -v

# 運行特定測試方法
pytest test/test_core_engine.py::TestDbiTransferEngine::test_init -v
```

### 4. 生成覆蓋率報告

```bash
python test/run_tests.py --coverage
```

## 📊 測試覆蓋範圍

### 核心模組測試
- **`test_core_engine.py`**: 測試 DBI 轉換引擎
  - 標籤值提取功能
  - 資料分組處理
  - 暫存器命令寫入
  - 狀態管理

### 解析器測試
- **`test_parsers.py`**: 測試 RGT 檔案解析器
  - 資料格式驗證
  - 各種命令類型解析
  - 檔案載入和處理
  - 錯誤處理

### 驗證模組測試
- **`test_validation.py`**: 測試版本和錯誤處理
  - 語義版本號解析
  - 配置檔案結構驗證
  - 版本兼容性檢查
  - 錯誤處理機制

### 輸出管理測試
- **`test_output.py`**: 測試輸出檔案管理
  - 標題生成
  - 檔案路徑管理
  - 輸出格式化
  - 檔案寫入操作

### 工具模組測試
- **`test_utils.py`**: 測試工具函數
  - 檔案操作工具
  - 日誌工廠
  - 配置管理
  - 批次處理

### 應用程式測試
- **`test_application.py`**: 測試主應用程式
  - 應用程式初始化
  - 命令行參數處理
  - 工作流程執行
  - 異常處理

## 🧪 測試類型

### 單元測試
測試個別函數和方法的功能：
```bash
pytest test/ -m "not integration" -v
```

### 整合測試
測試模組間的協作：
```bash
pytest test/ -m "integration" -v
```

### 性能測試
測試關鍵操作的性能：
```bash
pytest test/ -m "slow" -v
```

## 📝 編寫新測試

### 測試檔案命名規則
- 測試檔案：`test_<module_name>.py`
- 測試類別：`Test<ClassName>`
- 測試方法：`test_<function_name>`

### 使用 Fixtures
```python
def test_example(mock_logger, sample_params, temp_rgt_file):
    # 使用預定義的 fixtures
    pass
```

### 模擬外部依賴
```python
from unittest.mock import Mock, patch

@patch('module.external_function')
def test_with_mock(mock_external):
    mock_external.return_value = "mocked_result"
    # 測試邏輯
```

## 🔧 測試配置

### pytest.ini 設定
- 測試路徑：`test/`
- 測試檔案模式：`test_*.py`
- 輸出格式：詳細模式 (`-v`)
- 顏色輸出：啟用

### 常用 pytest 選項
```bash
# 詳細輸出
pytest -v

# 停在第一個失敗
pytest -x

# 顯示本地變數
pytest -l

# 並行執行
pytest -n auto

# 只運行失敗的測試
pytest --lf
```

## 📈 持續整合

### GitHub Actions 範例
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        pip install -r test/requirements.txt
    - name: Run tests
      run: python test/run_tests.py
```

## 🐛 故障排除

### 常見問題

1. **導入錯誤**
   ```
   ModuleNotFoundError: No module named 'source'
   ```
   確保從專案根目錄運行測試。

2. **Fixture 未找到**
   ```
   fixture 'mock_logger' not found
   ```
   檢查 `conftest.py` 是否正確載入。

3. **測試資料問題**
   使用 `tmp_path` fixture 創建臨時檔案。

### 除錯技巧
```python
# 在測試中使用 pdb
import pdb; pdb.set_trace()

# 使用 pytest 的除錯模式
pytest --pdb test/test_example.py
```

## 📚 參考資源

- [pytest 官方文檔](https://docs.pytest.org/)
- [unittest.mock 文檔](https://docs.python.org/3/library/unittest.mock.html)
- [pytest-cov 文檔](https://pytest-cov.readthedocs.io/)

---

**維護者**: DBI Transfer Tool 開發團隊  
**最後更新**: 2025/06/21
