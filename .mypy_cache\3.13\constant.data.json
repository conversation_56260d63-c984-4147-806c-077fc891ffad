{".class": "MypyFile", "_fullname": "constant", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CommandTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "constant.CommandTypes", "name": "CommandTypes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "constant.CommandTypes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "constant", "mro": ["constant.CommandTypes", "builtins.object"], "names": {".class": "SymbolTable", "CMD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.CommandTypes.CMD", "name": "CMD", "setter_type": null, "type": "builtins.str"}}, "COMMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.CommandTypes.COMMENT", "name": "COMMENT", "setter_type": null, "type": "builtins.str"}}, "DELAY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.CommandTypes.DELAY", "name": "DELAY", "setter_type": null, "type": "builtins.str"}}, "REGISTER_COMMANDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constant.CommandTypes.REGISTER_COMMANDS", "name": "REGISTER_COMMANDS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "RO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.CommandTypes.RO", "name": "RO", "setter_type": null, "type": "builtins.str"}}, "SIMPLE_COMMANDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constant.CommandTypes.SIMPLE_COMMANDS", "name": "SIMPLE_COMMANDS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "WO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.CommandTypes.WO", "name": "WO", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "constant.CommandTypes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "constant.CommandTypes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DataLength": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "constant.<PERSON><PERSON><PERSON>", "name": "DataLength", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "constant.<PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "constant", "mro": ["constant.<PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "BYTE_PAIR_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.DataLength.BYTE_PAIR_SIZE", "name": "BYTE_PAIR_SIZE", "setter_type": null, "type": "builtins.int"}}, "MAX_DATA_LENGTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.DataLength.MAX_DATA_LENGTH", "name": "MAX_DATA_LENGTH", "setter_type": null, "type": "builtins.int"}}, "VALID_LENGTHS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constant.DataLength.VALID_LENGTHS", "name": "VALID_LENGTHS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "constant.DataLength.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "constant.<PERSON><PERSON><PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Defaults": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "constant.<PERSON><PERSON><PERSON>s", "name": "De<PERSON>ults", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "constant.<PERSON><PERSON><PERSON>s", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "constant", "mro": ["constant.<PERSON><PERSON><PERSON>s", "builtins.object"], "names": {".class": "SymbolTable", "ALIGN_LEFT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.Defaults.ALIGN_LEFT", "name": "ALIGN_LEFT", "setter_type": null, "type": "builtins.str"}}, "LAST_1A": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.Defaults.LAST_1A", "name": "LAST_1A", "setter_type": null, "type": "builtins.str"}}, "LAST_1B": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.Defaults.LAST_1B", "name": "LAST_1B", "setter_type": null, "type": "builtins.str"}}, "PADDING_VALUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.Defaults.PADDING_VALUE", "name": "PADDING_VALUE", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "constant.De<PERSON><PERSON>s.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "constant.<PERSON><PERSON><PERSON>s", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FieldIndex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "constant.FieldIndex", "name": "FieldIndex", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "constant.FieldIndex", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "constant", "mro": ["constant.FieldIndex", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ADDR_IDX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.FieldIndex.ADDR_IDX", "name": "ADDR_IDX", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "COMMAND_IDX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.FieldIndex.COMMAND_IDX", "name": "COMMAND_IDX", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "COMMENT_IDX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.FieldIndex.COMMENT_IDX", "name": "COMMENT_IDX", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "DATA_IDX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.FieldIndex.DATA_IDX", "name": "DATA_IDX", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "FULL_DAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.FieldIndex.FULL_DAT", "name": "FULL_DAT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "constant.FieldIndex.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "constant.FieldIndex", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FilePaths": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "constant.FilePaths", "name": "FilePaths", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "constant.FilePaths", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "constant", "mro": ["constant.FilePaths", "builtins.object"], "names": {".class": "SymbolTable", "CASES_DIR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.FilePaths.CASES_DIR", "name": "CASES_DIR", "setter_type": null, "type": "builtins.str"}}, "INCLUDE_FILE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.FilePaths.INCLUDE_FILE", "name": "INCLUDE_FILE", "setter_type": null, "type": "builtins.str"}}, "LOG_DIR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.FilePaths.LOG_DIR", "name": "LOG_DIR", "setter_type": null, "type": "builtins.str"}}, "RESULTS_DIR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.FilePaths.RESULTS_DIR", "name": "RESULTS_DIR", "setter_type": null, "type": "builtins.str"}}, "RGT_DIR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.FilePaths.RGT_DIR", "name": "RGT_DIR", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "constant.FilePaths.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "constant.FilePaths", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IntEnum": {".class": "SymbolTableNode", "cross_ref": "enum.IntEnum", "kind": "Gdef"}, "LogLevels": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "constant.LogLevels", "name": "LogLevels", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "constant.LogLevels", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "constant", "mro": ["constant.LogLevels", "builtins.object"], "names": {".class": "SymbolTable", "ALL_LEVELS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constant.LogLevels.ALL_LEVELS", "name": "ALL_LEVELS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "DEBUG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.LogLevels.DEBUG", "name": "DEBUG", "setter_type": null, "type": "builtins.str"}}, "ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.LogLevels.ERROR", "name": "ERROR", "setter_type": null, "type": "builtins.str"}}, "INFO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.LogLevels.INFO", "name": "INFO", "setter_type": null, "type": "builtins.str"}}, "NOTE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.LogLevels.NOTE", "name": "NOTE", "setter_type": null, "type": "builtins.str"}}, "WARN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.LogLevels.WARN", "name": "WARN", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "constant.LogLevels.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "constant.LogLevels", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RegexPatterns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "constant.RegexPatterns", "name": "RegexPatterns", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "constant.RegexPatterns", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "constant", "mro": ["constant.RegexPatterns", "builtins.object"], "names": {".class": "SymbolTable", "COMMAND_PATTERN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.RegexPatterns.COMMAND_PATTERN", "name": "COMMAND_PATTERN", "setter_type": null, "type": "builtins.str"}}, "DELAY_PATTERN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.RegexPatterns.DELAY_PATTERN", "name": "DELAY_PATTERN", "setter_type": null, "type": "builtins.str"}}, "HEX_OR_XX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.RegexPatterns.HEX_OR_XX", "name": "HEX_OR_XX", "setter_type": null, "type": "builtins.str"}}, "REGISTER_PATTERN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constant.RegexPatterns.REGISTER_PATTERN", "name": "REGISTER_PATTERN", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "constant.RegexPatterns.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "constant.RegexPatterns", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constant.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constant.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constant.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constant.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constant.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constant.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\constant.py"}