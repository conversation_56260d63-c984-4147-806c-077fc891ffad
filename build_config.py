"""
DBI Transfer Tool 構建配置
"""
import os
from pathlib import Path

# 構建配置
BUILD_CONFIG = {
    # 基本設定
    "script_name": "source/application.py",
    "dist_name": "DBI_Transfer",
    "build_folder": "build",
    "dist_folder": "DBI_Transfer",
    
    # PyInstaller 選項
    "onefile": True,
    "console": True,
    "upx": True,
    "debug": False,
    "encoding": "utf-8",  # 設置編碼為 UTF-8
    
    # 包含的數據文件
    "add_data": [
        ("source/config/templates", "config/templates"),
        ("source/cases", "cases"),
        ("source/op2.exe", "."),
        ("source/para.txt", "."),
    ],
    
    # 隱藏導入
    "hidden_imports": [
        "config",
        "config.loader",
        "config.manager", 
        "config.schema",
        "config.tools",
        "config.validator",
        "core",
        "core.engine",
        "core.workflow",
        "parsers",
        "parsers.rgt_parser",
        "output",
        "output.output_manager",
        "utils",
        "utils.config",
        "utils.file_utils",
        "utils.logger",
        "validation",
        "validation.error_handler",
        "validation.version_validator",
        "logPrinter",
        "parameterManger",
        "exceptions",
        "constant",
        "type_definitions",
    ],
    
    # 排除的模組
    "excludes": [
        "test",
        "tests",
        "pytest",
        "unittest",
        "tkinter",
        "matplotlib",
        "numpy",
        "pandas",
        "scipy",
    ],
    
    # 版本信息
    "version_info": {
        "version": "1.0.0",
        "description": "DBI Transfer Tool - 數據傳輸工具",
        "company": "ChipOne Technology",
        "product": "DBI Transfer Tool",
        "copyright": "Copyright (C) 2024 ChipOne Technology",
    }
}

def get_pyinstaller_args():
    """生成 PyInstaller 命令行參數"""
    config = BUILD_CONFIG
    args = [
        "--name", config["dist_name"],
        "--distpath", config["dist_folder"],
        "--workpath", config["build_folder"],
    ]
    
    if config["onefile"]:
        args.append("--onefile")
    
    if config["console"]:
        args.append("--console")
    else:
        args.append("--windowed")
    
    if config["upx"]:
        args.append("--upx")
    
    if config["debug"]:
        args.append("--debug=all")
    
    # 添加數據文件
    for src, dst in config["add_data"]:
        if os.path.exists(src):
            args.extend(["--add-data", f"{src};{dst}"])
    
    # 添加隱藏導入
    for module in config["hidden_imports"]:
        args.extend(["--hidden-import", module])
    
    # 添加排除模組
    for module in config["excludes"]:
        args.extend(["--exclude-module", module])
    
    # 添加主腳本
    args.append(config["script_name"])
    
    return args

def validate_build_environment():
    """驗證構建環境"""
    errors = []
    
    # 檢查主腳本文件
    if not os.path.exists(BUILD_CONFIG["script_name"]):
        errors.append(f"主腳本文件不存在: {BUILD_CONFIG['script_name']}")
    
    # 檢查必要的數據文件
    for src, _ in BUILD_CONFIG["add_data"]:
        if not os.path.exists(src):
            errors.append(f"數據文件不存在: {src}")
    
    return errors

if __name__ == "__main__":
    # 測試配置
    print("DBI Transfer Tool 構建配置")
    print("=" * 40)
    print(f"主腳本: {BUILD_CONFIG['script_name']}")
    print(f"輸出名稱: {BUILD_CONFIG['dist_name']}")
    print(f"輸出目錄: {BUILD_CONFIG['dist_folder']}")
    print()
    
    # 驗證環境
    errors = validate_build_environment()
    if errors:
        print("❌ 構建環境檢查失敗:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✅ 構建環境檢查通過")
    
    print()
    print("PyInstaller 參數:")
    args = get_pyinstaller_args()
    for i, arg in enumerate(args):
        if i == 0:
            print(f"  pyinstaller {arg}")
        else:
            print(f"    {arg}")
