('D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\DBI_Transfer\\DBI_Transfer.exe',
 True,
 False,
 False,
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"*******" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\build\\DBI_Transfer\\DBI_Transfer.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\build\\DBI_Transfer\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\build\\DBI_Transfer\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\build\\DBI_Transfer\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\build\\DBI_Transfer\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\build\\DBI_Transfer\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\build\\DBI_Transfer\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('application',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\application.py',
   'PYSOURCE'),
  ('op2.exe',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\op2.exe',
   'BINARY'),
  ('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('cases\\ICNA3611\\POWER\\ICNA3611_test.c',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\cases\\ICNA3611\\POWER\\ICNA3611_test.c',
   'DATA'),
  ('cases\\ICNA3611\\POWER\\ICNA3611_test.txt',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\cases\\ICNA3611\\POWER\\ICNA3611_test.txt',
   'DATA'),
  ('cases\\example\\example.op2.txt',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\cases\\example\\example.op2.txt',
   'DATA'),
  ('cases\\otp\\otp_dmychk_v0.op2.txt',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\cases\\otp\\otp_dmychk_v0.op2.txt',
   'DATA'),
  ('cases\\stdflow\\open_ddi_p_mode.op2.txt',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\cases\\stdflow\\open_ddi_p_mode.op2.txt',
   'DATA'),
  ('cases\\stdflow\\pinbuild.op2.txt',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\cases\\stdflow\\pinbuild.op2.txt',
   'DATA'),
  ('cases\\stdflow\\reset.op2.txt',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\cases\\stdflow\\reset.op2.txt',
   'DATA'),
  ('config\\templates\\default.conf',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\config\\templates\\default.conf',
   'DATA'),
  ('config\\templates\\default.json',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\config\\templates\\default.json',
   'DATA'),
  ('para.txt',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\para.txt',
   'DATA'),
  ('base_library.zip',
   'D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\build\\DBI_Transfer\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1750539817,
 [('run.exe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll')
