"""
測試應用程式主入口
"""
import pytest
from unittest.mock import Mock, patch, MagicMock, call
import sys
from io import StringIO

from application import Application, main


class TestApplication:
    """測試應用程式類別"""
    
    def test_init_default(self):
        """測試預設初始化"""
        app = Application()
        assert app.config_file == "para.txt"
        assert app.logger is None
        assert app.workflow_manager is None
    
    def test_init_custom_config(self):
        """測試自定義配置檔案初始化"""
        app = Application("custom_para.txt")
        assert app.config_file == "custom_para.txt"
    
    @patch('application.LoggerFactory.create_logger')
    @patch('application.WorkflowManager')
    def test_setup(self, mock_workflow_class, mock_logger_factory, sample_params):
        """測試應用程式設置"""
        mock_logger = Mock()
        mock_workflow = Mock()
        mock_logger_factory.return_value = mock_logger
        mock_workflow_class.return_value = mock_workflow
        
        app = Application()
        app.setup(sample_params, "test_item")
        
        assert app.logger == mock_logger
        assert app.workflow_manager == mock_workflow
        
        mock_logger_factory.assert_called_once_with(sample_params, "TEST_PROJ_test_item.log")
        mock_workflow_class.assert_called_once_with(mock_logger)

        # 檢查日誌調用（現在有兩條日誌消息）
        expected_calls = [
            call("應用程式組件初始化完成", level="INFO"),
            call("專案: TEST_PROJ, 測試項目: test_item", level="INFO")
        ]
        mock_logger.log.assert_has_calls(expected_calls)
    
    @patch('application.ParameterManager')
    @patch('application.ConfigManager.parse_user_input')
    def test_run_with_user_input(self, mock_parse_input, mock_param_manager, sample_params, mock_logger):
        """測試使用使用者輸入運行"""
        mock_parse_input.return_value = "user_test_item"
        mock_param_manager.return_value.__enter__.return_value = sample_params
        mock_param_manager.return_value.__exit__.return_value = None

        app = Application()

        with patch.object(app, 'setup') as mock_setup, \
             patch.object(app, 'workflow_manager') as mock_workflow:

            # 設置 setup 的副作用，模擬真實的初始化過程
            def setup_side_effect(params, test_item):
                app._is_initialized = True
                app.logger = mock_logger
                app.workflow_manager = mock_workflow

            mock_setup.side_effect = setup_side_effect
            mock_workflow.execute_workflow = Mock()

            app.run()

            mock_parse_input.assert_called_once()
            mock_setup.assert_called_once_with(sample_params, "user_test_item")
            mock_workflow.execute_workflow.assert_called_once_with(sample_params, "user_test_item")
    
    @patch('application.ParameterManager')
    def test_run_with_provided_test_item(self, mock_param_manager, sample_params, mock_logger):
        """測試使用提供的測試項目運行"""
        mock_param_manager.return_value.__enter__.return_value = sample_params
        mock_param_manager.return_value.__exit__.return_value = None

        app = Application()

        with patch.object(app, 'setup') as mock_setup, \
             patch.object(app, 'workflow_manager') as mock_workflow:

            # 設置 setup 的副作用，模擬真實的初始化過程
            def setup_side_effect(params, test_item):
                app._is_initialized = True
                app.logger = mock_logger
                app.workflow_manager = mock_workflow

            mock_setup.side_effect = setup_side_effect
            mock_workflow.execute_workflow = Mock()

            app.run("provided_test_item")

            mock_setup.assert_called_once_with(sample_params, "provided_test_item")
            mock_workflow.execute_workflow.assert_called_once_with(sample_params, "provided_test_item")
    
    def test_run_with_keyboard_interrupt(self, mock_logger):
        """測試鍵盤中斷處理"""
        app = Application()
        app.logger = mock_logger
        
        with patch('application.ParameterManager') as mock_param_manager:
            mock_param_manager.return_value.__enter__.side_effect = KeyboardInterrupt()
            
            app.run("test_item")
            
            mock_logger.log.assert_called_with("使用者中斷執行", level="WARN")
    
    def test_run_with_exception(self, mock_logger):
        """測試異常處理"""
        app = Application()
        app.logger = mock_logger
        
        with patch('application.ParameterManager') as mock_param_manager:
            mock_param_manager.return_value.__enter__.side_effect = Exception("Test error")
            
            with pytest.raises(Exception):
                app.run("test_item")
            
            mock_logger.log.assert_called_with("應用程式執行錯誤：Test error", level="ERROR")
    
    def test_run_with_config(self):
        """測試使用指定配置檔案運行"""
        app = Application()
        
        with patch.object(app, 'run') as mock_run:
            app.run_with_config("custom_config.txt", "test_item")
            
            assert app.config_file == "custom_config.txt"
            mock_run.assert_called_once_with("test_item")
    
    def test_get_version(self):
        """測試獲取版本"""
        version = Application.get_version()
        assert version == "0.01"
    
    def test_get_info(self):
        """測試獲取應用程式資訊"""
        info = Application.get_info()
        
        assert info["name"] == "DBI Transfer Tool"
        assert info["version"] == "0.01"
        assert info["description"] is not None
        assert info["author"] == "Sychang"


class TestMainFunction:
    """測試主函數"""
    
    @patch('application.Application')
    def test_main_no_args(self, mock_app_class):
        """測試無參數執行"""
        mock_app = Mock()
        mock_app_class.return_value = mock_app
        
        with patch.object(sys, 'argv', ['application.py']):
            main()
            
            mock_app.run.assert_called_once_with()
    
    @patch('application.Application')
    def test_main_with_test_item(self, mock_app_class):
        """測試帶測試項目參數執行"""
        mock_app = Mock()
        mock_app_class.return_value = mock_app
        
        with patch.object(sys, 'argv', ['application.py', 'my_test_item']):
            main()
            
            mock_app.run.assert_called_once_with('my_test_item')
    
    @patch('builtins.print')
    def test_main_help(self, mock_print):
        """測試幫助參數"""
        with patch.object(sys, 'argv', ['application.py', '--help']):
            main()
            
            mock_print.assert_called()
            # 檢查是否印出了幫助資訊
            print_calls = []
            for call in mock_print.call_args_list:
                if call[0]:  # 確保有位置參數
                    print_calls.append(call[0][0])
            assert any("DBI Transfer Tool" in call for call in print_calls)
    
    @patch('builtins.print')
    @patch('application.Application.get_info')
    def test_main_version(self, mock_get_info, mock_print):
        """測試版本參數"""
        mock_get_info.return_value = {
            "name": "DBI Transfer Tool",
            "version": "0.01",
            "author": "Sychang"
        }
        
        with patch.object(sys, 'argv', ['application.py', '--version']):
            main()
            
            mock_print.assert_called()
            print_calls = []
            for call in mock_print.call_args_list:
                if call[0]:  # 確保有位置參數
                    print_calls.append(call[0][0])
            assert any("DBI Transfer Tool v0.01" in call for call in print_calls)
            assert any("作者: Sychang" in call for call in print_calls)


class TestApplicationIntegration:
    """測試應用程式整合"""
    
    @patch('application.ParameterManager')
    @patch('application.LoggerFactory.create_logger')
    @patch('application.WorkflowManager')
    def test_complete_workflow(self, mock_workflow_class, mock_logger_factory, mock_param_manager, sample_params):
        """測試完整工作流程"""
        # 設置模擬
        mock_logger = Mock()
        mock_workflow = Mock()
        mock_logger_factory.return_value = mock_logger
        mock_workflow_class.return_value = mock_workflow
        mock_param_manager.return_value.__enter__.return_value = sample_params
        mock_param_manager.return_value.__exit__.return_value = None
        
        # 執行應用程式
        app = Application("test_config.txt")
        app.run("integration_test")
        
        # 驗證調用順序和參數
        mock_param_manager.assert_called_once_with("test_config.txt")
        mock_logger_factory.assert_called_once_with(sample_params, "TEST_PROJ_integration_test.log")
        mock_workflow_class.assert_called_once_with(mock_logger)
        mock_workflow.execute_workflow.assert_called_once_with(sample_params, "integration_test")
        
        # 驗證日誌記錄
        expected_log_calls = [
            ("應用程式組件初始化完成", "INFO"),
            ("專案: TEST_PROJ, 測試項目: integration_test", "INFO"),
            ("應用程式執行完成", "INFO")
        ]
        
        actual_log_calls = []
        for call in mock_logger.log.call_args_list:
            if call[0] and call[1]:  # 確保有位置參數和關鍵字參數
                actual_log_calls.append((call[0][0], call[1]["level"]))
        for expected_call in expected_log_calls:
            assert expected_call in actual_log_calls
