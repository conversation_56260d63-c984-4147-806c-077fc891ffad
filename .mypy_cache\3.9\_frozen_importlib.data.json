{".class": "MypyFile", "_fullname": "_frozen_importlib", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BuiltinImporter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["importlib.abc.MetaPathFinder", "importlib.abc.InspectLoader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_frozen_importlib.BuiltinImporter", "name": "BuiltinImporter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_frozen_importlib.BuiltinImporter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "_frozen_importlib", "mro": ["_frozen_importlib.BuiltinImporter", "importlib.abc.MetaPathFinder", "importlib.abc.Finder", "importlib.abc.InspectLoader", "importlib.abc.Loader", "builtins.object"], "names": {".class": "SymbolTable", "create_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "spec"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.BuiltinImporter.create_module", "name": "create_module", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "spec"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "_frozen_importlib.ModuleSpec"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_module of BuiltinImporter", "ret_type": {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.BuiltinImporter.create_module", "name": "create_module", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "spec"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "_frozen_importlib.ModuleSpec"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_module of BuiltinImporter", "ret_type": {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "exec_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.BuiltinImporter.exec_module", "name": "exec_module", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "module"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "types.ModuleType"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "exec_module of BuiltinImporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.BuiltinImporter.exec_module", "name": "exec_module", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "module"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "types.ModuleType"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "exec_module of BuiltinImporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "find_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "fullname", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.BuiltinImporter.find_module", "name": "find_module", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "fullname", "path"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_module of BuiltinImporter", "ret_type": {".class": "UnionType", "items": ["importlib.abc.Loader", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.BuiltinImporter.find_module", "name": "find_module", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "fullname", "path"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_module of BuiltinImporter", "ret_type": {".class": "UnionType", "items": ["importlib.abc.Loader", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "find_spec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "fullname", "path", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.BuiltinImporter.find_spec", "name": "find_spec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "fullname", "path", "target"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_spec of BuiltinImporter", "ret_type": {".class": "UnionType", "items": ["_frozen_importlib.ModuleSpec", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.BuiltinImporter.find_spec", "name": "find_spec", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "fullname", "path", "target"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_spec of BuiltinImporter", "ret_type": {".class": "UnionType", "items": ["_frozen_importlib.ModuleSpec", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.BuiltinImporter.get_code", "name": "get_code", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_code of BuiltinImporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.BuiltinImporter.get_code", "name": "get_code", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_code of BuiltinImporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.BuiltinImporter.get_source", "name": "get_source", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_source of BuiltinImporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.BuiltinImporter.get_source", "name": "get_source", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_source of BuiltinImporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_package": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.BuiltinImporter.is_package", "name": "is_package", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_package of BuiltinImporter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.BuiltinImporter.is_package", "name": "is_package", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_package of BuiltinImporter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "load_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.BuiltinImporter.load_module", "name": "load_module", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load_module of BuiltinImporter", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.BuiltinImporter.load_module", "name": "load_module", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.BuiltinImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load_module of BuiltinImporter", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "module_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "_frozen_importlib.BuiltinImporter.module_repr", "name": "module_repr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["types.ModuleType"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "module_repr of BuiltinImporter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.BuiltinImporter.module_repr", "name": "module_repr", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["types.ModuleType"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "module_repr of BuiltinImporter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FrozenImporter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["importlib.abc.MetaPathFinder", "importlib.abc.InspectLoader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_frozen_importlib.FrozenImporter", "name": "FrozenImporter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_frozen_importlib.FrozenImporter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "_frozen_importlib", "mro": ["_frozen_importlib.FrozenImporter", "importlib.abc.MetaPathFinder", "importlib.abc.Finder", "importlib.abc.InspectLoader", "importlib.abc.Loader", "builtins.object"], "names": {".class": "SymbolTable", "create_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "spec"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.FrozenImporter.create_module", "name": "create_module", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "spec"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "_frozen_importlib.ModuleSpec"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_module of FrozenImporter", "ret_type": {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.FrozenImporter.create_module", "name": "create_module", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "spec"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "_frozen_importlib.ModuleSpec"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_module of FrozenImporter", "ret_type": {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "exec_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "_frozen_importlib.FrozenImporter.exec_module", "name": "exec_module", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["types.ModuleType"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "exec_module of FrozenImporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.FrozenImporter.exec_module", "name": "exec_module", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["types.ModuleType"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "exec_module of FrozenImporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "find_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "fullname", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.FrozenImporter.find_module", "name": "find_module", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "fullname", "path"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_module of FrozenImporter", "ret_type": {".class": "UnionType", "items": ["importlib.abc.Loader", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.FrozenImporter.find_module", "name": "find_module", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "fullname", "path"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_module of FrozenImporter", "ret_type": {".class": "UnionType", "items": ["importlib.abc.Loader", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "find_spec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "fullname", "path", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.FrozenImporter.find_spec", "name": "find_spec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "fullname", "path", "target"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_spec of FrozenImporter", "ret_type": {".class": "UnionType", "items": ["_frozen_importlib.ModuleSpec", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.FrozenImporter.find_spec", "name": "find_spec", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "fullname", "path", "target"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_spec of FrozenImporter", "ret_type": {".class": "UnionType", "items": ["_frozen_importlib.ModuleSpec", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.FrozenImporter.get_code", "name": "get_code", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_code of FrozenImporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.FrozenImporter.get_code", "name": "get_code", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_code of FrozenImporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.FrozenImporter.get_source", "name": "get_source", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_source of FrozenImporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.FrozenImporter.get_source", "name": "get_source", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_source of FrozenImporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_package": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.FrozenImporter.is_package", "name": "is_package", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_package of FrozenImporter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.FrozenImporter.is_package", "name": "is_package", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_package of FrozenImporter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "load_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.FrozenImporter.load_module", "name": "load_module", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load_module of FrozenImporter", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.FrozenImporter.load_module", "name": "load_module", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "fullname"], "arg_types": [{".class": "TypeType", "item": "_frozen_importlib.FrozenImporter"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load_module of FrozenImporter", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "module_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["m"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "_frozen_importlib.FrozenImporter.module_repr", "name": "module_repr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["m"], "arg_types": ["types.ModuleType"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "module_repr of FrozenImporter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.FrozenImporter.module_repr", "name": "module_repr", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["m"], "arg_types": ["types.ModuleType"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "module_repr of FrozenImporter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LoaderProtocol": {".class": "SymbolTableNode", "cross_ref": "_typeshed.importlib.LoaderProtocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModuleSpec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_frozen_importlib.ModuleSpec", "name": "ModuleSpec", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_frozen_importlib.ModuleSpec", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_frozen_importlib", "mro": ["_frozen_importlib.ModuleSpec", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_frozen_importlib.ModuleSpec.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_frozen_importlib.ModuleSpec", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of ModuleSpec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "_frozen_importlib.ModuleSpec.__hash__", "name": "__hash__", "setter_type": null, "type": {".class": "NoneType"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "name", "loader", "origin", "loader_state", "is_package"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_frozen_importlib.ModuleSpec.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "name", "loader", "origin", "loader_state", "is_package"], "arg_types": ["_frozen_importlib.ModuleSpec", "builtins.str", {".class": "UnionType", "items": ["importlib.abc.Loader", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ModuleSpec", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cached": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_frozen_importlib.ModuleSpec.cached", "name": "cached", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "has_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_frozen_importlib.ModuleSpec.has_location", "name": "has_location", "setter_type": null, "type": "builtins.bool"}}, "loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_frozen_importlib.ModuleSpec.loader", "name": "loader", "setter_type": null, "type": {".class": "UnionType", "items": ["importlib.abc.Loader", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "loader_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_frozen_importlib.ModuleSpec.loader_state", "name": "loader_state", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_frozen_importlib.ModuleSpec.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "origin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_frozen_importlib.ModuleSpec.origin", "name": "origin", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "_frozen_importlib.ModuleSpec.parent", "name": "parent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_frozen_importlib.ModuleSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parent of ModuleSpec", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_frozen_importlib.ModuleSpec.parent", "name": "parent", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_frozen_importlib.ModuleSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parent of ModuleSpec", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "submodule_search_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_frozen_importlib.ModuleSpec.submodule_search_locations", "name": "submodule_search_locations", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_frozen_importlib.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_frozen_importlib.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_frozen_importlib.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__import__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["name", "globals", "locals", "fromlist", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_frozen_importlib.__import__", "name": "__import__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["name", "globals", "locals", "fromlist", "level"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__import__", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_frozen_importlib.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_frozen_importlib.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_frozen_importlib.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_init_module_attrs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["spec", "module", "override"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_frozen_importlib._init_module_attrs", "name": "_init_module_attrs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["spec", "module", "override"], "arg_types": ["_frozen_importlib.ModuleSpec", "types.ModuleType", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_init_module_attrs", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef", "module_hidden": true, "module_public": false}, "module_from_spec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["spec"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_frozen_importlib.module_from_spec", "name": "module_from_spec", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["spec"], "arg_types": ["_frozen_importlib.ModuleSpec"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "module_from_spec", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "spec_from_loader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["name", "loader", "origin", "is_package"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_frozen_importlib.spec_from_loader", "name": "spec_from_loader", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["name", "loader", "origin", "is_package"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["_typeshed.importlib.LoaderProtocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "spec_from_loader", "ret_type": {".class": "UnionType", "items": ["_frozen_importlib.ModuleSpec", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\_frozen_importlib.pyi"}