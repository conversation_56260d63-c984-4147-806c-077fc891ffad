["test_application.py::TestApplication::test_get_info", "test_application.py::TestApplication::test_get_version", "test_application.py::TestApplication::test_init_custom_config", "test_application.py::TestApplication::test_init_default", "test_application.py::TestApplication::test_run_with_config", "test_application.py::TestApplication::test_run_with_exception", "test_application.py::TestApplication::test_run_with_keyboard_interrupt", "test_application.py::TestApplication::test_run_with_provided_test_item", "test_application.py::TestApplication::test_run_with_user_input", "test_application.py::TestApplication::test_setup", "test_application.py::TestApplicationIntegration::test_complete_workflow", "test_application.py::TestMainFunction::test_main_help", "test_application.py::TestMainFunction::test_main_no_args", "test_application.py::TestMainFunction::test_main_version", "test_application.py::TestMainFunction::test_main_with_test_item", "test_config_system.py::TestConfigIntegration::test_full_workflow", "test_config_system.py::TestConfigLoader::test_load_from_env", "test_config_system.py::TestConfigLoader::test_load_json_format", "test_config_system.py::TestConfigLoader::test_load_layered_config", "test_config_system.py::TestConfigLoader::test_load_simple_format", "test_config_system.py::TestConfigManager::test_attribute_access", "test_config_system.py::TestConfigManager::test_callback_exception_handling", "test_config_system.py::TestConfigManager::test_change_callbacks", "test_config_system.py::TestConfigManager::test_config_manager_creation", "test_config_system.py::TestConfigManager::test_context_manager", "test_config_system.py::TestConfigManager::test_get_config_summary", "test_config_system.py::TestConfigManager::test_get_validation_report", "test_config_system.py::TestConfigManager::test_load_config", "test_config_system.py::TestConfigManager::test_remove_change_callback", "test_config_system.py::TestConfigManager::test_reset_to_defaults", "test_config_system.py::TestConfigManager::test_save_config_with_loaded_file", "test_config_system.py::TestConfigManager::test_save_config_with_path", "test_config_system.py::TestConfigManager::test_save_config_without_path_error", "test_config_system.py::TestConfigManager::test_set_and_get", "test_config_system.py::TestConfigManager::test_temporary_config", "test_config_system.py::TestConfigManager::test_update_config", "test_config_system.py::TestConfigManager::test_update_with_validation_error", "test_config_system.py::TestConfigManagerUtilities::test_detect_project_from_config", "test_config_system.py::TestConfigManagerUtilities::test_detect_project_from_config_error", "test_config_system.py::TestConfigManagerUtilities::test_find_config_file_legacy", "test_config_system.py::TestConfigManagerUtilities::test_find_config_file_modern", "test_config_system.py::TestConfigManagerUtilities::test_find_config_file_none", "test_config_system.py::TestConfigManagerUtilities::test_find_config_file_project_specific", "test_config_system.py::TestConfigSchema::test_get_defaults", "test_config_system.py::TestConfigSchema::test_schema_initialization", "test_config_system.py::TestConfigSchema::test_validate_config", "test_config_system.py::TestConfigTools::test_compare_configs_different", "test_config_system.py::TestConfigTools::test_compare_configs_file_not_exists", "test_config_system.py::TestConfigTools::test_compare_configs_identical", "test_config_system.py::TestConfigTools::test_generate_config", "test_config_system.py::TestConfigTools::test_generate_config_ini_format", "test_config_system.py::TestConfigTools::test_generate_config_json_format", "test_config_system.py::TestConfigTools::test_generate_config_simple_format", "test_config_system.py::TestConfigTools::test_generate_config_with_template_not_exists", "test_config_system.py::TestConfigTools::test_migrate_config_file_not_exists", "test_config_system.py::TestConfigTools::test_migrate_config_with_backup", "test_config_system.py::TestConfigTools::test_migrate_config_without_backup", "test_config_system.py::TestConfigTools::test_show_schema", "test_config_system.py::TestConfigTools::test_validate_config_tool", "test_config_system.py::TestConfigTools::test_validate_config_tool_file_not_exists", "test_config_system.py::TestConfigTools::test_validate_config_tool_invalid", "test_config_system.py::TestConfigTools::test_validate_config_tool_strict_mode", "test_config_system.py::TestConfigTools::test_validate_config_tool_valid", "test_config_system.py::TestConfigToolsCommandLine::test_main_function_generate_command", "test_config_system.py::TestConfigToolsCommandLine::test_main_function_keyboard_interrupt", "test_config_system.py::TestConfigToolsCommandLine::test_main_function_no_command", "test_config_system.py::TestConfigToolsCommandLine::test_main_function_schema_command", "test_config_system.py::TestConfigToolsErrorHandling::test_compare_configs_load_error", "test_config_system.py::TestConfigToolsErrorHandling::test_generate_config_save_error", "test_config_system.py::TestConfigToolsErrorHandling::test_migrate_config_load_error", "test_config_system.py::TestConfigToolsErrorHandling::test_validate_config_load_error", "test_config_system.py::TestConfigToolsUtilities::test_quick_generate_config", "test_config_system.py::TestConfigToolsUtilities::test_quick_validate_invalid_config", "test_config_system.py::TestConfigToolsUtilities::test_quick_validate_valid_config", "test_config_system.py::TestConfigValidator::test_invalid_output_width", "test_config_system.py::TestConfigValidator::test_invalid_product_id", "test_config_system.py::TestConfigValidator::test_invalid_project_name", "test_config_system.py::TestConfigValidator::test_valid_config", "test_config_system.py::TestConfigValidator::test_valid_product_id", "test_config_system.py::TestConfigValidator::test_validation_report", "test_config_system.py::TestCreateConfigManager::test_create_with_file", "test_config_system.py::TestCreateConfigManager::test_create_without_file", "test_core_engine.py::TestDbiTransferEngine::test_init", "test_core_engine.py::TestDbiTransferEngine::test_process_data_groups_basic", "test_core_engine.py::TestDbiTransferEngine::test_process_data_groups_with_tags", "test_core_engine.py::TestDbiTransferEngine::test_reset_state", "test_core_engine.py::TestDbiTransferEngine::test_split_data_into_byte_groups_basic", "test_core_engine.py::TestDbiTransferEngine::test_split_data_into_byte_groups_with_tags", "test_core_engine.py::TestDbiTransferEngine::test_write_c_register_operation_reg16", "test_core_engine.py::TestDbiTransferEngine::test_write_c_register_operation_reg32", "test_core_engine.py::TestDbiTransferEngine::test_write_c_register_operation_reg8", "test_core_engine.py::TestDbiTransferEngine::test_write_register_with_optimization_different_value", "test_core_engine.py::TestDbiTransferEngine::test_write_register_with_optimization_same_value", "test_core_engine.py::TestDbiTransferEngine::test_write_simple_command", "test_core_engine.py::TestDbiTransferEngine::test_write_tv_c_reg16", "test_core_engine.py::TestDbiTransferEngine::test_write_tv_c_reg32", "test_core_engine.py::TestDbiTransferEngine::test_write_tv_c_reg8", "test_core_engine.py::TestDbiTransferEngine::test_write_with_comment_different_value", "test_core_engine.py::TestDbiTransferEngine::test_write_with_comment_same_value", "test_core_engine.py::TestExtractLabelValue::test_extract_label_value_empty_string", "test_core_engine.py::TestExtractLabelValue::test_extract_label_value_none", "test_core_engine.py::TestExtractLabelValue::test_extract_label_value_not_found", "test_core_engine.py::TestExtractLabelValue::test_extract_label_value_success", "test_core_engine.py::TestGetLabelValue::test_get_label_value_empty_string", "test_core_engine.py::TestGetLabelValue::test_get_label_value_none", "test_core_engine.py::TestGetLabelValue::test_get_label_value_not_found", "test_core_engine.py::TestGetLabelValue::test_get_label_value_success", "test_error_handling.py::TestCustomExceptions::test_command_not_supported_error", "test_error_handling.py::TestCustomExceptions::test_dbi_transfer_error_basic", "test_error_handling.py::TestCustomExceptions::test_dbi_transfer_error_with_context", "test_error_handling.py::TestCustomExceptions::test_error_to_dict", "test_error_handling.py::TestCustomExceptions::test_parse_error_with_line_info", "test_error_handling.py::TestCustomExceptions::test_validation_error", "test_error_handling.py::TestErrorCreation::test_create_from_file_not_found", "test_error_handling.py::TestErrorCreation::test_create_from_generic_exception", "test_error_handling.py::TestErrorCreation::test_create_from_permission_error", "test_error_handling.py::TestErrorCreation::test_create_from_value_error", "test_error_handling.py::TestErrorHandler::test_clear_history", "test_error_handling.py::TestErrorHandler::test_command_not_supported", "test_error_handling.py::TestErrorHandler::test_create_error_handler", "test_error_handling.py::TestErrorHandler::test_error_summary", "test_error_handling.py::TestErrorHandler::test_handle_error_with_context_fatal", "test_error_handling.py::TestErrorHandler::test_handle_error_with_context_non_fatal", "test_error_handling.py::TestErrorHandler::test_init", "test_error_handling.py::TestErrorHandlerIntegration::test_error_context_preservation", "test_error_handling.py::TestErrorHandlerIntegration::test_multiple_error_types", "test_log_printer.py::TestLogPrinter::test_colorize_functionality", "test_log_printer.py::TestLogPrinter::test_disable_console", "test_log_printer.py::TestLogPrinter::test_disable_file", "test_log_printer.py::TestLogPrinter::test_disable_file_log", "test_log_printer.py::TestLogPrinter::test_empty_message", "test_log_printer.py::TestLogPrinter::test_enable_disable_levels", "test_log_printer.py::TestLogPrinter::test_enable_file_log", "test_log_printer.py::TestLogPrinter::test_enter_leave_methods", "test_log_printer.py::TestLogPrinter::test_file_write_error", "test_log_printer.py::TestLogPrinter::test_init_default", "test_log_printer.py::TestLogPrinter::test_init_with_parameters", "test_log_printer.py::TestLogPrinter::test_log_different_levels", "test_log_printer.py::TestLogPrinter::test_log_file_append", "test_log_printer.py::TestLogPrinter::test_log_file_creation", "test_log_printer.py::TestLogPrinter::test_log_formatting", "test_log_printer.py::TestLogPrinter::test_log_level_filtering", "test_log_printer.py::TestLogPrinter::test_log_to_console", "test_log_printer.py::TestLogPrinter::test_log_to_file", "test_log_printer.py::TestLogPrinter::test_log_with_indent", "test_log_printer.py::TestLogPrinter::test_log_with_section", "test_log_printer.py::TestLogPrinter::test_nested_sections", "test_log_printer.py::TestLogPrinter::test_none_message", "test_log_printer.py::TestLogPrinter::test_section_context_manager", "test_log_printer.py::TestLogPrinter::test_section_indent_level", "test_log_printer.py::TestLogPrinter::test_section_stack", "test_log_printer.py::TestLogPrinter::test_step_method", "test_log_printer.py::TestLogPrinterIntegration::test_real_world_usage", "test_output.py::TestOutputManager::test_build_title", "test_output.py::TestOutputManager::test_get_front_text_basic", "test_output.py::TestOutputManager::test_get_front_text_custom_delimiter", "test_output.py::TestOutputManager::test_get_output_folder", "test_output.py::TestOutputManager::test_init", "test_output.py::TestOutputManager::test_prepare_filenames", "test_output.py::TestOutputManager::test_write_output_files", "test_output.py::TestOutputManager::test_write_title_section", "test_output.py::TestOutputManagerIntegration::test_complete_workflow", "test_parameter_manager.py::TestParameterManager::test_context_manager", "test_parameter_manager.py::TestParameterManager::test_empty_values", "test_parameter_manager.py::TestParameterManager::test_getattr_existing_param", "test_parameter_manager.py::TestParameterManager::test_getattr_nonexistent_param", "test_parameter_manager.py::TestParameterManager::test_init_and_load_params", "test_parameter_manager.py::TestParameterManager::test_load_params_empty_file", "test_parameter_manager.py::TestParameterManager::test_load_params_file_not_found", "test_parameter_manager.py::TestParameterManager::test_load_params_skip_invalid_lines", "test_parameter_manager.py::TestParameterManager::test_load_params_with_multiple_equals", "test_parameter_manager.py::TestParameterManager::test_load_params_with_spaces", "test_parameter_manager.py::TestParameterManager::test_overwrite_params", "test_parameter_manager.py::TestParameterManager::test_params_dict_access", "test_parameter_manager.py::TestParameterManager::test_unicode_support", "test_parameter_manager.py::TestParameterManagerIntegration::test_real_world_config_file", "test_parsers.py::TestRgtParser::test_get_label_value", "test_parsers.py::TestRgtParser::test_init", "test_parsers.py::TestRgtParser::test_is_valid_data_pattern_invalid", "test_parsers.py::TestRgtParser::test_is_valid_data_pattern_valid", "test_parsers.py::TestRgtParser::test_load_file_content_success", "test_parsers.py::TestRgtParser::test_parse_comment_line", "test_parsers.py::TestRgtParser::test_parse_delay_line_valid", "test_parsers.py::TestRgtParser::test_parse_delay_line_with_decimal", "test_parsers.py::TestRgtParser::test_parse_file_complete", "test_parsers.py::TestRgtParser::test_parse_register_command_6_digit_addr", "test_parsers.py::TestRgtParser::test_parse_register_command_ro", "test_parsers.py::TestRgtParser::test_parse_register_command_wo", "test_type_annotations.py::TestMypyIntegration::test_mypy_check_core_engine", "test_type_annotations.py::TestMypyIntegration::test_mypy_check_types_module", "test_type_annotations.py::TestProtocolCompliance::test_loggable_protocol", "test_type_annotations.py::TestProtocolCompliance::test_parsable_protocol", "test_type_annotations.py::TestTypeCompatibility::test_generic_types", "test_type_annotations.py::TestTypeCompatibility::test_optional_types", "test_type_annotations.py::TestTypeCompatibility::test_union_types", "test_type_annotations.py::TestTypeDefinitions::test_basic_type_aliases", "test_type_annotations.py::TestTypeDefinitions::test_data_types", "test_type_annotations.py::TestTypeDefinitions::test_parsing_types", "test_type_annotations.py::TestTypeDefinitions::test_typed_dict_structures", "test_type_annotations.py::TestTypeHints::test_class_annotations", "test_type_annotations.py::TestTypeHints::test_function_annotations", "test_type_annotations.py::TestTypeValidation::test_runtime_type_checking", "test_type_annotations.py::TestTypeValidation::test_type_narrowing", "test_utils.py::TestConfigManager::test_copy_result_files", "test_utils.py::TestConfigManager::test_parse_user_input", "test_utils.py::TestFileUtils::test_copy_and_rename_files_basic", "test_utils.py::TestFileUtils::test_copy_and_rename_files_skip_directories", "test_utils.py::TestFileUtils::test_copy_and_rename_files_with_index", "test_utils.py::TestFileUtils::test_execute_batch_file", "test_utils.py::TestFileUtils::test_update_batch_file", "test_utils.py::TestLoggerFactory::test_create_logger", "test_utils.py::TestLoggerFactory::test_create_logger_all_disabled", "test_utils.py::TestUtilsIntegration::test_file_operations_workflow", "test_validation.py::TestConfigStructure::test_validate_format_invalid_log_level", "test_validation.py::TestConfigStructure::test_validate_format_invalid_width", "test_validation.py::TestConfigStructure::test_validate_format_valid", "test_validation.py::TestConfigStructure::test_validate_structure_complete", "test_validation.py::TestConfigStructure::test_validate_structure_missing_critical", "test_validation.py::TestErrorHandler::test_create_error_handler", "test_validation.py::TestErrorHandler::test_handle_command_not_supported", "test_validation.py::TestErrorHandler::test_init_with_logger", "test_validation.py::TestErrorHandler::test_init_without_logger", "test_validation.py::TestSemanticVersion::test_compatibility_different_major", "test_validation.py::TestSemanticVersion::test_compatibility_higher_minor", "test_validation.py::TestSemanticVersion::test_compatibility_same_major", "test_validation.py::TestSemanticVersion::test_parse_full_version", "test_validation.py::TestSemanticVersion::test_parse_invalid_version", "test_validation.py::TestSemanticVersion::test_parse_short_version", "test_validation.py::TestVersionValidator::test_create_version_validator", "test_validation.py::TestVersionValidator::test_get_compatibility_info", "test_validation.py::TestVersionValidator::test_init_custom_version", "test_validation.py::TestVersionValidator::test_init_default_version", "test_validation.py::TestVersionValidator::test_validate_compatibility_success", "test_validation.py::TestVersionValidator::test_validate_compatibility_unsupported_version", "test_workflow.py::TestWorkflowIntegration::test_workflow_error_handling", "test_workflow.py::TestWorkflowIntegration::test_workflow_with_real_logger", "test_workflow.py::TestWorkflowManager::test_execute_workflow_success", "test_workflow.py::TestWorkflowManager::test_execute_workflow_validation_failure", "test_workflow.py::TestWorkflowManager::test_generate_output", "test_workflow.py::TestWorkflowManager::test_init", "test_workflow.py::TestWorkflowManager::test_process_rgt_file", "test_workflow.py::TestWorkflowManager::test_run_batch_and_copy", "test_workflow.py::TestWorkflowManager::test_validate_configuration_failure", "test_workflow.py::TestWorkflowManager::test_validate_configuration_success"]