{"data_mtime": 1750671797, "dep_lines": [18, 4, 1, 1, 1, 1], "dep_prios": [20, 5, 5, 30, 30, 30], "dependencies": ["utils.file_utils", "typing", "builtins", "_frozen_importlib", "abc", "logPrinter"], "hash": "c26d3aa773fab91a33eeaa15a404a72580291a88", "id": "utils.config", "ignore_all": false, "interface_hash": "9272c45eaf7a35244699934807eec7d7723eb8a5", "mtime": 1750671497, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "D:\\文件\\工作文件\\chipone\\ICNT3611\\16.CP\\dbi_transfer_tool\\source\\utils\\config.py", "plugin_data": null, "size": 531, "suppressed": [], "version_id": "1.16.1"}