task open_ddi_p_mode;
begin


    $display("This configue to change TP_boot_state to DDI_PRGM");
    ext_dbi_write(8'd03, 8'hFD, {8'h5A, 8'h5A});
    ext_dbi_write(8'd02, 8'h9F, {8'h02});
    ext_dbi_write(8'd02, 8'h9B, {8'h11});
    ext_dbi_write(8'd02, 8'hB8, {8'h5A});
    ext_dbi_write(8'd02, 8'h9B, {8'h00});
    ext_dbi_write(8'd02, 8'h17, {8'h01});
    $display("read back");
    ext_dbi_read_and_compare(8'h17, 8'd02, "Read 1 byte from GR00.17 and chek", 
                                           '{8'h01});
    #2_000_000ns;
    $display("finish");
    ext_dbi_write(8'd02, 8'h1A, {8'h03});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h00, 8'h00});
    ext_dbi_write(8'd02, 8'h1E, {8'h00});
    ext_dbi_read_and_compare(8'h1F, 8'd05, "Read 4 bytes from UCS.1F and check the byte [4]", 
                                           '{8'hxx, 8'hxx, 8'hxx, 8'h68});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h47, 8'h00, 8'h00});
    ext_dbi_write(8'd02, 8'h1E, {8'h00});
    ext_dbi_read_and_compare(8'h1F, 8'd05, "Read 4 bytes from UCS.1F and check the byte [4]", 
                                           '{8'hxx, 8'hxx, 8'hxx, 8'h68});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h60, 8'h00});
    ext_dbi_write(8'd02, 8'h1E, {8'h00});
    ext_dbi_read_and_compare(8'h1F, 8'd05, "Read 4 bytes from UCS.1F and check the byte [4]", 
                                           '{8'hxx, 8'hxx, 8'hxx, 8'h12});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h60, 8'h04});
    ext_dbi_write(8'd02, 8'h1E, {8'h00});
    ext_dbi_read_and_compare(8'h1F, 8'd05, "Read 4 bytes from UCS.1F and check the byte [3, 4]", 
                                           '{8'hxx, 8'hxx, 8'h12, 8'h34});
    ext_dbi_write(8'd02, 8'h1A, {8'h02});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h60, 8'h12});
    ext_dbi_write(8'd02, 8'h1E, {8'h00});
    ext_dbi_read_and_compare(8'h1F, 8'd04, "Read 3 bytes from UCS.1F and check the byte [1, 2, 3]", 
                                           '{8'h12, 8'h34, 8'h56});
    ext_dbi_write(8'd02, 8'h1A, {8'h03});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h04, 8'h75, 8'h04});
    ext_dbi_write(8'd02, 8'h1E, {8'h00});
    ext_dbi_read_and_compare(8'h1F, 8'd05, "Read 4 bytes from UCS.1F and check the byte [1]", 
                                           '{8'h12, 8'hxx, 8'hxx, 8'hxx});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h60, 8'h00});
    ext_dbi_write(8'd02, 8'h1E, {8'h00});
    ext_dbi_read_and_compare(8'h1F, 8'd05, "Read 4 bytes from UCS.1F and check the byte [3]", 
                                           '{8'hxx, 8'hxx, 8'h12, 8'hxx});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h04, 8'h75, 8'h04});
    ext_dbi_write(8'd02, 8'h1E, {8'h00});
    ext_dbi_read_and_compare(8'h1F, 8'd05, "Read 4 bytes from UCS.1F and check the byte [2, 4]", 
                                           '{8'hxx, 8'h12, 8'hxx, 8'h34});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h60, 8'h12});
    ext_dbi_write(8'd02, 8'h1E, {8'h00});
    ext_dbi_read_and_compare(8'h1F, 8'd05, "Read 4 bytes from UCS.1F and check the byte [2, 4]", 
                                           '{8'hxx, 8'h34, 8'hxx, 8'h56});
    ext_dbi_write(8'd02, 8'h1A, {8'h00});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h00, 8'h00});
    ext_dbi_write(8'd02, 8'h1D, {8'h68});
    ext_dbi_write(8'd02, 8'h1C, {8'h00});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h47, 8'h00, 8'h00});
    ext_dbi_write(8'd02, 8'h1D, {8'h68});
    ext_dbi_write(8'd02, 8'h1C, {8'h00});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h60, 8'h00});
    ext_dbi_write(8'd02, 8'h1D, {8'h12});
    ext_dbi_write(8'd02, 8'h1C, {8'h00});
    ext_dbi_write(8'd02, 8'h1D, {8'h13});
    ext_dbi_write(8'd02, 8'h1C, {8'h00});
    ext_dbi_write(8'd02, 8'h1D, {8'h14});
    ext_dbi_write(8'd02, 8'h1C, {8'h00});
    ext_dbi_write(8'd02, 8'h1A, {8'h01});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h60, 8'h04});
    ext_dbi_write(8'd03, 8'h1D, {8'h12, 8'h34});
    ext_dbi_write(8'd02, 8'h1C, {8'h00});
    ext_dbi_write(8'd02, 8'h1A, {8'h02});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h60, 8'h12});
    ext_dbi_write(8'd04, 8'h1D, {8'h12, 8'h34, 8'h56});
    ext_dbi_write(8'd02, 8'h1C, {8'h00});
    ext_dbi_write(8'd02, 8'h1A, {8'h03});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h04, 8'h75, 8'h04});
    ext_dbi_write(8'd05, 8'h1D, {8'h12, 8'h34, 8'h56, 8'h78});
    ext_dbi_write(8'd02, 8'h1C, {8'h00});
    ext_dbi_write(8'd05, 8'h1B, {8'h00, 8'h07, 8'h60, 8'h12});
    ext_dbi_write(8'd05, 8'h1D, {8'h12, 8'h34, 8'h56/*REG_A*/, 8'h78/*REG_TMP0*/});
    ext_dbi_write(8'd02, 8'h1C, {8'h00});
    #2_000_000ns;
    #10ns; // Finish
end
endtask